# 🚀 Panduan Deployment WhatsApp Bot AI

Panduan lengkap untuk deploy WhatsApp Bot AI dengan frontend modern ke berbagai platform.

## 📋 Persiapan

### 1. Environment Variables
Pastikan Anda memiliki:
- **API_KEY**: Google Gemini API key dari [Google AI Studio](https://makersuite.google.com/app/apikey)
- **PORT**: Port untuk backend (default: 3000)
- **NODE_ENV**: Environment mode (production/development)
- **FRONTEND_URL**: URL frontend untuk CORS

### 2. Repository Setup
```bash
git clone https://github.com/vickyymosafan/whatsapp-ai.git
cd whatsapp-ai
```

## 🌐 Deployment Options

### Option 1: Railway (Full Stack - Recommended)

#### Backend + Frontend di Railway
1. **Fork repository** ke GitHub Anda
2. **Login ke Railway** dan connect repository
3. **Set Environment Variables:**
   ```
   API_KEY=your_gemini_api_key_here
   NODE_ENV=production
   FRONTEND_URL=https://your-app.railway.app
   ```
4. **Deploy otomatis** - Railway akan build dan deploy

#### Konfigurasi Railway
- **Build Command**: `npm run build`
- **Start Command**: `npm start`
- **Port**: Railway akan set otomatis

### Option 2: Vercel (Frontend) + Railway (Backend)

#### Deploy Backend ke Railway
1. Fork repository
2. Deploy backend ke Railway dengan env vars:
   ```
   API_KEY=your_gemini_api_key_here
   NODE_ENV=production
   FRONTEND_URL=https://your-frontend.vercel.app
   ```

#### Deploy Frontend ke Vercel
1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy frontend:**
   ```bash
   cd frontend
   vercel
   ```

3. **Set Environment Variables di Vercel:**
   ```
   NEXT_PUBLIC_API_URL=https://your-backend.railway.app
   ```

### Option 3: Netlify (Frontend) + Railway (Backend)

#### Deploy Frontend ke Netlify
1. **Build frontend:**
   ```bash
   cd frontend
   npm run build
   ```

2. **Upload folder `.next` ke Netlify**

3. **Set Environment Variables:**
   ```
   NEXT_PUBLIC_API_URL=https://your-backend.railway.app
   ```

## 🔧 Konfigurasi Production

### Backend (Railway)
```env
API_KEY=your_gemini_api_key_here
NODE_ENV=production
FRONTEND_URL=https://your-frontend-url.com
```

### Frontend (Vercel/Netlify)
```env
NEXT_PUBLIC_API_URL=https://your-backend.railway.app
```

## 📱 Testing Deployment

### 1. Backend Health Check
```bash
curl https://your-backend.railway.app/health
```

Response:
```json
{
  "status": "healthy",
  "uptime": 123.45,
  "memory": {...},
  "activeUsers": 0,
  "botStatus": "disconnected"
}
```

### 2. Frontend Access
- Buka `https://your-frontend-url.com`
- Pastikan QR scanner muncul
- Check console untuk WebSocket connection

### 3. WhatsApp Integration
1. Scan QR code yang muncul
2. Kirim pesan `/test` ke bot
3. Pastikan bot merespons dalam bahasa Indonesia

## 🔍 Troubleshooting

### Common Issues

#### 1. CORS Error
**Problem**: Frontend tidak bisa connect ke backend
**Solution**: 
- Pastikan `FRONTEND_URL` di backend sesuai dengan URL frontend
- Check CORS configuration di backend

#### 2. WebSocket Connection Failed
**Problem**: Real-time updates tidak bekerja
**Solution**:
- Pastikan `NEXT_PUBLIC_API_URL` di frontend benar
- Check firewall/proxy settings

#### 3. QR Code Tidak Muncul
**Problem**: QR code tidak generate
**Solution**:
- Check API key Gemini valid
- Pastikan WhatsApp Web.js dependencies terinstall
- Check logs untuk error puppeteer

#### 4. Bot Tidak Merespons
**Problem**: Bot tidak reply pesan
**Solution**:
- Pastikan pesan dimulai dengan "/"
- Check API key Gemini quota
- Verify WhatsApp connection status

### Logs Debugging

#### Backend Logs
```bash
# Railway
railway logs

# Local
npm run dev
```

#### Frontend Logs
```bash
# Vercel
vercel logs

# Local
cd frontend && npm run dev
```

## 📊 Monitoring

### Health Endpoints
- `GET /health` - Backend health check
- `GET /api/status` - Bot status dan QR code
- `GET /api/stats` - Statistics lengkap

### Performance Monitoring
- **Railway**: Built-in metrics
- **Vercel**: Analytics dashboard
- **Custom**: Implement logging sesuai kebutuhan

## 🔐 Security

### Environment Variables
- Jangan commit `.env` files
- Use platform-specific env var management
- Rotate API keys secara berkala

### CORS Configuration
- Set specific origins, jangan gunakan `*`
- Validate frontend URLs
- Use HTTPS di production

## 📈 Scaling

### Railway Scaling
- Auto-scaling berdasarkan traffic
- Monitor memory usage
- Upgrade plan jika diperlukan

### Frontend Optimization
- Enable Vercel/Netlify CDN
- Optimize images dan assets
- Use caching strategies

## 🆘 Support

Jika mengalami masalah deployment:
1. Check logs untuk error messages
2. Verify environment variables
3. Test API endpoints manually
4. Create issue di repository dengan detail error

---

**Happy Deploying! 🚀**
