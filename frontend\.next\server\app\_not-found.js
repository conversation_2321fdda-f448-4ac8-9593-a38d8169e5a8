(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6109:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=n(482),s=n(9108),o=n(2563),i=n.n(o),a=n(8300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);n.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,2917)),"D:\\Project\\wa\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}],c=[],u="/_not-found",p={require:n,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2601:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,6840,23)),Promise.resolve().then(n.t.bind(n,8771,23)),Promise.resolve().then(n.t.bind(n,3225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,3982,23))},2576:()=>{},2917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a,metadata:()=>i});var r=n(5036),s=n(265),o=n.n(s);n(7272);let i={title:"WhatsApp Bot AI - QR Scanner",description:"Professional WhatsApp Bot AI dengan interface modern untuk scan QR code",keywords:["whatsapp","bot","ai","qr","scanner"],authors:[{name:"WhatsApp Bot AI Team"}],viewport:"width=device-width, initial-scale=1"};function a({children:e}){return(0,r.jsxs)("html",{lang:"id",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("meta",{name:"theme-color",content:"#25D366"})]}),r.jsx("body",{className:o().className,children:r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",children:e})})]})}},7272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[869],()=>n(6109));module.exports=r})();