globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"1778":{"*":{"id":"9295","name":"*","chunks":[],"async":false}},"1902":{"*":{"id":"3225","name":"*","chunks":[],"async":false}},"5613":{"*":{"id":"8771","name":"*","chunks":[],"async":false}},"7690":{"*":{"id":"2583","name":"*","chunks":[],"async":false}},"7831":{"*":{"id":"3982","name":"*","chunks":[],"async":false}},"8777":{"*":{"id":"6322","name":"*","chunks":[],"async":false}},"8955":{"*":{"id":"6840","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":7690,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":8955,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":5613,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":1902,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1778,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":7831,"name":"*","chunks":[],"async":false},"D:\\Project\\wa\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":3445,"name":"*","chunks":["185","static/chunks/app/layout-85850e7cd89a65ea.js"],"async":false},"D:\\Project\\wa\\frontend\\app\\globals.css":{"id":2445,"name":"*","chunks":["185","static/chunks/app/layout-85850e7cd89a65ea.js"],"async":false},"D:\\Project\\wa\\frontend\\components\\QRScanner.tsx":{"id":8777,"name":"*","chunks":["853","static/chunks/853-54ce9866f3ab26dc.js","931","static/chunks/app/page-30698a7dbaaf3955.js"],"async":false}},"entryCSSFiles":{"D:\\Project\\wa\\frontend\\app\\_not-found":[],"D:\\Project\\wa\\frontend\\app\\layout":["static/css/675608dff574e363.css"],"D:\\Project\\wa\\frontend\\app\\page":[]}}