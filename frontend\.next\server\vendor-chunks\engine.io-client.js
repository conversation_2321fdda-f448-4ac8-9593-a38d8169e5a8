/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/engine.io-client";
exports.ids = ["vendor-chunks/engine.io-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/engine.io-client/node_modules/debug/src/browser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/engine.io-client/node_modules/debug/src/browser.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-env browser */ /**\n * This is the web browser implementation of `debug()`.\n */ exports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (()=>{\n    let warned = false;\n    return ()=>{\n        if (!warned) {\n            warned = true;\n            console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n        }\n    };\n})();\n/**\n * Colors.\n */ exports.colors = [\n    \"#0000CC\",\n    \"#0000FF\",\n    \"#0033CC\",\n    \"#0033FF\",\n    \"#0066CC\",\n    \"#0066FF\",\n    \"#0099CC\",\n    \"#0099FF\",\n    \"#00CC00\",\n    \"#00CC33\",\n    \"#00CC66\",\n    \"#00CC99\",\n    \"#00CCCC\",\n    \"#00CCFF\",\n    \"#3300CC\",\n    \"#3300FF\",\n    \"#3333CC\",\n    \"#3333FF\",\n    \"#3366CC\",\n    \"#3366FF\",\n    \"#3399CC\",\n    \"#3399FF\",\n    \"#33CC00\",\n    \"#33CC33\",\n    \"#33CC66\",\n    \"#33CC99\",\n    \"#33CCCC\",\n    \"#33CCFF\",\n    \"#6600CC\",\n    \"#6600FF\",\n    \"#6633CC\",\n    \"#6633FF\",\n    \"#66CC00\",\n    \"#66CC33\",\n    \"#9900CC\",\n    \"#9900FF\",\n    \"#9933CC\",\n    \"#9933FF\",\n    \"#99CC00\",\n    \"#99CC33\",\n    \"#CC0000\",\n    \"#CC0033\",\n    \"#CC0066\",\n    \"#CC0099\",\n    \"#CC00CC\",\n    \"#CC00FF\",\n    \"#CC3300\",\n    \"#CC3333\",\n    \"#CC3366\",\n    \"#CC3399\",\n    \"#CC33CC\",\n    \"#CC33FF\",\n    \"#CC6600\",\n    \"#CC6633\",\n    \"#CC9900\",\n    \"#CC9933\",\n    \"#CCCC00\",\n    \"#CCCC33\",\n    \"#FF0000\",\n    \"#FF0033\",\n    \"#FF0066\",\n    \"#FF0099\",\n    \"#FF00CC\",\n    \"#FF00FF\",\n    \"#FF3300\",\n    \"#FF3333\",\n    \"#FF3366\",\n    \"#FF3399\",\n    \"#FF33CC\",\n    \"#FF33FF\",\n    \"#FF6600\",\n    \"#FF6633\",\n    \"#FF9900\",\n    \"#FF9933\",\n    \"#FFCC00\",\n    \"#FFCC33\"\n];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */ // eslint-disable-next-line complexity\nfunction useColors() {\n    // NB: In an Electron preload script, document will be defined but not fully\n    // initialized. Since we know we're in Chrome, we'll just detect this case\n    // explicitly\n    if (false) {}\n    // Internet Explorer and Edge do not support colors.\n    if (typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n        return false;\n    }\n    let m;\n    // Is webkit? http://stackoverflow.com/a/16459606/376773\n    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n    return typeof document !== \"undefined\" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n     false && (0) || // Is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    typeof navigator !== \"undefined\" && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n    typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    args[0] = (this.useColors ? \"%c\" : \"\") + this.namespace + (this.useColors ? \" %c\" : \" \") + args[0] + (this.useColors ? \"%c \" : \" \") + \"+\" + module.exports.humanize(this.diff);\n    if (!this.useColors) {\n        return;\n    }\n    const c = \"color: \" + this.color;\n    args.splice(1, 0, c, \"color: inherit\");\n    // The final \"%c\" is somewhat tricky, because there could be other\n    // arguments passed either before or after the %c, so we need to\n    // figure out the correct index to insert the CSS into\n    let index = 0;\n    let lastC = 0;\n    args[0].replace(/%[a-zA-Z%]/g, (match)=>{\n        if (match === \"%%\") {\n            return;\n        }\n        index++;\n        if (match === \"%c\") {\n            // We only are interested in the *last* %c\n            // (the user may have provided their own)\n            lastC = index;\n        }\n    });\n    args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */ exports.log = console.debug || console.log || (()=>{});\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    try {\n        if (namespaces) {\n            exports.storage.setItem(\"debug\", namespaces);\n        } else {\n            exports.storage.removeItem(\"debug\");\n        }\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    let r;\n    try {\n        r = exports.storage.getItem(\"debug\");\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n    if (!r && typeof process !== \"undefined\" && \"env\" in process) {\n        r = process.env.DEBUG;\n    }\n    return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */ function localstorage() {\n    try {\n        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n        // The Browser also has localStorage in the global context.\n        return localStorage;\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */ formatters.j = function(v) {\n    try {\n        return JSON.stringify(v);\n    } catch (error) {\n        return \"[UnexpectedJSONParseError]: \" + error.message;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/node_modules/debug/src/common.js":
/*!************************************************************************!*\
  !*** ./node_modules/engine.io-client/node_modules/debug/src/common.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */ function setup(env) {\n    createDebug.debug = createDebug;\n    createDebug.default = createDebug;\n    createDebug.coerce = coerce;\n    createDebug.disable = disable;\n    createDebug.enable = enable;\n    createDebug.enabled = enabled;\n    createDebug.humanize = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n    createDebug.destroy = destroy;\n    Object.keys(env).forEach((key)=>{\n        createDebug[key] = env[key];\n    });\n    /**\n\t* The currently active debug mode names, and names to skip.\n\t*/ createDebug.names = [];\n    createDebug.skips = [];\n    /**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/ createDebug.formatters = {};\n    /**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/ function selectColor(namespace) {\n        let hash = 0;\n        for(let i = 0; i < namespace.length; i++){\n            hash = (hash << 5) - hash + namespace.charCodeAt(i);\n            hash |= 0; // Convert to 32bit integer\n        }\n        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n    }\n    createDebug.selectColor = selectColor;\n    /**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/ function createDebug(namespace) {\n        let prevTime;\n        let enableOverride = null;\n        let namespacesCache;\n        let enabledCache;\n        function debug(...args) {\n            // Disabled?\n            if (!debug.enabled) {\n                return;\n            }\n            const self = debug;\n            // Set `diff` timestamp\n            const curr = Number(new Date());\n            const ms = curr - (prevTime || curr);\n            self.diff = ms;\n            self.prev = prevTime;\n            self.curr = curr;\n            prevTime = curr;\n            args[0] = createDebug.coerce(args[0]);\n            if (typeof args[0] !== \"string\") {\n                // Anything else let's inspect with %O\n                args.unshift(\"%O\");\n            }\n            // Apply any `formatters` transformations\n            let index = 0;\n            args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format)=>{\n                // If we encounter an escaped % then don't increase the array index\n                if (match === \"%%\") {\n                    return \"%\";\n                }\n                index++;\n                const formatter = createDebug.formatters[format];\n                if (typeof formatter === \"function\") {\n                    const val = args[index];\n                    match = formatter.call(self, val);\n                    // Now we need to remove `args[index]` since it's inlined in the `format`\n                    args.splice(index, 1);\n                    index--;\n                }\n                return match;\n            });\n            // Apply env-specific formatting (colors, etc.)\n            createDebug.formatArgs.call(self, args);\n            const logFn = self.log || createDebug.log;\n            logFn.apply(self, args);\n        }\n        debug.namespace = namespace;\n        debug.useColors = createDebug.useColors();\n        debug.color = createDebug.selectColor(namespace);\n        debug.extend = extend;\n        debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n        Object.defineProperty(debug, \"enabled\", {\n            enumerable: true,\n            configurable: false,\n            get: ()=>{\n                if (enableOverride !== null) {\n                    return enableOverride;\n                }\n                if (namespacesCache !== createDebug.namespaces) {\n                    namespacesCache = createDebug.namespaces;\n                    enabledCache = createDebug.enabled(namespace);\n                }\n                return enabledCache;\n            },\n            set: (v)=>{\n                enableOverride = v;\n            }\n        });\n        // Env-specific initialization logic for debug instances\n        if (typeof createDebug.init === \"function\") {\n            createDebug.init(debug);\n        }\n        return debug;\n    }\n    function extend(namespace, delimiter) {\n        const newDebug = createDebug(this.namespace + (typeof delimiter === \"undefined\" ? \":\" : delimiter) + namespace);\n        newDebug.log = this.log;\n        return newDebug;\n    }\n    /**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/ function enable(namespaces) {\n        createDebug.save(namespaces);\n        createDebug.namespaces = namespaces;\n        createDebug.names = [];\n        createDebug.skips = [];\n        let i;\n        const split = (typeof namespaces === \"string\" ? namespaces : \"\").split(/[\\s,]+/);\n        const len = split.length;\n        for(i = 0; i < len; i++){\n            if (!split[i]) {\n                continue;\n            }\n            namespaces = split[i].replace(/\\*/g, \".*?\");\n            if (namespaces[0] === \"-\") {\n                createDebug.skips.push(new RegExp(\"^\" + namespaces.slice(1) + \"$\"));\n            } else {\n                createDebug.names.push(new RegExp(\"^\" + namespaces + \"$\"));\n            }\n        }\n    }\n    /**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/ function disable() {\n        const namespaces = [\n            ...createDebug.names.map(toNamespace),\n            ...createDebug.skips.map(toNamespace).map((namespace)=>\"-\" + namespace)\n        ].join(\",\");\n        createDebug.enable(\"\");\n        return namespaces;\n    }\n    /**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/ function enabled(name) {\n        if (name[name.length - 1] === \"*\") {\n            return true;\n        }\n        let i;\n        let len;\n        for(i = 0, len = createDebug.skips.length; i < len; i++){\n            if (createDebug.skips[i].test(name)) {\n                return false;\n            }\n        }\n        for(i = 0, len = createDebug.names.length; i < len; i++){\n            if (createDebug.names[i].test(name)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/ function toNamespace(regexp) {\n        return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, \"*\");\n    }\n    /**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/ function coerce(val) {\n        if (val instanceof Error) {\n            return val.stack || val.message;\n        }\n        return val;\n    }\n    /**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/ function destroy() {\n        console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n    }\n    createDebug.enable(createDebug.load());\n    return createDebug;\n}\nmodule.exports = setup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/engine.io-client/node_modules/debug/src/index.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */ if (typeof process === \"undefined\" || process.type === \"renderer\" || false === true || process.__nwjs) {\n    module.exports = __webpack_require__(/*! ./browser.js */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/browser.js\");\n} else {\n    module.exports = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUVELElBQUksT0FBT0EsWUFBWSxlQUFlQSxRQUFRQyxJQUFJLEtBQUssY0FBY0QsS0FBZSxLQUFLLFFBQVFBLFFBQVFHLE1BQU0sRUFBRTtJQUNoSEMsbUlBQXlCO0FBQzFCLE9BQU87SUFDTkEsNkhBQXlCO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2VuZ2luZS5pby1jbGllbnQvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9pbmRleC5qcz9hZmQyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGV0ZWN0IEVsZWN0cm9uIHJlbmRlcmVyIC8gbndqcyBwcm9jZXNzLCB3aGljaCBpcyBub2RlLCBidXQgd2Ugc2hvdWxkXG4gKiB0cmVhdCBhcyBhIGJyb3dzZXIuXG4gKi9cblxuaWYgKHR5cGVvZiBwcm9jZXNzID09PSAndW5kZWZpbmVkJyB8fCBwcm9jZXNzLnR5cGUgPT09ICdyZW5kZXJlcicgfHwgcHJvY2Vzcy5icm93c2VyID09PSB0cnVlIHx8IHByb2Nlc3MuX19ud2pzKSB7XG5cdG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9icm93c2VyLmpzJyk7XG59IGVsc2Uge1xuXHRtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbm9kZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJ0eXBlIiwiYnJvd3NlciIsIl9fbndqcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/node_modules/debug/src/node.js":
/*!**********************************************************************!*\
  !*** ./node_modules/engine.io-client/node_modules/debug/src/node.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */ const tty = __webpack_require__(/*! tty */ \"tty\");\nconst util = __webpack_require__(/*! util */ \"util\");\n/**\n * This is the Node.js implementation of `debug()`.\n */ exports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(()=>{}, \"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n/**\n * Colors.\n */ exports.colors = [\n    6,\n    2,\n    3,\n    4,\n    5,\n    1\n];\ntry {\n    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const supportsColor = __webpack_require__(/*! supports-color */ \"(ssr)/./node_modules/supports-color/index.js\");\n    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n        exports.colors = [\n            20,\n            21,\n            26,\n            27,\n            32,\n            33,\n            38,\n            39,\n            40,\n            41,\n            42,\n            43,\n            44,\n            45,\n            56,\n            57,\n            62,\n            63,\n            68,\n            69,\n            74,\n            75,\n            76,\n            77,\n            78,\n            79,\n            80,\n            81,\n            92,\n            93,\n            98,\n            99,\n            112,\n            113,\n            128,\n            129,\n            134,\n            135,\n            148,\n            149,\n            160,\n            161,\n            162,\n            163,\n            164,\n            165,\n            166,\n            167,\n            168,\n            169,\n            170,\n            171,\n            172,\n            173,\n            178,\n            179,\n            184,\n            185,\n            196,\n            197,\n            198,\n            199,\n            200,\n            201,\n            202,\n            203,\n            204,\n            205,\n            206,\n            207,\n            208,\n            209,\n            214,\n            215,\n            220,\n            221\n        ];\n    }\n} catch (error) {\n// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */ exports.inspectOpts = Object.keys(process.env).filter((key)=>{\n    return /^debug_/i.test(key);\n}).reduce((obj, key)=>{\n    // Camel-case\n    const prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, (_, k)=>{\n        return k.toUpperCase();\n    });\n    // Coerce string value into JS value\n    let val = process.env[key];\n    if (/^(yes|on|true|enabled)$/i.test(val)) {\n        val = true;\n    } else if (/^(no|off|false|disabled)$/i.test(val)) {\n        val = false;\n    } else if (val === \"null\") {\n        val = null;\n    } else {\n        val = Number(val);\n    }\n    obj[prop] = val;\n    return obj;\n}, {});\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */ function useColors() {\n    return \"colors\" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);\n}\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    const { namespace: name, useColors } = this;\n    if (useColors) {\n        const c = this.color;\n        const colorCode = \"\\x1b[3\" + (c < 8 ? c : \"8;5;\" + c);\n        const prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n        args[0] = prefix + args[0].split(\"\\n\").join(\"\\n\" + prefix);\n        args.push(colorCode + \"m+\" + module.exports.humanize(this.diff) + \"\\x1b[0m\");\n    } else {\n        args[0] = getDate() + name + \" \" + args[0];\n    }\n}\nfunction getDate() {\n    if (exports.inspectOpts.hideDate) {\n        return \"\";\n    }\n    return new Date().toISOString() + \" \";\n}\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */ function log(...args) {\n    return process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + \"\\n\");\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    if (namespaces) {\n        process.env.DEBUG = namespaces;\n    } else {\n        // If you set a process.env field to null or undefined, it gets cast to the\n        // string 'null' or 'undefined'. Just delete instead.\n        delete process.env.DEBUG;\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    return process.env.DEBUG;\n}\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */ function init(debug) {\n    debug.inspectOpts = {};\n    const keys = Object.keys(exports.inspectOpts);\n    for(let i = 0; i < keys.length; i++){\n        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */ formatters.o = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts).split(\"\\n\").map((str)=>str.trim()).join(\" \");\n};\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */ formatters.O = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasCORS: () => (/* binding */ hasCORS)\n/* harmony export */ });\n// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== \"undefined\" && \"withCredentials\" in new XMLHttpRequest();\n} catch (err) {\n// if XMLHttp support is disabled in IE then it will throw\n// when trying to create\n}\nconst hasCORS = value;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvY29udHJpYi9oYXMtY29ycy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsc0RBQXNEO0FBQ3RELElBQUlBLFFBQVE7QUFDWixJQUFJO0lBQ0FBLFFBQVEsT0FBT0MsbUJBQW1CLGVBQzlCLHFCQUFxQixJQUFJQTtBQUNqQyxFQUNBLE9BQU9DLEtBQUs7QUFDUiwwREFBMEQ7QUFDMUQsd0JBQXdCO0FBQzVCO0FBQ08sTUFBTUMsVUFBVUgsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9lbmdpbmUuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy9jb250cmliL2hhcy1jb3JzLmpzP2Y3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gaW1wb3J0ZWQgZnJvbSBodHRwczovL2dpdGh1Yi5jb20vY29tcG9uZW50L2hhcy1jb3JzXG5sZXQgdmFsdWUgPSBmYWxzZTtcbnRyeSB7XG4gICAgdmFsdWUgPSB0eXBlb2YgWE1MSHR0cFJlcXVlc3QgIT09ICd1bmRlZmluZWQnICYmXG4gICAgICAgICd3aXRoQ3JlZGVudGlhbHMnIGluIG5ldyBYTUxIdHRwUmVxdWVzdCgpO1xufVxuY2F0Y2ggKGVycikge1xuICAgIC8vIGlmIFhNTEh0dHAgc3VwcG9ydCBpcyBkaXNhYmxlZCBpbiBJRSB0aGVuIGl0IHdpbGwgdGhyb3dcbiAgICAvLyB3aGVuIHRyeWluZyB0byBjcmVhdGVcbn1cbmV4cG9ydCBjb25zdCBoYXNDT1JTID0gdmFsdWU7XG4iXSwibmFtZXMiOlsidmFsdWUiLCJYTUxIdHRwUmVxdWVzdCIsImVyciIsImhhc0NPUlMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js":
/*!**************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */ function encode(obj) {\n    let str = \"\";\n    for(let i in obj){\n        if (obj.hasOwnProperty(i)) {\n            if (str.length) str += \"&\";\n            str += encodeURIComponent(i) + \"=\" + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */ function decode(qs) {\n    let qry = {};\n    let pairs = qs.split(\"&\");\n    for(let i = 0, l = pairs.length; i < l; i++){\n        let pair = pairs[i].split(\"=\");\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\n * @api private\n */ const re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    \"source\",\n    \"protocol\",\n    \"authority\",\n    \"userInfo\",\n    \"user\",\n    \"password\",\n    \"host\",\n    \"port\",\n    \"relative\",\n    \"path\",\n    \"directory\",\n    \"file\",\n    \"query\",\n    \"anchor\"\n];\nfunction parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf(\"[\"), e = str.indexOf(\"]\");\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, \";\") + str.substring(e, str.length);\n    }\n    let m = re.exec(str || \"\"), uri = {}, i = 14;\n    while(i--){\n        uri[parts[i]] = m[i] || \"\";\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, \":\");\n        uri.authority = uri.authority.replace(\"[\", \"\").replace(\"]\", \"\").replace(/;/g, \":\");\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri[\"path\"]);\n    uri.queryKey = queryKey(uri, uri[\"query\"]);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == \"/\" || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == \"/\") {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js":
/*!***********************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/globals.node.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CookieJar: () => (/* binding */ CookieJar),\n/* harmony export */   createCookieJar: () => (/* binding */ createCookieJar),\n/* harmony export */   defaultBinaryType: () => (/* binding */ defaultBinaryType),\n/* harmony export */   globalThisShim: () => (/* binding */ globalThisShim),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\nconst nextTick = process.nextTick;\nconst globalThisShim = global;\nconst defaultBinaryType = \"nodebuffer\";\nfunction createCookieJar() {\n    return new CookieJar();\n}\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie\n */ function parse(setCookieString) {\n    const parts = setCookieString.split(\"; \");\n    const i = parts[0].indexOf(\"=\");\n    if (i === -1) {\n        return;\n    }\n    const name = parts[0].substring(0, i).trim();\n    if (!name.length) {\n        return;\n    }\n    let value = parts[0].substring(i + 1).trim();\n    if (value.charCodeAt(0) === 0x22) {\n        // remove double quotes\n        value = value.slice(1, -1);\n    }\n    const cookie = {\n        name,\n        value\n    };\n    for(let j = 1; j < parts.length; j++){\n        const subParts = parts[j].split(\"=\");\n        if (subParts.length !== 2) {\n            continue;\n        }\n        const key = subParts[0].trim();\n        const value = subParts[1].trim();\n        switch(key){\n            case \"Expires\":\n                cookie.expires = new Date(value);\n                break;\n            case \"Max-Age\":\n                const expiration = new Date();\n                expiration.setUTCSeconds(expiration.getUTCSeconds() + parseInt(value, 10));\n                cookie.expires = expiration;\n                break;\n            default:\n        }\n    }\n    return cookie;\n}\nclass CookieJar {\n    constructor(){\n        this._cookies = new Map();\n    }\n    parseCookies(values) {\n        if (!values) {\n            return;\n        }\n        values.forEach((value)=>{\n            const parsed = parse(value);\n            if (parsed) {\n                this._cookies.set(parsed.name, parsed);\n            }\n        });\n    }\n    get cookies() {\n        const now = Date.now();\n        this._cookies.forEach((cookie, name)=>{\n            var _a;\n            if (((_a = cookie.expires) === null || _a === void 0 ? void 0 : _a.getTime()) < now) {\n                this._cookies.delete(name);\n            }\n        });\n        return this._cookies.entries();\n    }\n    addCookies(xhr) {\n        const cookies = [];\n        for (const [name, cookie] of this.cookies){\n            cookies.push(`${name}=${cookie.value}`);\n        }\n        if (cookies.length) {\n            xhr.setDisableHeaderCheck(true);\n            xhr.setRequestHeader(\"cookie\", cookies.join(\"; \"));\n        }\n    }\n    appendCookies(headers) {\n        for (const [name, cookie] of this.cookies){\n            headers.append(\"cookie\", `${name}=${cookie.value}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* reexport safe */ _transports_polling_fetch_js__WEBPACK_IMPORTED_MODULE_6__.Fetch),\n/* harmony export */   NodeWebSocket: () => (/* reexport safe */ _transports_websocket_node_js__WEBPACK_IMPORTED_MODULE_9__.WS),\n/* harmony export */   NodeXHR: () => (/* reexport safe */ _transports_polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_7__.XHR),\n/* harmony export */   Socket: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.Socket),\n/* harmony export */   SocketWithUpgrade: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.SocketWithUpgrade),\n/* harmony export */   SocketWithoutUpgrade: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.SocketWithoutUpgrade),\n/* harmony export */   Transport: () => (/* reexport safe */ _transport_js__WEBPACK_IMPORTED_MODULE_1__.Transport),\n/* harmony export */   TransportError: () => (/* reexport safe */ _transport_js__WEBPACK_IMPORTED_MODULE_1__.TransportError),\n/* harmony export */   WebSocket: () => (/* reexport safe */ _transports_websocket_js__WEBPACK_IMPORTED_MODULE_10__.WS),\n/* harmony export */   WebTransport: () => (/* reexport safe */ _transports_webtransport_js__WEBPACK_IMPORTED_MODULE_11__.WT),\n/* harmony export */   XHR: () => (/* reexport safe */ _transports_polling_xhr_js__WEBPACK_IMPORTED_MODULE_8__.XHR),\n/* harmony export */   installTimerFunctions: () => (/* reexport safe */ _util_js__WEBPACK_IMPORTED_MODULE_3__.installTimerFunctions),\n/* harmony export */   nextTick: () => (/* reexport safe */ _globals_node_js__WEBPACK_IMPORTED_MODULE_5__.nextTick),\n/* harmony export */   parse: () => (/* reexport safe */ _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_4__.parse),\n/* harmony export */   protocol: () => (/* binding */ protocol),\n/* harmony export */   transports: () => (/* reexport safe */ _transports_index_js__WEBPACK_IMPORTED_MODULE_2__.transports)\n/* harmony export */ });\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _transports_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transports/index.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contrib/parseuri.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var _transports_polling_fetch_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./transports/polling-fetch.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js\");\n/* harmony import */ var _transports_polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./transports/polling-xhr.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\");\n/* harmony import */ var _transports_polling_xhr_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./transports/polling-xhr.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\");\n/* harmony import */ var _transports_websocket_node_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./transports/websocket.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\");\n/* harmony import */ var _transports_websocket_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./transports/websocket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\");\n/* harmony import */ var _transports_webtransport_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./transports/webtransport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\");\n\n\n\nconst protocol = _socket_js__WEBPACK_IMPORTED_MODULE_0__.Socket.protocol;\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNuQjtBQUNxRDtBQUNoRSxNQUFNRyxXQUFXSCw4Q0FBTUEsQ0FBQ0csUUFBUSxDQUFDO0FBQ21CO0FBQ1I7QUFDRDtBQUNKO0FBQ0Q7QUFDUztBQUNZO0FBQ2hCO0FBQ21CO0FBQ1Q7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9lbmdpbmUuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy9pbmRleC5qcz84YTQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNvY2tldCB9IGZyb20gXCIuL3NvY2tldC5qc1wiO1xuZXhwb3J0IHsgU29ja2V0IH07XG5leHBvcnQgeyBTb2NrZXRXaXRob3V0VXBncmFkZSwgU29ja2V0V2l0aFVwZ3JhZGUsIH0gZnJvbSBcIi4vc29ja2V0LmpzXCI7XG5leHBvcnQgY29uc3QgcHJvdG9jb2wgPSBTb2NrZXQucHJvdG9jb2w7XG5leHBvcnQgeyBUcmFuc3BvcnQsIFRyYW5zcG9ydEVycm9yIH0gZnJvbSBcIi4vdHJhbnNwb3J0LmpzXCI7XG5leHBvcnQgeyB0cmFuc3BvcnRzIH0gZnJvbSBcIi4vdHJhbnNwb3J0cy9pbmRleC5qc1wiO1xuZXhwb3J0IHsgaW5zdGFsbFRpbWVyRnVuY3Rpb25zIH0gZnJvbSBcIi4vdXRpbC5qc1wiO1xuZXhwb3J0IHsgcGFyc2UgfSBmcm9tIFwiLi9jb250cmliL3BhcnNldXJpLmpzXCI7XG5leHBvcnQgeyBuZXh0VGljayB9IGZyb20gXCIuL2dsb2JhbHMubm9kZS5qc1wiO1xuZXhwb3J0IHsgRmV0Y2ggfSBmcm9tIFwiLi90cmFuc3BvcnRzL3BvbGxpbmctZmV0Y2guanNcIjtcbmV4cG9ydCB7IFhIUiBhcyBOb2RlWEhSIH0gZnJvbSBcIi4vdHJhbnNwb3J0cy9wb2xsaW5nLXhoci5ub2RlLmpzXCI7XG5leHBvcnQgeyBYSFIgfSBmcm9tIFwiLi90cmFuc3BvcnRzL3BvbGxpbmcteGhyLmpzXCI7XG5leHBvcnQgeyBXUyBhcyBOb2RlV2ViU29ja2V0IH0gZnJvbSBcIi4vdHJhbnNwb3J0cy93ZWJzb2NrZXQubm9kZS5qc1wiO1xuZXhwb3J0IHsgV1MgYXMgV2ViU29ja2V0IH0gZnJvbSBcIi4vdHJhbnNwb3J0cy93ZWJzb2NrZXQuanNcIjtcbmV4cG9ydCB7IFdUIGFzIFdlYlRyYW5zcG9ydCB9IGZyb20gXCIuL3RyYW5zcG9ydHMvd2VidHJhbnNwb3J0LmpzXCI7XG4iXSwibmFtZXMiOlsiU29ja2V0IiwiU29ja2V0V2l0aG91dFVwZ3JhZGUiLCJTb2NrZXRXaXRoVXBncmFkZSIsInByb3RvY29sIiwiVHJhbnNwb3J0IiwiVHJhbnNwb3J0RXJyb3IiLCJ0cmFuc3BvcnRzIiwiaW5zdGFsbFRpbWVyRnVuY3Rpb25zIiwicGFyc2UiLCJuZXh0VGljayIsIkZldGNoIiwiWEhSIiwiTm9kZVhIUiIsIldTIiwiTm9kZVdlYlNvY2tldCIsIldlYlNvY2tldCIsIldUIiwiV2ViVHJhbnNwb3J0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/socket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Socket: () => (/* binding */ Socket),\n/* harmony export */   SocketWithUpgrade: () => (/* binding */ SocketWithUpgrade),\n/* harmony export */   SocketWithoutUpgrade: () => (/* binding */ SocketWithoutUpgrade)\n/* harmony export */ });\n/* harmony import */ var _transports_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transports/index.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./contrib/parseqs.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\");\n/* harmony import */ var _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contrib/parseuri.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_7__(\"engine.io-client:socket\"); // debug()\nconst withEventListeners = typeof addEventListener === \"function\" && typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", ()=>{\n        debug(\"closing %d connection(s) because the network was lost\", OFFLINE_EVENT_LISTENERS.length);\n        OFFLINE_EVENT_LISTENERS.forEach((listener)=>listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */ class SocketWithoutUpgrade extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_4__.Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */ constructor(uri, opts){\n        super();\n        this.binaryType = _globals_node_js__WEBPACK_IMPORTED_MODULE_6__.defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */ this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = (0,_contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__.parse)(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure = parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query) opts.query = parsedUri.query;\n        } else if (opts.host) {\n            opts.hostname = (0,_contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__.parse)(opts.host).host;\n        }\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.installTimerFunctions)(this, opts);\n        this.secure = null != opts.secure ? opts.secure : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname = opts.hostname || (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port = opts.port || (typeof location !== \"undefined\" && location.port ? location.port : this.secure ? \"443\" : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t)=>{\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = (0,_contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_2__.decode)(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = ()=>{\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                debug(\"adding listener for the 'offline' event\");\n                this._offlineEventListener = ()=>{\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_6__.createCookieJar)();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */ createTransport(name) {\n        debug('creating transport \"%s\"', name);\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = engine_io_parser__WEBPACK_IMPORTED_MODULE_5__.protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id) query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        }, this.opts.transportOptions[name]);\n        debug(\"options: %j\", opts);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */ _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(()=>{\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade && SocketWithoutUpgrade.priorWebsocketSuccess && this.transports.indexOf(\"websocket\") !== -1 ? \"websocket\" : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */ setTransport(transport) {\n        debug(\"setting transport %s\", transport.name);\n        if (this.transport) {\n            debug(\"clearing existing transport %s\", this.transport.name);\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport.on(\"drain\", this._onDrain.bind(this)).on(\"packet\", this._onPacket.bind(this)).on(\"error\", this._onError.bind(this)).on(\"close\", (reason)=>this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */ onOpen() {\n        debug(\"socket open\");\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */ _onPacket(packet) {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n            debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch(packet.type){\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        } else {\n            debug('packet received with socket readyState \"%s\"', this.readyState);\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */ onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState) return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */ _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(()=>{\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */ _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        } else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */ flush() {\n        if (\"closed\" !== this.readyState && this.transport.writable && !this.upgrading && this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            debug(\"flushing %d packets in socket\", packets.length);\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */ _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload && this.transport.name === \"polling\" && this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for(let i = 0; i < this.writeBuffer.length; i++){\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.byteLength)(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                debug(\"only send %d out of %d packets\", i, this.writeBuffer.length);\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        debug(\"payload size is %d (max: %d)\", payloadSize, this._maxPayload);\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */ /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime) return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            debug(\"throttled timer detected, scheduling connection close\");\n            this._pingTimeoutTime = 0;\n            (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_6__.nextTick)(()=>{\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */ write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */ send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */ _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn) this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */ close() {\n        const close = ()=>{\n            this._onClose(\"forced close\");\n            debug(\"socket closing - telling transport to close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = ()=>{\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = ()=>{\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", ()=>{\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    } else {\n                        close();\n                    }\n                });\n            } else if (this.upgrading) {\n                waitForUpgrade();\n            } else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */ _onError(err) {\n        debug(\"socket error %j\", err);\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports && this.transports.length > 1 && this.readyState === \"opening\") {\n            debug(\"trying next transport\");\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */ _onClose(reason, description) {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n            debug('socket close with reason: \"%s\"', reason);\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        debug(\"removing listener for the 'offline' event\");\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = engine_io_parser__WEBPACK_IMPORTED_MODULE_5__.protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */ class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor(){\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            debug(\"starting upgrade probes\");\n            for(let i = 0; i < this._upgrades.length; i++){\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */ _probe(name) {\n        debug('probing transport \"%s\"', name);\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = ()=>{\n            if (failed) return;\n            debug('probe transport \"%s\" opened', name);\n            transport.send([\n                {\n                    type: \"ping\",\n                    data: \"probe\"\n                }\n            ]);\n            transport.once(\"packet\", (msg)=>{\n                if (failed) return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    debug('probe transport \"%s\" pong', name);\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport) return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    debug('pausing current transport \"%s\"', this.transport.name);\n                    this.transport.pause(()=>{\n                        if (failed) return;\n                        if (\"closed\" === this.readyState) return;\n                        debug(\"changing transport and sending upgrade packet\");\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([\n                            {\n                                type: \"upgrade\"\n                            }\n                        ]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                } else {\n                    debug('probe transport \"%s\" failed', name);\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed) return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err)=>{\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            debug('probe transport \"%s\" failed because of error: %s', name, err);\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = ()=>{\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 && name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(()=>{\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        } else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */ _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for(let i = 0; i < upgrades.length; i++){\n            if (~this.transports.indexOf(upgrades[i])) filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */ class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}){\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports || o.transports && typeof o.transports[0] === \"string\") {\n            o.transports = (o.transports || [\n                \"polling\",\n                \"websocket\",\n                \"webtransport\"\n            ]).map((transportName)=>_transports_index_js__WEBPACK_IMPORTED_MODULE_0__.transports[transportName]).filter((t)=>!!t);\n        }\n        super(uri, o);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvc29ja2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQXlFO0FBQ1g7QUFDaEI7QUFDQTtBQUNTO0FBQ1g7QUFDc0M7QUFDbEQsQ0FBQyxVQUFVO0FBQzNDLE1BQU1ZLFFBQVFELGtDQUFXQSxDQUFDLDRCQUE0QixVQUFVO0FBQ2hFLE1BQU1FLHFCQUFxQixPQUFPQyxxQkFBcUIsY0FDbkQsT0FBT0Msd0JBQXdCO0FBQ25DLE1BQU1DLDBCQUEwQixFQUFFO0FBQ2xDLElBQUlILG9CQUFvQjtJQUNwQixtSEFBbUg7SUFDbkgsMkdBQTJHO0lBQzNHQyxpQkFBaUIsV0FBVztRQUN4QkYsTUFBTSx5REFBeURJLHdCQUF3QkMsTUFBTTtRQUM3RkQsd0JBQXdCRSxPQUFPLENBQUMsQ0FBQ0MsV0FBYUE7SUFDbEQsR0FBRztBQUNQO0FBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FzQkMsR0FDTSxNQUFNQyw2QkFBNkJkLGlFQUFPQTtJQUM3Qzs7Ozs7S0FLQyxHQUNEZSxZQUFZQyxHQUFHLEVBQUVDLElBQUksQ0FBRTtRQUNuQixLQUFLO1FBQ0wsSUFBSSxDQUFDQyxVQUFVLEdBQUdmLCtEQUFpQkE7UUFDbkMsSUFBSSxDQUFDZ0IsV0FBVyxHQUFHLEVBQUU7UUFDckIsSUFBSSxDQUFDQyxjQUFjLEdBQUc7UUFDdEIsSUFBSSxDQUFDQyxhQUFhLEdBQUcsQ0FBQztRQUN0QixJQUFJLENBQUNDLFlBQVksR0FBRyxDQUFDO1FBQ3JCLElBQUksQ0FBQ0MsV0FBVyxHQUFHLENBQUM7UUFDcEI7OztTQUdDLEdBQ0QsSUFBSSxDQUFDQyxnQkFBZ0IsR0FBR0M7UUFDeEIsSUFBSVQsT0FBTyxhQUFhLE9BQU9BLEtBQUs7WUFDaENDLE9BQU9EO1lBQ1BBLE1BQU07UUFDVjtRQUNBLElBQUlBLEtBQUs7WUFDTCxNQUFNVSxZQUFZM0IsMkRBQUtBLENBQUNpQjtZQUN4QkMsS0FBS1UsUUFBUSxHQUFHRCxVQUFVRSxJQUFJO1lBQzlCWCxLQUFLWSxNQUFNLEdBQ1BILFVBQVV6QixRQUFRLEtBQUssV0FBV3lCLFVBQVV6QixRQUFRLEtBQUs7WUFDN0RnQixLQUFLYSxJQUFJLEdBQUdKLFVBQVVJLElBQUk7WUFDMUIsSUFBSUosVUFBVUssS0FBSyxFQUNmZCxLQUFLYyxLQUFLLEdBQUdMLFVBQVVLLEtBQUs7UUFDcEMsT0FDSyxJQUFJZCxLQUFLVyxJQUFJLEVBQUU7WUFDaEJYLEtBQUtVLFFBQVEsR0FBRzVCLDJEQUFLQSxDQUFDa0IsS0FBS1csSUFBSSxFQUFFQSxJQUFJO1FBQ3pDO1FBQ0FoQywrREFBcUJBLENBQUMsSUFBSSxFQUFFcUI7UUFDNUIsSUFBSSxDQUFDWSxNQUFNLEdBQ1AsUUFBUVosS0FBS1ksTUFBTSxHQUNiWixLQUFLWSxNQUFNLEdBQ1gsT0FBT0csYUFBYSxlQUFlLGFBQWFBLFNBQVMvQixRQUFRO1FBQzNFLElBQUlnQixLQUFLVSxRQUFRLElBQUksQ0FBQ1YsS0FBS2EsSUFBSSxFQUFFO1lBQzdCLDZEQUE2RDtZQUM3RGIsS0FBS2EsSUFBSSxHQUFHLElBQUksQ0FBQ0QsTUFBTSxHQUFHLFFBQVE7UUFDdEM7UUFDQSxJQUFJLENBQUNGLFFBQVEsR0FDVFYsS0FBS1UsUUFBUSxJQUNSLFFBQU9LLGFBQWEsY0FBY0EsU0FBU0wsUUFBUSxHQUFHLFdBQVU7UUFDekUsSUFBSSxDQUFDRyxJQUFJLEdBQ0xiLEtBQUthLElBQUksSUFDSixRQUFPRSxhQUFhLGVBQWVBLFNBQVNGLElBQUksR0FDM0NFLFNBQVNGLElBQUksR0FDYixJQUFJLENBQUNELE1BQU0sR0FDUCxRQUNBLElBQUc7UUFDckIsSUFBSSxDQUFDbkMsVUFBVSxHQUFHLEVBQUU7UUFDcEIsSUFBSSxDQUFDdUMsaUJBQWlCLEdBQUcsQ0FBQztRQUMxQmhCLEtBQUt2QixVQUFVLENBQUNrQixPQUFPLENBQUMsQ0FBQ3NCO1lBQ3JCLE1BQU1DLGdCQUFnQkQsRUFBRUUsU0FBUyxDQUFDQyxJQUFJO1lBQ3RDLElBQUksQ0FBQzNDLFVBQVUsQ0FBQzRDLElBQUksQ0FBQ0g7WUFDckIsSUFBSSxDQUFDRixpQkFBaUIsQ0FBQ0UsY0FBYyxHQUFHRDtRQUM1QztRQUNBLElBQUksQ0FBQ2pCLElBQUksR0FBR3NCLE9BQU9DLE1BQU0sQ0FBQztZQUN0QkMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLGlCQUFpQjtZQUNqQkMsU0FBUztZQUNUQyxnQkFBZ0I7WUFDaEJDLGlCQUFpQjtZQUNqQkMsa0JBQWtCO1lBQ2xCQyxvQkFBb0I7WUFDcEJDLG1CQUFtQjtnQkFDZkMsV0FBVztZQUNmO1lBQ0FDLGtCQUFrQixDQUFDO1lBQ25CQyxxQkFBcUI7UUFDekIsR0FBR25DO1FBQ0gsSUFBSSxDQUFDQSxJQUFJLENBQUN3QixJQUFJLEdBQ1YsSUFBSSxDQUFDeEIsSUFBSSxDQUFDd0IsSUFBSSxDQUFDWSxPQUFPLENBQUMsT0FBTyxNQUN6QixLQUFJLENBQUNwQyxJQUFJLENBQUM4QixnQkFBZ0IsR0FBRyxNQUFNLEVBQUM7UUFDN0MsSUFBSSxPQUFPLElBQUksQ0FBQzlCLElBQUksQ0FBQ2MsS0FBSyxLQUFLLFVBQVU7WUFDckMsSUFBSSxDQUFDZCxJQUFJLENBQUNjLEtBQUssR0FBR2pDLDJEQUFNQSxDQUFDLElBQUksQ0FBQ21CLElBQUksQ0FBQ2MsS0FBSztRQUM1QztRQUNBLElBQUl4QixvQkFBb0I7WUFDcEIsSUFBSSxJQUFJLENBQUNVLElBQUksQ0FBQ21DLG1CQUFtQixFQUFFO2dCQUMvQiw2R0FBNkc7Z0JBQzdHLHdHQUF3RztnQkFDeEcsbUJBQW1CO2dCQUNuQixJQUFJLENBQUNFLDBCQUEwQixHQUFHO29CQUM5QixJQUFJLElBQUksQ0FBQ0MsU0FBUyxFQUFFO3dCQUNoQiwrQkFBK0I7d0JBQy9CLElBQUksQ0FBQ0EsU0FBUyxDQUFDQyxrQkFBa0I7d0JBQ2pDLElBQUksQ0FBQ0QsU0FBUyxDQUFDRSxLQUFLO29CQUN4QjtnQkFDSjtnQkFDQWpELGlCQUFpQixnQkFBZ0IsSUFBSSxDQUFDOEMsMEJBQTBCLEVBQUU7WUFDdEU7WUFDQSxJQUFJLElBQUksQ0FBQzNCLFFBQVEsS0FBSyxhQUFhO2dCQUMvQnJCLE1BQU07Z0JBQ04sSUFBSSxDQUFDb0QscUJBQXFCLEdBQUc7b0JBQ3pCLElBQUksQ0FBQ0MsUUFBUSxDQUFDLG1CQUFtQjt3QkFDN0JDLGFBQWE7b0JBQ2pCO2dCQUNKO2dCQUNBbEQsd0JBQXdCNEIsSUFBSSxDQUFDLElBQUksQ0FBQ29CLHFCQUFxQjtZQUMzRDtRQUNKO1FBQ0EsSUFBSSxJQUFJLENBQUN6QyxJQUFJLENBQUMwQixlQUFlLEVBQUU7WUFDM0IsSUFBSSxDQUFDa0IsVUFBVSxHQUFHM0QsaUVBQWVBO1FBQ3JDO1FBQ0EsSUFBSSxDQUFDNEQsS0FBSztJQUNkO0lBQ0E7Ozs7OztLQU1DLEdBQ0RDLGdCQUFnQjFCLElBQUksRUFBRTtRQUNsQi9CLE1BQU0sMkJBQTJCK0I7UUFDakMsTUFBTU4sUUFBUVEsT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUN2QixJQUFJLENBQUNjLEtBQUs7UUFDL0MsdUNBQXVDO1FBQ3ZDQSxNQUFNaUMsR0FBRyxHQUFHL0Qsc0RBQVFBO1FBQ3BCLGlCQUFpQjtRQUNqQjhCLE1BQU13QixTQUFTLEdBQUdsQjtRQUNsQixvQ0FBb0M7UUFDcEMsSUFBSSxJQUFJLENBQUM0QixFQUFFLEVBQ1BsQyxNQUFNbUMsR0FBRyxHQUFHLElBQUksQ0FBQ0QsRUFBRTtRQUN2QixNQUFNaEQsT0FBT3NCLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDdkIsSUFBSSxFQUFFO1lBQ3RDYztZQUNBb0MsUUFBUSxJQUFJO1lBQ1p4QyxVQUFVLElBQUksQ0FBQ0EsUUFBUTtZQUN2QkUsUUFBUSxJQUFJLENBQUNBLE1BQU07WUFDbkJDLE1BQU0sSUFBSSxDQUFDQSxJQUFJO1FBQ25CLEdBQUcsSUFBSSxDQUFDYixJQUFJLENBQUNrQyxnQkFBZ0IsQ0FBQ2QsS0FBSztRQUNuQy9CLE1BQU0sZUFBZVc7UUFDckIsT0FBTyxJQUFJLElBQUksQ0FBQ2dCLGlCQUFpQixDQUFDSSxLQUFLLENBQUNwQjtJQUM1QztJQUNBOzs7O0tBSUMsR0FDRDZDLFFBQVE7UUFDSixJQUFJLElBQUksQ0FBQ3BFLFVBQVUsQ0FBQ2lCLE1BQU0sS0FBSyxHQUFHO1lBQzlCLG1EQUFtRDtZQUNuRCxJQUFJLENBQUN5RCxZQUFZLENBQUM7Z0JBQ2QsSUFBSSxDQUFDQyxZQUFZLENBQUMsU0FBUztZQUMvQixHQUFHO1lBQ0g7UUFDSjtRQUNBLE1BQU1sQyxnQkFBZ0IsSUFBSSxDQUFDbEIsSUFBSSxDQUFDNkIsZUFBZSxJQUMzQ2hDLHFCQUFxQndELHFCQUFxQixJQUMxQyxJQUFJLENBQUM1RSxVQUFVLENBQUM2RSxPQUFPLENBQUMsaUJBQWlCLENBQUMsSUFDeEMsY0FDQSxJQUFJLENBQUM3RSxVQUFVLENBQUMsRUFBRTtRQUN4QixJQUFJLENBQUM4RSxVQUFVLEdBQUc7UUFDbEIsTUFBTWpCLFlBQVksSUFBSSxDQUFDUSxlQUFlLENBQUM1QjtRQUN2Q29CLFVBQVVrQixJQUFJO1FBQ2QsSUFBSSxDQUFDQyxZQUFZLENBQUNuQjtJQUN0QjtJQUNBOzs7O0tBSUMsR0FDRG1CLGFBQWFuQixTQUFTLEVBQUU7UUFDcEJqRCxNQUFNLHdCQUF3QmlELFVBQVVsQixJQUFJO1FBQzVDLElBQUksSUFBSSxDQUFDa0IsU0FBUyxFQUFFO1lBQ2hCakQsTUFBTSxrQ0FBa0MsSUFBSSxDQUFDaUQsU0FBUyxDQUFDbEIsSUFBSTtZQUMzRCxJQUFJLENBQUNrQixTQUFTLENBQUNDLGtCQUFrQjtRQUNyQztRQUNBLG1CQUFtQjtRQUNuQixJQUFJLENBQUNELFNBQVMsR0FBR0E7UUFDakIsNkJBQTZCO1FBQzdCQSxVQUNLb0IsRUFBRSxDQUFDLFNBQVMsSUFBSSxDQUFDQyxRQUFRLENBQUNDLElBQUksQ0FBQyxJQUFJLEdBQ25DRixFQUFFLENBQUMsVUFBVSxJQUFJLENBQUNHLFNBQVMsQ0FBQ0QsSUFBSSxDQUFDLElBQUksR0FDckNGLEVBQUUsQ0FBQyxTQUFTLElBQUksQ0FBQ0ksUUFBUSxDQUFDRixJQUFJLENBQUMsSUFBSSxHQUNuQ0YsRUFBRSxDQUFDLFNBQVMsQ0FBQ0ssU0FBVyxJQUFJLENBQUNyQixRQUFRLENBQUMsbUJBQW1CcUI7SUFDbEU7SUFDQTs7OztLQUlDLEdBQ0RDLFNBQVM7UUFDTDNFLE1BQU07UUFDTixJQUFJLENBQUNrRSxVQUFVLEdBQUc7UUFDbEIxRCxxQkFBcUJ3RCxxQkFBcUIsR0FDdEMsZ0JBQWdCLElBQUksQ0FBQ2YsU0FBUyxDQUFDbEIsSUFBSTtRQUN2QyxJQUFJLENBQUNnQyxZQUFZLENBQUM7UUFDbEIsSUFBSSxDQUFDYSxLQUFLO0lBQ2Q7SUFDQTs7OztLQUlDLEdBQ0RKLFVBQVVLLE1BQU0sRUFBRTtRQUNkLElBQUksY0FBYyxJQUFJLENBQUNYLFVBQVUsSUFDN0IsV0FBVyxJQUFJLENBQUNBLFVBQVUsSUFDMUIsY0FBYyxJQUFJLENBQUNBLFVBQVUsRUFBRTtZQUMvQmxFLE1BQU0sd0NBQXdDNkUsT0FBT0MsSUFBSSxFQUFFRCxPQUFPRSxJQUFJO1lBQ3RFLElBQUksQ0FBQ2hCLFlBQVksQ0FBQyxVQUFVYztZQUM1QixxQ0FBcUM7WUFDckMsSUFBSSxDQUFDZCxZQUFZLENBQUM7WUFDbEIsT0FBUWMsT0FBT0MsSUFBSTtnQkFDZixLQUFLO29CQUNELElBQUksQ0FBQ0UsV0FBVyxDQUFDQyxLQUFLeEYsS0FBSyxDQUFDb0YsT0FBT0UsSUFBSTtvQkFDdkM7Z0JBQ0osS0FBSztvQkFDRCxJQUFJLENBQUNHLFdBQVcsQ0FBQztvQkFDakIsSUFBSSxDQUFDbkIsWUFBWSxDQUFDO29CQUNsQixJQUFJLENBQUNBLFlBQVksQ0FBQztvQkFDbEIsSUFBSSxDQUFDb0IsaUJBQWlCO29CQUN0QjtnQkFDSixLQUFLO29CQUNELE1BQU1DLE1BQU0sSUFBSUMsTUFBTTtvQkFDdEIsYUFBYTtvQkFDYkQsSUFBSUUsSUFBSSxHQUFHVCxPQUFPRSxJQUFJO29CQUN0QixJQUFJLENBQUNOLFFBQVEsQ0FBQ1c7b0JBQ2Q7Z0JBQ0osS0FBSztvQkFDRCxJQUFJLENBQUNyQixZQUFZLENBQUMsUUFBUWMsT0FBT0UsSUFBSTtvQkFDckMsSUFBSSxDQUFDaEIsWUFBWSxDQUFDLFdBQVdjLE9BQU9FLElBQUk7b0JBQ3hDO1lBQ1I7UUFDSixPQUNLO1lBQ0QvRSxNQUFNLCtDQUErQyxJQUFJLENBQUNrRSxVQUFVO1FBQ3hFO0lBQ0o7SUFDQTs7Ozs7S0FLQyxHQUNEYyxZQUFZRCxJQUFJLEVBQUU7UUFDZCxJQUFJLENBQUNoQixZQUFZLENBQUMsYUFBYWdCO1FBQy9CLElBQUksQ0FBQ3BCLEVBQUUsR0FBR29CLEtBQUtuQixHQUFHO1FBQ2xCLElBQUksQ0FBQ1gsU0FBUyxDQUFDeEIsS0FBSyxDQUFDbUMsR0FBRyxHQUFHbUIsS0FBS25CLEdBQUc7UUFDbkMsSUFBSSxDQUFDN0MsYUFBYSxHQUFHZ0UsS0FBS1EsWUFBWTtRQUN0QyxJQUFJLENBQUN2RSxZQUFZLEdBQUcrRCxLQUFLUyxXQUFXO1FBQ3BDLElBQUksQ0FBQ3ZFLFdBQVcsR0FBRzhELEtBQUtVLFVBQVU7UUFDbEMsSUFBSSxDQUFDZCxNQUFNO1FBQ1gscUNBQXFDO1FBQ3JDLElBQUksYUFBYSxJQUFJLENBQUNULFVBQVUsRUFDNUI7UUFDSixJQUFJLENBQUNpQixpQkFBaUI7SUFDMUI7SUFDQTs7OztLQUlDLEdBQ0RBLG9CQUFvQjtRQUNoQixJQUFJLENBQUNPLGNBQWMsQ0FBQyxJQUFJLENBQUNDLGlCQUFpQjtRQUMxQyxNQUFNQyxRQUFRLElBQUksQ0FBQzdFLGFBQWEsR0FBRyxJQUFJLENBQUNDLFlBQVk7UUFDcEQsSUFBSSxDQUFDRSxnQkFBZ0IsR0FBRzJFLEtBQUtDLEdBQUcsS0FBS0Y7UUFDckMsSUFBSSxDQUFDRCxpQkFBaUIsR0FBRyxJQUFJLENBQUM3QixZQUFZLENBQUM7WUFDdkMsSUFBSSxDQUFDVCxRQUFRLENBQUM7UUFDbEIsR0FBR3VDO1FBQ0gsSUFBSSxJQUFJLENBQUNqRixJQUFJLENBQUNvRixTQUFTLEVBQUU7WUFDckIsSUFBSSxDQUFDSixpQkFBaUIsQ0FBQ0ssS0FBSztRQUNoQztJQUNKO0lBQ0E7Ozs7S0FJQyxHQUNEMUIsV0FBVztRQUNQLElBQUksQ0FBQ3pELFdBQVcsQ0FBQ29GLE1BQU0sQ0FBQyxHQUFHLElBQUksQ0FBQ25GLGNBQWM7UUFDOUMsOENBQThDO1FBQzlDLDREQUE0RDtRQUM1RCw4REFBOEQ7UUFDOUQsSUFBSSxDQUFDQSxjQUFjLEdBQUc7UUFDdEIsSUFBSSxNQUFNLElBQUksQ0FBQ0QsV0FBVyxDQUFDUixNQUFNLEVBQUU7WUFDL0IsSUFBSSxDQUFDMEQsWUFBWSxDQUFDO1FBQ3RCLE9BQ0s7WUFDRCxJQUFJLENBQUNhLEtBQUs7UUFDZDtJQUNKO0lBQ0E7Ozs7S0FJQyxHQUNEQSxRQUFRO1FBQ0osSUFBSSxhQUFhLElBQUksQ0FBQ1YsVUFBVSxJQUM1QixJQUFJLENBQUNqQixTQUFTLENBQUNpRCxRQUFRLElBQ3ZCLENBQUMsSUFBSSxDQUFDQyxTQUFTLElBQ2YsSUFBSSxDQUFDdEYsV0FBVyxDQUFDUixNQUFNLEVBQUU7WUFDekIsTUFBTStGLFVBQVUsSUFBSSxDQUFDQyxtQkFBbUI7WUFDeENyRyxNQUFNLGlDQUFpQ29HLFFBQVEvRixNQUFNO1lBQ3JELElBQUksQ0FBQzRDLFNBQVMsQ0FBQ3FELElBQUksQ0FBQ0Y7WUFDcEIsOENBQThDO1lBQzlDLG1EQUFtRDtZQUNuRCxJQUFJLENBQUN0RixjQUFjLEdBQUdzRixRQUFRL0YsTUFBTTtZQUNwQyxJQUFJLENBQUMwRCxZQUFZLENBQUM7UUFDdEI7SUFDSjtJQUNBOzs7OztLQUtDLEdBQ0RzQyxzQkFBc0I7UUFDbEIsTUFBTUUseUJBQXlCLElBQUksQ0FBQ3RGLFdBQVcsSUFDM0MsSUFBSSxDQUFDZ0MsU0FBUyxDQUFDbEIsSUFBSSxLQUFLLGFBQ3hCLElBQUksQ0FBQ2xCLFdBQVcsQ0FBQ1IsTUFBTSxHQUFHO1FBQzlCLElBQUksQ0FBQ2tHLHdCQUF3QjtZQUN6QixPQUFPLElBQUksQ0FBQzFGLFdBQVc7UUFDM0I7UUFDQSxJQUFJMkYsY0FBYyxHQUFHLG9CQUFvQjtRQUN6QyxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSSxJQUFJLENBQUM1RixXQUFXLENBQUNSLE1BQU0sRUFBRW9HLElBQUs7WUFDOUMsTUFBTTFCLE9BQU8sSUFBSSxDQUFDbEUsV0FBVyxDQUFDNEYsRUFBRSxDQUFDMUIsSUFBSTtZQUNyQyxJQUFJQSxNQUFNO2dCQUNOeUIsZUFBZWpILG9EQUFVQSxDQUFDd0Y7WUFDOUI7WUFDQSxJQUFJMEIsSUFBSSxLQUFLRCxjQUFjLElBQUksQ0FBQ3ZGLFdBQVcsRUFBRTtnQkFDekNqQixNQUFNLGtDQUFrQ3lHLEdBQUcsSUFBSSxDQUFDNUYsV0FBVyxDQUFDUixNQUFNO2dCQUNsRSxPQUFPLElBQUksQ0FBQ1EsV0FBVyxDQUFDNkYsS0FBSyxDQUFDLEdBQUdEO1lBQ3JDO1lBQ0FELGVBQWUsR0FBRywwQkFBMEI7UUFDaEQ7UUFDQXhHLE1BQU0sZ0NBQWdDd0csYUFBYSxJQUFJLENBQUN2RixXQUFXO1FBQ25FLE9BQU8sSUFBSSxDQUFDSixXQUFXO0lBQzNCO0lBQ0E7Ozs7Ozs7O0tBUUMsR0FDRCxXQUFXLEdBQUc4RixrQkFBa0I7UUFDNUIsSUFBSSxDQUFDLElBQUksQ0FBQ3pGLGdCQUFnQixFQUN0QixPQUFPO1FBQ1gsTUFBTTBGLGFBQWFmLEtBQUtDLEdBQUcsS0FBSyxJQUFJLENBQUM1RSxnQkFBZ0I7UUFDckQsSUFBSTBGLFlBQVk7WUFDWjVHLE1BQU07WUFDTixJQUFJLENBQUNrQixnQkFBZ0IsR0FBRztZQUN4QnBCLDBEQUFRQSxDQUFDO2dCQUNMLElBQUksQ0FBQ3VELFFBQVEsQ0FBQztZQUNsQixHQUFHLElBQUksQ0FBQ1MsWUFBWTtRQUN4QjtRQUNBLE9BQU84QztJQUNYO0lBQ0E7Ozs7Ozs7S0FPQyxHQUNEQyxNQUFNQyxHQUFHLEVBQUVDLE9BQU8sRUFBRUMsRUFBRSxFQUFFO1FBQ3BCLElBQUksQ0FBQzlCLFdBQVcsQ0FBQyxXQUFXNEIsS0FBS0MsU0FBU0M7UUFDMUMsT0FBTyxJQUFJO0lBQ2Y7SUFDQTs7Ozs7OztLQU9DLEdBQ0RWLEtBQUtRLEdBQUcsRUFBRUMsT0FBTyxFQUFFQyxFQUFFLEVBQUU7UUFDbkIsSUFBSSxDQUFDOUIsV0FBVyxDQUFDLFdBQVc0QixLQUFLQyxTQUFTQztRQUMxQyxPQUFPLElBQUk7SUFDZjtJQUNBOzs7Ozs7OztLQVFDLEdBQ0Q5QixZQUFZSixJQUFJLEVBQUVDLElBQUksRUFBRWdDLE9BQU8sRUFBRUMsRUFBRSxFQUFFO1FBQ2pDLElBQUksZUFBZSxPQUFPakMsTUFBTTtZQUM1QmlDLEtBQUtqQztZQUNMQSxPQUFPa0M7UUFDWDtRQUNBLElBQUksZUFBZSxPQUFPRixTQUFTO1lBQy9CQyxLQUFLRDtZQUNMQSxVQUFVO1FBQ2Q7UUFDQSxJQUFJLGNBQWMsSUFBSSxDQUFDN0MsVUFBVSxJQUFJLGFBQWEsSUFBSSxDQUFDQSxVQUFVLEVBQUU7WUFDL0Q7UUFDSjtRQUNBNkMsVUFBVUEsV0FBVyxDQUFDO1FBQ3RCQSxRQUFRRyxRQUFRLEdBQUcsVUFBVUgsUUFBUUcsUUFBUTtRQUM3QyxNQUFNckMsU0FBUztZQUNYQyxNQUFNQTtZQUNOQyxNQUFNQTtZQUNOZ0MsU0FBU0E7UUFDYjtRQUNBLElBQUksQ0FBQ2hELFlBQVksQ0FBQyxnQkFBZ0JjO1FBQ2xDLElBQUksQ0FBQ2hFLFdBQVcsQ0FBQ21CLElBQUksQ0FBQzZDO1FBQ3RCLElBQUltQyxJQUNBLElBQUksQ0FBQ0csSUFBSSxDQUFDLFNBQVNIO1FBQ3ZCLElBQUksQ0FBQ3BDLEtBQUs7SUFDZDtJQUNBOztLQUVDLEdBQ0R6QixRQUFRO1FBQ0osTUFBTUEsUUFBUTtZQUNWLElBQUksQ0FBQ0UsUUFBUSxDQUFDO1lBQ2RyRCxNQUFNO1lBQ04sSUFBSSxDQUFDaUQsU0FBUyxDQUFDRSxLQUFLO1FBQ3hCO1FBQ0EsTUFBTWlFLGtCQUFrQjtZQUNwQixJQUFJLENBQUNDLEdBQUcsQ0FBQyxXQUFXRDtZQUNwQixJQUFJLENBQUNDLEdBQUcsQ0FBQyxnQkFBZ0JEO1lBQ3pCakU7UUFDSjtRQUNBLE1BQU1tRSxpQkFBaUI7WUFDbkIsbUZBQW1GO1lBQ25GLElBQUksQ0FBQ0gsSUFBSSxDQUFDLFdBQVdDO1lBQ3JCLElBQUksQ0FBQ0QsSUFBSSxDQUFDLGdCQUFnQkM7UUFDOUI7UUFDQSxJQUFJLGNBQWMsSUFBSSxDQUFDbEQsVUFBVSxJQUFJLFdBQVcsSUFBSSxDQUFDQSxVQUFVLEVBQUU7WUFDN0QsSUFBSSxDQUFDQSxVQUFVLEdBQUc7WUFDbEIsSUFBSSxJQUFJLENBQUNyRCxXQUFXLENBQUNSLE1BQU0sRUFBRTtnQkFDekIsSUFBSSxDQUFDOEcsSUFBSSxDQUFDLFNBQVM7b0JBQ2YsSUFBSSxJQUFJLENBQUNoQixTQUFTLEVBQUU7d0JBQ2hCbUI7b0JBQ0osT0FDSzt3QkFDRG5FO29CQUNKO2dCQUNKO1lBQ0osT0FDSyxJQUFJLElBQUksQ0FBQ2dELFNBQVMsRUFBRTtnQkFDckJtQjtZQUNKLE9BQ0s7Z0JBQ0RuRTtZQUNKO1FBQ0o7UUFDQSxPQUFPLElBQUk7SUFDZjtJQUNBOzs7O0tBSUMsR0FDRHNCLFNBQVNXLEdBQUcsRUFBRTtRQUNWcEYsTUFBTSxtQkFBbUJvRjtRQUN6QjVFLHFCQUFxQndELHFCQUFxQixHQUFHO1FBQzdDLElBQUksSUFBSSxDQUFDckQsSUFBSSxDQUFDNEcsZ0JBQWdCLElBQzFCLElBQUksQ0FBQ25JLFVBQVUsQ0FBQ2lCLE1BQU0sR0FBRyxLQUN6QixJQUFJLENBQUM2RCxVQUFVLEtBQUssV0FBVztZQUMvQmxFLE1BQU07WUFDTixJQUFJLENBQUNaLFVBQVUsQ0FBQ29JLEtBQUs7WUFDckIsT0FBTyxJQUFJLENBQUNoRSxLQUFLO1FBQ3JCO1FBQ0EsSUFBSSxDQUFDTyxZQUFZLENBQUMsU0FBU3FCO1FBQzNCLElBQUksQ0FBQy9CLFFBQVEsQ0FBQyxtQkFBbUIrQjtJQUNyQztJQUNBOzs7O0tBSUMsR0FDRC9CLFNBQVNxQixNQUFNLEVBQUVwQixXQUFXLEVBQUU7UUFDMUIsSUFBSSxjQUFjLElBQUksQ0FBQ1ksVUFBVSxJQUM3QixXQUFXLElBQUksQ0FBQ0EsVUFBVSxJQUMxQixjQUFjLElBQUksQ0FBQ0EsVUFBVSxFQUFFO1lBQy9CbEUsTUFBTSxrQ0FBa0MwRTtZQUN4QyxlQUFlO1lBQ2YsSUFBSSxDQUFDZ0IsY0FBYyxDQUFDLElBQUksQ0FBQ0MsaUJBQWlCO1lBQzFDLDZDQUE2QztZQUM3QyxJQUFJLENBQUMxQyxTQUFTLENBQUNDLGtCQUFrQixDQUFDO1lBQ2xDLG1DQUFtQztZQUNuQyxJQUFJLENBQUNELFNBQVMsQ0FBQ0UsS0FBSztZQUNwQix5Q0FBeUM7WUFDekMsSUFBSSxDQUFDRixTQUFTLENBQUNDLGtCQUFrQjtZQUNqQyxJQUFJakQsb0JBQW9CO2dCQUNwQixJQUFJLElBQUksQ0FBQytDLDBCQUEwQixFQUFFO29CQUNqQzdDLG9CQUFvQixnQkFBZ0IsSUFBSSxDQUFDNkMsMEJBQTBCLEVBQUU7Z0JBQ3pFO2dCQUNBLElBQUksSUFBSSxDQUFDSSxxQkFBcUIsRUFBRTtvQkFDNUIsTUFBTXFELElBQUlyRyx3QkFBd0I2RCxPQUFPLENBQUMsSUFBSSxDQUFDYixxQkFBcUI7b0JBQ3BFLElBQUlxRCxNQUFNLENBQUMsR0FBRzt3QkFDVnpHLE1BQU07d0JBQ05JLHdCQUF3QjZGLE1BQU0sQ0FBQ1EsR0FBRztvQkFDdEM7Z0JBQ0o7WUFDSjtZQUNBLGtCQUFrQjtZQUNsQixJQUFJLENBQUN2QyxVQUFVLEdBQUc7WUFDbEIsbUJBQW1CO1lBQ25CLElBQUksQ0FBQ1AsRUFBRSxHQUFHO1lBQ1YsbUJBQW1CO1lBQ25CLElBQUksQ0FBQ0ksWUFBWSxDQUFDLFNBQVNXLFFBQVFwQjtZQUNuQywwQ0FBMEM7WUFDMUMsb0NBQW9DO1lBQ3BDLElBQUksQ0FBQ3pDLFdBQVcsR0FBRyxFQUFFO1lBQ3JCLElBQUksQ0FBQ0MsY0FBYyxHQUFHO1FBQzFCO0lBQ0o7QUFDSjtBQUNBTixxQkFBcUJiLFFBQVEsR0FBR0Esc0RBQVFBO0FBQ3hDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBc0JDLEdBQ00sTUFBTThILDBCQUEwQmpIO0lBQ25DQyxhQUFjO1FBQ1YsS0FBSyxJQUFJaUg7UUFDVCxJQUFJLENBQUNDLFNBQVMsR0FBRyxFQUFFO0lBQ3ZCO0lBQ0FoRCxTQUFTO1FBQ0wsS0FBSyxDQUFDQTtRQUNOLElBQUksV0FBVyxJQUFJLENBQUNULFVBQVUsSUFBSSxJQUFJLENBQUN2RCxJQUFJLENBQUMyQixPQUFPLEVBQUU7WUFDakR0QyxNQUFNO1lBQ04sSUFBSyxJQUFJeUcsSUFBSSxHQUFHQSxJQUFJLElBQUksQ0FBQ2tCLFNBQVMsQ0FBQ3RILE1BQU0sRUFBRW9HLElBQUs7Z0JBQzVDLElBQUksQ0FBQ21CLE1BQU0sQ0FBQyxJQUFJLENBQUNELFNBQVMsQ0FBQ2xCLEVBQUU7WUFDakM7UUFDSjtJQUNKO0lBQ0E7Ozs7O0tBS0MsR0FDRG1CLE9BQU83RixJQUFJLEVBQUU7UUFDVC9CLE1BQU0sMEJBQTBCK0I7UUFDaEMsSUFBSWtCLFlBQVksSUFBSSxDQUFDUSxlQUFlLENBQUMxQjtRQUNyQyxJQUFJOEYsU0FBUztRQUNickgscUJBQXFCd0QscUJBQXFCLEdBQUc7UUFDN0MsTUFBTThELGtCQUFrQjtZQUNwQixJQUFJRCxRQUNBO1lBQ0o3SCxNQUFNLCtCQUErQitCO1lBQ3JDa0IsVUFBVXFELElBQUksQ0FBQztnQkFBQztvQkFBRXhCLE1BQU07b0JBQVFDLE1BQU07Z0JBQVE7YUFBRTtZQUNoRDlCLFVBQVVrRSxJQUFJLENBQUMsVUFBVSxDQUFDTDtnQkFDdEIsSUFBSWUsUUFDQTtnQkFDSixJQUFJLFdBQVdmLElBQUloQyxJQUFJLElBQUksWUFBWWdDLElBQUkvQixJQUFJLEVBQUU7b0JBQzdDL0UsTUFBTSw2QkFBNkIrQjtvQkFDbkMsSUFBSSxDQUFDb0UsU0FBUyxHQUFHO29CQUNqQixJQUFJLENBQUNwQyxZQUFZLENBQUMsYUFBYWQ7b0JBQy9CLElBQUksQ0FBQ0EsV0FDRDtvQkFDSnpDLHFCQUFxQndELHFCQUFxQixHQUN0QyxnQkFBZ0JmLFVBQVVsQixJQUFJO29CQUNsQy9CLE1BQU0sa0NBQWtDLElBQUksQ0FBQ2lELFNBQVMsQ0FBQ2xCLElBQUk7b0JBQzNELElBQUksQ0FBQ2tCLFNBQVMsQ0FBQzhFLEtBQUssQ0FBQzt3QkFDakIsSUFBSUYsUUFDQTt3QkFDSixJQUFJLGFBQWEsSUFBSSxDQUFDM0QsVUFBVSxFQUM1Qjt3QkFDSmxFLE1BQU07d0JBQ05nSTt3QkFDQSxJQUFJLENBQUM1RCxZQUFZLENBQUNuQjt3QkFDbEJBLFVBQVVxRCxJQUFJLENBQUM7NEJBQUM7Z0NBQUV4QixNQUFNOzRCQUFVO3lCQUFFO3dCQUNwQyxJQUFJLENBQUNmLFlBQVksQ0FBQyxXQUFXZDt3QkFDN0JBLFlBQVk7d0JBQ1osSUFBSSxDQUFDa0QsU0FBUyxHQUFHO3dCQUNqQixJQUFJLENBQUN2QixLQUFLO29CQUNkO2dCQUNKLE9BQ0s7b0JBQ0Q1RSxNQUFNLCtCQUErQitCO29CQUNyQyxNQUFNcUQsTUFBTSxJQUFJQyxNQUFNO29CQUN0QixhQUFhO29CQUNiRCxJQUFJbkMsU0FBUyxHQUFHQSxVQUFVbEIsSUFBSTtvQkFDOUIsSUFBSSxDQUFDZ0MsWUFBWSxDQUFDLGdCQUFnQnFCO2dCQUN0QztZQUNKO1FBQ0o7UUFDQSxTQUFTNkM7WUFDTCxJQUFJSixRQUNBO1lBQ0osK0RBQStEO1lBQy9EQSxTQUFTO1lBQ1RHO1lBQ0EvRSxVQUFVRSxLQUFLO1lBQ2ZGLFlBQVk7UUFDaEI7UUFDQSw4Q0FBOEM7UUFDOUMsTUFBTWlGLFVBQVUsQ0FBQzlDO1lBQ2IsTUFBTStDLFFBQVEsSUFBSTlDLE1BQU0sa0JBQWtCRDtZQUMxQyxhQUFhO1lBQ2IrQyxNQUFNbEYsU0FBUyxHQUFHQSxVQUFVbEIsSUFBSTtZQUNoQ2tHO1lBQ0FqSSxNQUFNLG9EQUFvRCtCLE1BQU1xRDtZQUNoRSxJQUFJLENBQUNyQixZQUFZLENBQUMsZ0JBQWdCb0U7UUFDdEM7UUFDQSxTQUFTQztZQUNMRixRQUFRO1FBQ1o7UUFDQSxnREFBZ0Q7UUFDaEQsU0FBU0c7WUFDTEgsUUFBUTtRQUNaO1FBQ0Esa0RBQWtEO1FBQ2xELFNBQVNJLFVBQVVDLEVBQUU7WUFDakIsSUFBSXRGLGFBQWFzRixHQUFHeEcsSUFBSSxLQUFLa0IsVUFBVWxCLElBQUksRUFBRTtnQkFDekMvQixNQUFNLDhCQUE4QnVJLEdBQUd4RyxJQUFJLEVBQUVrQixVQUFVbEIsSUFBSTtnQkFDM0RrRztZQUNKO1FBQ0o7UUFDQSxvREFBb0Q7UUFDcEQsTUFBTUQsVUFBVTtZQUNaL0UsVUFBVXVGLGNBQWMsQ0FBQyxRQUFRVjtZQUNqQzdFLFVBQVV1RixjQUFjLENBQUMsU0FBU047WUFDbENqRixVQUFVdUYsY0FBYyxDQUFDLFNBQVNKO1lBQ2xDLElBQUksQ0FBQ2YsR0FBRyxDQUFDLFNBQVNnQjtZQUNsQixJQUFJLENBQUNoQixHQUFHLENBQUMsYUFBYWlCO1FBQzFCO1FBQ0FyRixVQUFVa0UsSUFBSSxDQUFDLFFBQVFXO1FBQ3ZCN0UsVUFBVWtFLElBQUksQ0FBQyxTQUFTZTtRQUN4QmpGLFVBQVVrRSxJQUFJLENBQUMsU0FBU2lCO1FBQ3hCLElBQUksQ0FBQ2pCLElBQUksQ0FBQyxTQUFTa0I7UUFDbkIsSUFBSSxDQUFDbEIsSUFBSSxDQUFDLGFBQWFtQjtRQUN2QixJQUFJLElBQUksQ0FBQ1gsU0FBUyxDQUFDMUQsT0FBTyxDQUFDLG9CQUFvQixDQUFDLEtBQzVDbEMsU0FBUyxnQkFBZ0I7WUFDekIscUJBQXFCO1lBQ3JCLElBQUksQ0FBQytCLFlBQVksQ0FBQztnQkFDZCxJQUFJLENBQUMrRCxRQUFRO29CQUNUNUUsVUFBVWtCLElBQUk7Z0JBQ2xCO1lBQ0osR0FBRztRQUNQLE9BQ0s7WUFDRGxCLFVBQVVrQixJQUFJO1FBQ2xCO0lBQ0o7SUFDQWEsWUFBWUQsSUFBSSxFQUFFO1FBQ2QsSUFBSSxDQUFDNEMsU0FBUyxHQUFHLElBQUksQ0FBQ2MsZUFBZSxDQUFDMUQsS0FBSzJELFFBQVE7UUFDbkQsS0FBSyxDQUFDMUQsWUFBWUQ7SUFDdEI7SUFDQTs7Ozs7S0FLQyxHQUNEMEQsZ0JBQWdCQyxRQUFRLEVBQUU7UUFDdEIsTUFBTUMsbUJBQW1CLEVBQUU7UUFDM0IsSUFBSyxJQUFJbEMsSUFBSSxHQUFHQSxJQUFJaUMsU0FBU3JJLE1BQU0sRUFBRW9HLElBQUs7WUFDdEMsSUFBSSxDQUFDLElBQUksQ0FBQ3JILFVBQVUsQ0FBQzZFLE9BQU8sQ0FBQ3lFLFFBQVEsQ0FBQ2pDLEVBQUUsR0FDcENrQyxpQkFBaUIzRyxJQUFJLENBQUMwRyxRQUFRLENBQUNqQyxFQUFFO1FBQ3pDO1FBQ0EsT0FBT2tDO0lBQ1g7QUFDSjtBQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FrQkMsR0FDTSxNQUFNQyxlQUFlbkI7SUFDeEJoSCxZQUFZQyxHQUFHLEVBQUVDLE9BQU8sQ0FBQyxDQUFDLENBQUU7UUFDeEIsTUFBTWtJLElBQUksT0FBT25JLFFBQVEsV0FBV0EsTUFBTUM7UUFDMUMsSUFBSSxDQUFDa0ksRUFBRXpKLFVBQVUsSUFDWnlKLEVBQUV6SixVQUFVLElBQUksT0FBT3lKLEVBQUV6SixVQUFVLENBQUMsRUFBRSxLQUFLLFVBQVc7WUFDdkR5SixFQUFFekosVUFBVSxHQUFHLENBQUN5SixFQUFFekosVUFBVSxJQUFJO2dCQUFDO2dCQUFXO2dCQUFhO2FBQWUsRUFDbkUwSixHQUFHLENBQUMsQ0FBQ2pILGdCQUFrQnhDLDREQUFrQixDQUFDd0MsY0FBYyxFQUN4RGtILE1BQU0sQ0FBQyxDQUFDbkgsSUFBTSxDQUFDLENBQUNBO1FBQ3pCO1FBQ0EsS0FBSyxDQUFDbEIsS0FBS21JO0lBQ2Y7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9lbmdpbmUuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy9zb2NrZXQuanM/NmZhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0cmFuc3BvcnRzIGFzIERFRkFVTFRfVFJBTlNQT1JUUyB9IGZyb20gXCIuL3RyYW5zcG9ydHMvaW5kZXguanNcIjtcbmltcG9ydCB7IGluc3RhbGxUaW1lckZ1bmN0aW9ucywgYnl0ZUxlbmd0aCB9IGZyb20gXCIuL3V0aWwuanNcIjtcbmltcG9ydCB7IGRlY29kZSB9IGZyb20gXCIuL2NvbnRyaWIvcGFyc2Vxcy5qc1wiO1xuaW1wb3J0IHsgcGFyc2UgfSBmcm9tIFwiLi9jb250cmliL3BhcnNldXJpLmpzXCI7XG5pbXBvcnQgeyBFbWl0dGVyIH0gZnJvbSBcIkBzb2NrZXQuaW8vY29tcG9uZW50LWVtaXR0ZXJcIjtcbmltcG9ydCB7IHByb3RvY29sIH0gZnJvbSBcImVuZ2luZS5pby1wYXJzZXJcIjtcbmltcG9ydCB7IGNyZWF0ZUNvb2tpZUphciwgZGVmYXVsdEJpbmFyeVR5cGUsIG5leHRUaWNrLCB9IGZyb20gXCIuL2dsb2JhbHMubm9kZS5qc1wiO1xuaW1wb3J0IGRlYnVnTW9kdWxlIGZyb20gXCJkZWJ1Z1wiOyAvLyBkZWJ1ZygpXG5jb25zdCBkZWJ1ZyA9IGRlYnVnTW9kdWxlKFwiZW5naW5lLmlvLWNsaWVudDpzb2NrZXRcIik7IC8vIGRlYnVnKClcbmNvbnN0IHdpdGhFdmVudExpc3RlbmVycyA9IHR5cGVvZiBhZGRFdmVudExpc3RlbmVyID09PSBcImZ1bmN0aW9uXCIgJiZcbiAgICB0eXBlb2YgcmVtb3ZlRXZlbnRMaXN0ZW5lciA9PT0gXCJmdW5jdGlvblwiO1xuY29uc3QgT0ZGTElORV9FVkVOVF9MSVNURU5FUlMgPSBbXTtcbmlmICh3aXRoRXZlbnRMaXN0ZW5lcnMpIHtcbiAgICAvLyB3aXRoaW4gYSBTZXJ2aWNlV29ya2VyLCBhbnkgZXZlbnQgaGFuZGxlciBmb3IgdGhlICdvZmZsaW5lJyBldmVudCBtdXN0IGJlIGFkZGVkIG9uIHRoZSBpbml0aWFsIGV2YWx1YXRpb24gb2YgdGhlXG4gICAgLy8gc2NyaXB0LCBzbyB3ZSBjcmVhdGUgb25lIHNpbmdsZSBldmVudCBsaXN0ZW5lciBoZXJlIHdoaWNoIHdpbGwgZm9yd2FyZCB0aGUgZXZlbnQgdG8gdGhlIHNvY2tldCBpbnN0YW5jZXNcbiAgICBhZGRFdmVudExpc3RlbmVyKFwib2ZmbGluZVwiLCAoKSA9PiB7XG4gICAgICAgIGRlYnVnKFwiY2xvc2luZyAlZCBjb25uZWN0aW9uKHMpIGJlY2F1c2UgdGhlIG5ldHdvcmsgd2FzIGxvc3RcIiwgT0ZGTElORV9FVkVOVF9MSVNURU5FUlMubGVuZ3RoKTtcbiAgICAgICAgT0ZGTElORV9FVkVOVF9MSVNURU5FUlMuZm9yRWFjaCgobGlzdGVuZXIpID0+IGxpc3RlbmVyKCkpO1xuICAgIH0sIGZhbHNlKTtcbn1cbi8qKlxuICogVGhpcyBjbGFzcyBwcm92aWRlcyBhIFdlYlNvY2tldC1saWtlIGludGVyZmFjZSB0byBjb25uZWN0IHRvIGFuIEVuZ2luZS5JTyBzZXJ2ZXIuIFRoZSBjb25uZWN0aW9uIHdpbGwgYmUgZXN0YWJsaXNoZWRcbiAqIHdpdGggb25lIG9mIHRoZSBhdmFpbGFibGUgbG93LWxldmVsIHRyYW5zcG9ydHMsIGxpa2UgSFRUUCBsb25nLXBvbGxpbmcsIFdlYlNvY2tldCBvciBXZWJUcmFuc3BvcnQuXG4gKlxuICogVGhpcyBjbGFzcyBjb21lcyB3aXRob3V0IHVwZ3JhZGUgbWVjaGFuaXNtLCB3aGljaCBtZWFucyB0aGF0IGl0IHdpbGwga2VlcCB0aGUgZmlyc3QgbG93LWxldmVsIHRyYW5zcG9ydCB0aGF0XG4gKiBzdWNjZXNzZnVsbHkgZXN0YWJsaXNoZXMgdGhlIGNvbm5lY3Rpb24uXG4gKlxuICogSW4gb3JkZXIgdG8gYWxsb3cgdHJlZS1zaGFraW5nLCB0aGVyZSBhcmUgbm8gdHJhbnNwb3J0cyBpbmNsdWRlZCwgdGhhdCdzIHdoeSB0aGUgYHRyYW5zcG9ydHNgIG9wdGlvbiBpcyBtYW5kYXRvcnkuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IFNvY2tldFdpdGhvdXRVcGdyYWRlLCBXZWJTb2NrZXQgfSBmcm9tIFwiZW5naW5lLmlvLWNsaWVudFwiO1xuICpcbiAqIGNvbnN0IHNvY2tldCA9IG5ldyBTb2NrZXRXaXRob3V0VXBncmFkZSh7XG4gKiAgIHRyYW5zcG9ydHM6IFtXZWJTb2NrZXRdXG4gKiB9KTtcbiAqXG4gKiBzb2NrZXQub24oXCJvcGVuXCIsICgpID0+IHtcbiAqICAgc29ja2V0LnNlbmQoXCJoZWxsb1wiKTtcbiAqIH0pO1xuICpcbiAqIEBzZWUgU29ja2V0V2l0aFVwZ3JhZGVcbiAqIEBzZWUgU29ja2V0XG4gKi9cbmV4cG9ydCBjbGFzcyBTb2NrZXRXaXRob3V0VXBncmFkZSBleHRlbmRzIEVtaXR0ZXIge1xuICAgIC8qKlxuICAgICAqIFNvY2tldCBjb25zdHJ1Y3Rvci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7U3RyaW5nfE9iamVjdH0gdXJpIC0gdXJpIG9yIG9wdGlvbnNcbiAgICAgKiBAcGFyYW0ge09iamVjdH0gb3B0cyAtIG9wdGlvbnNcbiAgICAgKi9cbiAgICBjb25zdHJ1Y3Rvcih1cmksIG9wdHMpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5iaW5hcnlUeXBlID0gZGVmYXVsdEJpbmFyeVR5cGU7XG4gICAgICAgIHRoaXMud3JpdGVCdWZmZXIgPSBbXTtcbiAgICAgICAgdGhpcy5fcHJldkJ1ZmZlckxlbiA9IDA7XG4gICAgICAgIHRoaXMuX3BpbmdJbnRlcnZhbCA9IC0xO1xuICAgICAgICB0aGlzLl9waW5nVGltZW91dCA9IC0xO1xuICAgICAgICB0aGlzLl9tYXhQYXlsb2FkID0gLTE7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBUaGUgZXhwaXJhdGlvbiB0aW1lc3RhbXAgb2YgdGhlIHtAbGluayBfcGluZ1RpbWVvdXRUaW1lcn0gb2JqZWN0IGlzIHRyYWNrZWQsIGluIGNhc2UgdGhlIHRpbWVyIGlzIHRocm90dGxlZCBhbmQgdGhlXG4gICAgICAgICAqIGNhbGxiYWNrIGlzIG5vdCBmaXJlZCBvbiB0aW1lLiBUaGlzIGNhbiBoYXBwZW4gZm9yIGV4YW1wbGUgd2hlbiBhIGxhcHRvcCBpcyBzdXNwZW5kZWQgb3Igd2hlbiBhIHBob25lIGlzIGxvY2tlZC5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuX3BpbmdUaW1lb3V0VGltZSA9IEluZmluaXR5O1xuICAgICAgICBpZiAodXJpICYmIFwib2JqZWN0XCIgPT09IHR5cGVvZiB1cmkpIHtcbiAgICAgICAgICAgIG9wdHMgPSB1cmk7XG4gICAgICAgICAgICB1cmkgPSBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh1cmkpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZFVyaSA9IHBhcnNlKHVyaSk7XG4gICAgICAgICAgICBvcHRzLmhvc3RuYW1lID0gcGFyc2VkVXJpLmhvc3Q7XG4gICAgICAgICAgICBvcHRzLnNlY3VyZSA9XG4gICAgICAgICAgICAgICAgcGFyc2VkVXJpLnByb3RvY29sID09PSBcImh0dHBzXCIgfHwgcGFyc2VkVXJpLnByb3RvY29sID09PSBcIndzc1wiO1xuICAgICAgICAgICAgb3B0cy5wb3J0ID0gcGFyc2VkVXJpLnBvcnQ7XG4gICAgICAgICAgICBpZiAocGFyc2VkVXJpLnF1ZXJ5KVxuICAgICAgICAgICAgICAgIG9wdHMucXVlcnkgPSBwYXJzZWRVcmkucXVlcnk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAob3B0cy5ob3N0KSB7XG4gICAgICAgICAgICBvcHRzLmhvc3RuYW1lID0gcGFyc2Uob3B0cy5ob3N0KS5ob3N0O1xuICAgICAgICB9XG4gICAgICAgIGluc3RhbGxUaW1lckZ1bmN0aW9ucyh0aGlzLCBvcHRzKTtcbiAgICAgICAgdGhpcy5zZWN1cmUgPVxuICAgICAgICAgICAgbnVsbCAhPSBvcHRzLnNlY3VyZVxuICAgICAgICAgICAgICAgID8gb3B0cy5zZWN1cmVcbiAgICAgICAgICAgICAgICA6IHR5cGVvZiBsb2NhdGlvbiAhPT0gXCJ1bmRlZmluZWRcIiAmJiBcImh0dHBzOlwiID09PSBsb2NhdGlvbi5wcm90b2NvbDtcbiAgICAgICAgaWYgKG9wdHMuaG9zdG5hbWUgJiYgIW9wdHMucG9ydCkge1xuICAgICAgICAgICAgLy8gaWYgbm8gcG9ydCBpcyBzcGVjaWZpZWQgbWFudWFsbHksIHVzZSB0aGUgcHJvdG9jb2wgZGVmYXVsdFxuICAgICAgICAgICAgb3B0cy5wb3J0ID0gdGhpcy5zZWN1cmUgPyBcIjQ0M1wiIDogXCI4MFwiO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaG9zdG5hbWUgPVxuICAgICAgICAgICAgb3B0cy5ob3N0bmFtZSB8fFxuICAgICAgICAgICAgICAgICh0eXBlb2YgbG9jYXRpb24gIT09IFwidW5kZWZpbmVkXCIgPyBsb2NhdGlvbi5ob3N0bmFtZSA6IFwibG9jYWxob3N0XCIpO1xuICAgICAgICB0aGlzLnBvcnQgPVxuICAgICAgICAgICAgb3B0cy5wb3J0IHx8XG4gICAgICAgICAgICAgICAgKHR5cGVvZiBsb2NhdGlvbiAhPT0gXCJ1bmRlZmluZWRcIiAmJiBsb2NhdGlvbi5wb3J0XG4gICAgICAgICAgICAgICAgICAgID8gbG9jYXRpb24ucG9ydFxuICAgICAgICAgICAgICAgICAgICA6IHRoaXMuc2VjdXJlXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiNDQzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCI4MFwiKTtcbiAgICAgICAgdGhpcy50cmFuc3BvcnRzID0gW107XG4gICAgICAgIHRoaXMuX3RyYW5zcG9ydHNCeU5hbWUgPSB7fTtcbiAgICAgICAgb3B0cy50cmFuc3BvcnRzLmZvckVhY2goKHQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zcG9ydE5hbWUgPSB0LnByb3RvdHlwZS5uYW1lO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BvcnRzLnB1c2godHJhbnNwb3J0TmFtZSk7XG4gICAgICAgICAgICB0aGlzLl90cmFuc3BvcnRzQnlOYW1lW3RyYW5zcG9ydE5hbWVdID0gdDtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub3B0cyA9IE9iamVjdC5hc3NpZ24oe1xuICAgICAgICAgICAgcGF0aDogXCIvZW5naW5lLmlvXCIsXG4gICAgICAgICAgICBhZ2VudDogZmFsc2UsXG4gICAgICAgICAgICB3aXRoQ3JlZGVudGlhbHM6IGZhbHNlLFxuICAgICAgICAgICAgdXBncmFkZTogdHJ1ZSxcbiAgICAgICAgICAgIHRpbWVzdGFtcFBhcmFtOiBcInRcIixcbiAgICAgICAgICAgIHJlbWVtYmVyVXBncmFkZTogZmFsc2UsXG4gICAgICAgICAgICBhZGRUcmFpbGluZ1NsYXNoOiB0cnVlLFxuICAgICAgICAgICAgcmVqZWN0VW5hdXRob3JpemVkOiB0cnVlLFxuICAgICAgICAgICAgcGVyTWVzc2FnZURlZmxhdGU6IHtcbiAgICAgICAgICAgICAgICB0aHJlc2hvbGQ6IDEwMjQsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgdHJhbnNwb3J0T3B0aW9uczoge30sXG4gICAgICAgICAgICBjbG9zZU9uQmVmb3JldW5sb2FkOiBmYWxzZSxcbiAgICAgICAgfSwgb3B0cyk7XG4gICAgICAgIHRoaXMub3B0cy5wYXRoID1cbiAgICAgICAgICAgIHRoaXMub3B0cy5wYXRoLnJlcGxhY2UoL1xcLyQvLCBcIlwiKSArXG4gICAgICAgICAgICAgICAgKHRoaXMub3B0cy5hZGRUcmFpbGluZ1NsYXNoID8gXCIvXCIgOiBcIlwiKTtcbiAgICAgICAgaWYgKHR5cGVvZiB0aGlzLm9wdHMucXVlcnkgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgIHRoaXMub3B0cy5xdWVyeSA9IGRlY29kZSh0aGlzLm9wdHMucXVlcnkpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh3aXRoRXZlbnRMaXN0ZW5lcnMpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLm9wdHMuY2xvc2VPbkJlZm9yZXVubG9hZCkge1xuICAgICAgICAgICAgICAgIC8vIEZpcmVmb3ggY2xvc2VzIHRoZSBjb25uZWN0aW9uIHdoZW4gdGhlIFwiYmVmb3JldW5sb2FkXCIgZXZlbnQgaXMgZW1pdHRlZCBidXQgbm90IENocm9tZS4gVGhpcyBldmVudCBsaXN0ZW5lclxuICAgICAgICAgICAgICAgIC8vIGVuc3VyZXMgZXZlcnkgYnJvd3NlciBiZWhhdmVzIHRoZSBzYW1lIChubyBcImRpc2Nvbm5lY3RcIiBldmVudCBhdCB0aGUgU29ja2V0LklPIGxldmVsIHdoZW4gdGhlIHBhZ2UgaXNcbiAgICAgICAgICAgICAgICAvLyBjbG9zZWQvcmVsb2FkZWQpXG4gICAgICAgICAgICAgICAgdGhpcy5fYmVmb3JldW5sb2FkRXZlbnRMaXN0ZW5lciA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMudHJhbnNwb3J0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBzaWxlbnRseSBjbG9zZSB0aGUgdHJhbnNwb3J0XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRyYW5zcG9ydC5yZW1vdmVBbGxMaXN0ZW5lcnMoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudHJhbnNwb3J0LmNsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGFkZEV2ZW50TGlzdGVuZXIoXCJiZWZvcmV1bmxvYWRcIiwgdGhpcy5fYmVmb3JldW5sb2FkRXZlbnRMaXN0ZW5lciwgZmFsc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMuaG9zdG5hbWUgIT09IFwibG9jYWxob3N0XCIpIHtcbiAgICAgICAgICAgICAgICBkZWJ1ZyhcImFkZGluZyBsaXN0ZW5lciBmb3IgdGhlICdvZmZsaW5lJyBldmVudFwiKTtcbiAgICAgICAgICAgICAgICB0aGlzLl9vZmZsaW5lRXZlbnRMaXN0ZW5lciA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fb25DbG9zZShcInRyYW5zcG9ydCBjbG9zZVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJuZXR3b3JrIGNvbm5lY3Rpb24gbG9zdFwiLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIE9GRkxJTkVfRVZFTlRfTElTVEVORVJTLnB1c2godGhpcy5fb2ZmbGluZUV2ZW50TGlzdGVuZXIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLm9wdHMud2l0aENyZWRlbnRpYWxzKSB7XG4gICAgICAgICAgICB0aGlzLl9jb29raWVKYXIgPSBjcmVhdGVDb29raWVKYXIoKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9vcGVuKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgdHJhbnNwb3J0IG9mIHRoZSBnaXZlbiB0eXBlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtTdHJpbmd9IG5hbWUgLSB0cmFuc3BvcnQgbmFtZVxuICAgICAqIEByZXR1cm4ge1RyYW5zcG9ydH1cbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIGNyZWF0ZVRyYW5zcG9ydChuYW1lKSB7XG4gICAgICAgIGRlYnVnKCdjcmVhdGluZyB0cmFuc3BvcnQgXCIlc1wiJywgbmFtZSk7XG4gICAgICAgIGNvbnN0IHF1ZXJ5ID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5vcHRzLnF1ZXJ5KTtcbiAgICAgICAgLy8gYXBwZW5kIGVuZ2luZS5pbyBwcm90b2NvbCBpZGVudGlmaWVyXG4gICAgICAgIHF1ZXJ5LkVJTyA9IHByb3RvY29sO1xuICAgICAgICAvLyB0cmFuc3BvcnQgbmFtZVxuICAgICAgICBxdWVyeS50cmFuc3BvcnQgPSBuYW1lO1xuICAgICAgICAvLyBzZXNzaW9uIGlkIGlmIHdlIGFscmVhZHkgaGF2ZSBvbmVcbiAgICAgICAgaWYgKHRoaXMuaWQpXG4gICAgICAgICAgICBxdWVyeS5zaWQgPSB0aGlzLmlkO1xuICAgICAgICBjb25zdCBvcHRzID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5vcHRzLCB7XG4gICAgICAgICAgICBxdWVyeSxcbiAgICAgICAgICAgIHNvY2tldDogdGhpcyxcbiAgICAgICAgICAgIGhvc3RuYW1lOiB0aGlzLmhvc3RuYW1lLFxuICAgICAgICAgICAgc2VjdXJlOiB0aGlzLnNlY3VyZSxcbiAgICAgICAgICAgIHBvcnQ6IHRoaXMucG9ydCxcbiAgICAgICAgfSwgdGhpcy5vcHRzLnRyYW5zcG9ydE9wdGlvbnNbbmFtZV0pO1xuICAgICAgICBkZWJ1ZyhcIm9wdGlvbnM6ICVqXCIsIG9wdHMpO1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMuX3RyYW5zcG9ydHNCeU5hbWVbbmFtZV0ob3B0cyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEluaXRpYWxpemVzIHRyYW5zcG9ydCB0byB1c2UgYW5kIHN0YXJ0cyBwcm9iZS5cbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgX29wZW4oKSB7XG4gICAgICAgIGlmICh0aGlzLnRyYW5zcG9ydHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAvLyBFbWl0IGVycm9yIG9uIG5leHQgdGljayBzbyBpdCBjYW4gYmUgbGlzdGVuZWQgdG9cbiAgICAgICAgICAgIHRoaXMuc2V0VGltZW91dEZuKCgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImVycm9yXCIsIFwiTm8gdHJhbnNwb3J0cyBhdmFpbGFibGVcIik7XG4gICAgICAgICAgICB9LCAwKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0cmFuc3BvcnROYW1lID0gdGhpcy5vcHRzLnJlbWVtYmVyVXBncmFkZSAmJlxuICAgICAgICAgICAgU29ja2V0V2l0aG91dFVwZ3JhZGUucHJpb3JXZWJzb2NrZXRTdWNjZXNzICYmXG4gICAgICAgICAgICB0aGlzLnRyYW5zcG9ydHMuaW5kZXhPZihcIndlYnNvY2tldFwiKSAhPT0gLTFcbiAgICAgICAgICAgID8gXCJ3ZWJzb2NrZXRcIlxuICAgICAgICAgICAgOiB0aGlzLnRyYW5zcG9ydHNbMF07XG4gICAgICAgIHRoaXMucmVhZHlTdGF0ZSA9IFwib3BlbmluZ1wiO1xuICAgICAgICBjb25zdCB0cmFuc3BvcnQgPSB0aGlzLmNyZWF0ZVRyYW5zcG9ydCh0cmFuc3BvcnROYW1lKTtcbiAgICAgICAgdHJhbnNwb3J0Lm9wZW4oKTtcbiAgICAgICAgdGhpcy5zZXRUcmFuc3BvcnQodHJhbnNwb3J0KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0cyB0aGUgY3VycmVudCB0cmFuc3BvcnQuIERpc2FibGVzIHRoZSBleGlzdGluZyBvbmUgKGlmIGFueSkuXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIHNldFRyYW5zcG9ydCh0cmFuc3BvcnQpIHtcbiAgICAgICAgZGVidWcoXCJzZXR0aW5nIHRyYW5zcG9ydCAlc1wiLCB0cmFuc3BvcnQubmFtZSk7XG4gICAgICAgIGlmICh0aGlzLnRyYW5zcG9ydCkge1xuICAgICAgICAgICAgZGVidWcoXCJjbGVhcmluZyBleGlzdGluZyB0cmFuc3BvcnQgJXNcIiwgdGhpcy50cmFuc3BvcnQubmFtZSk7XG4gICAgICAgICAgICB0aGlzLnRyYW5zcG9ydC5yZW1vdmVBbGxMaXN0ZW5lcnMoKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBzZXQgdXAgdHJhbnNwb3J0XG4gICAgICAgIHRoaXMudHJhbnNwb3J0ID0gdHJhbnNwb3J0O1xuICAgICAgICAvLyBzZXQgdXAgdHJhbnNwb3J0IGxpc3RlbmVyc1xuICAgICAgICB0cmFuc3BvcnRcbiAgICAgICAgICAgIC5vbihcImRyYWluXCIsIHRoaXMuX29uRHJhaW4uYmluZCh0aGlzKSlcbiAgICAgICAgICAgIC5vbihcInBhY2tldFwiLCB0aGlzLl9vblBhY2tldC5iaW5kKHRoaXMpKVxuICAgICAgICAgICAgLm9uKFwiZXJyb3JcIiwgdGhpcy5fb25FcnJvci5iaW5kKHRoaXMpKVxuICAgICAgICAgICAgLm9uKFwiY2xvc2VcIiwgKHJlYXNvbikgPT4gdGhpcy5fb25DbG9zZShcInRyYW5zcG9ydCBjbG9zZVwiLCByZWFzb24pKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHdoZW4gY29ubmVjdGlvbiBpcyBkZWVtZWQgb3Blbi5cbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgb25PcGVuKCkge1xuICAgICAgICBkZWJ1ZyhcInNvY2tldCBvcGVuXCIpO1xuICAgICAgICB0aGlzLnJlYWR5U3RhdGUgPSBcIm9wZW5cIjtcbiAgICAgICAgU29ja2V0V2l0aG91dFVwZ3JhZGUucHJpb3JXZWJzb2NrZXRTdWNjZXNzID1cbiAgICAgICAgICAgIFwid2Vic29ja2V0XCIgPT09IHRoaXMudHJhbnNwb3J0Lm5hbWU7XG4gICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwib3BlblwiKTtcbiAgICAgICAgdGhpcy5mbHVzaCgpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBIYW5kbGVzIGEgcGFja2V0LlxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfb25QYWNrZXQocGFja2V0KSB7XG4gICAgICAgIGlmIChcIm9wZW5pbmdcIiA9PT0gdGhpcy5yZWFkeVN0YXRlIHx8XG4gICAgICAgICAgICBcIm9wZW5cIiA9PT0gdGhpcy5yZWFkeVN0YXRlIHx8XG4gICAgICAgICAgICBcImNsb3NpbmdcIiA9PT0gdGhpcy5yZWFkeVN0YXRlKSB7XG4gICAgICAgICAgICBkZWJ1Zygnc29ja2V0IHJlY2VpdmU6IHR5cGUgXCIlc1wiLCBkYXRhIFwiJXNcIicsIHBhY2tldC50eXBlLCBwYWNrZXQuZGF0YSk7XG4gICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcInBhY2tldFwiLCBwYWNrZXQpO1xuICAgICAgICAgICAgLy8gU29ja2V0IGlzIGxpdmUgLSBhbnkgcGFja2V0IGNvdW50c1xuICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJoZWFydGJlYXRcIik7XG4gICAgICAgICAgICBzd2l0Y2ggKHBhY2tldC50eXBlKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBcIm9wZW5cIjpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5vbkhhbmRzaGFrZShKU09OLnBhcnNlKHBhY2tldC5kYXRhKSk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgXCJwaW5nXCI6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3NlbmRQYWNrZXQoXCJwb25nXCIpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcInBpbmdcIik7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwicG9uZ1wiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fcmVzZXRQaW5nVGltZW91dCgpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyID0gbmV3IEVycm9yKFwic2VydmVyIGVycm9yXCIpO1xuICAgICAgICAgICAgICAgICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAgICAgICAgICAgICAgIGVyci5jb2RlID0gcGFja2V0LmRhdGE7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX29uRXJyb3IoZXJyKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBcIm1lc3NhZ2VcIjpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJkYXRhXCIsIHBhY2tldC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJtZXNzYWdlXCIsIHBhY2tldC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBkZWJ1ZygncGFja2V0IHJlY2VpdmVkIHdpdGggc29ja2V0IHJlYWR5U3RhdGUgXCIlc1wiJywgdGhpcy5yZWFkeVN0YXRlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYWxsZWQgdXBvbiBoYW5kc2hha2UgY29tcGxldGlvbi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7T2JqZWN0fSBkYXRhIC0gaGFuZHNoYWtlIG9ialxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgb25IYW5kc2hha2UoZGF0YSkge1xuICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImhhbmRzaGFrZVwiLCBkYXRhKTtcbiAgICAgICAgdGhpcy5pZCA9IGRhdGEuc2lkO1xuICAgICAgICB0aGlzLnRyYW5zcG9ydC5xdWVyeS5zaWQgPSBkYXRhLnNpZDtcbiAgICAgICAgdGhpcy5fcGluZ0ludGVydmFsID0gZGF0YS5waW5nSW50ZXJ2YWw7XG4gICAgICAgIHRoaXMuX3BpbmdUaW1lb3V0ID0gZGF0YS5waW5nVGltZW91dDtcbiAgICAgICAgdGhpcy5fbWF4UGF5bG9hZCA9IGRhdGEubWF4UGF5bG9hZDtcbiAgICAgICAgdGhpcy5vbk9wZW4oKTtcbiAgICAgICAgLy8gSW4gY2FzZSBvcGVuIGhhbmRsZXIgY2xvc2VzIHNvY2tldFxuICAgICAgICBpZiAoXCJjbG9zZWRcIiA9PT0gdGhpcy5yZWFkeVN0YXRlKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB0aGlzLl9yZXNldFBpbmdUaW1lb3V0KCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldHMgYW5kIHJlc2V0cyBwaW5nIHRpbWVvdXQgdGltZXIgYmFzZWQgb24gc2VydmVyIHBpbmdzLlxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfcmVzZXRQaW5nVGltZW91dCgpIHtcbiAgICAgICAgdGhpcy5jbGVhclRpbWVvdXRGbih0aGlzLl9waW5nVGltZW91dFRpbWVyKTtcbiAgICAgICAgY29uc3QgZGVsYXkgPSB0aGlzLl9waW5nSW50ZXJ2YWwgKyB0aGlzLl9waW5nVGltZW91dDtcbiAgICAgICAgdGhpcy5fcGluZ1RpbWVvdXRUaW1lID0gRGF0ZS5ub3coKSArIGRlbGF5O1xuICAgICAgICB0aGlzLl9waW5nVGltZW91dFRpbWVyID0gdGhpcy5zZXRUaW1lb3V0Rm4oKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5fb25DbG9zZShcInBpbmcgdGltZW91dFwiKTtcbiAgICAgICAgfSwgZGVsYXkpO1xuICAgICAgICBpZiAodGhpcy5vcHRzLmF1dG9VbnJlZikge1xuICAgICAgICAgICAgdGhpcy5fcGluZ1RpbWVvdXRUaW1lci51bnJlZigpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENhbGxlZCBvbiBgZHJhaW5gIGV2ZW50XG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIF9vbkRyYWluKCkge1xuICAgICAgICB0aGlzLndyaXRlQnVmZmVyLnNwbGljZSgwLCB0aGlzLl9wcmV2QnVmZmVyTGVuKTtcbiAgICAgICAgLy8gc2V0dGluZyBwcmV2QnVmZmVyTGVuID0gMCBpcyB2ZXJ5IGltcG9ydGFudFxuICAgICAgICAvLyBmb3IgZXhhbXBsZSwgd2hlbiB1cGdyYWRpbmcsIHVwZ3JhZGUgcGFja2V0IGlzIHNlbnQgb3ZlcixcbiAgICAgICAgLy8gYW5kIGEgbm9uemVybyBwcmV2QnVmZmVyTGVuIGNvdWxkIGNhdXNlIHByb2JsZW1zIG9uIGBkcmFpbmBcbiAgICAgICAgdGhpcy5fcHJldkJ1ZmZlckxlbiA9IDA7XG4gICAgICAgIGlmICgwID09PSB0aGlzLndyaXRlQnVmZmVyLmxlbmd0aCkge1xuICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJkcmFpblwiKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuZmx1c2goKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBGbHVzaCB3cml0ZSBidWZmZXJzLlxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBmbHVzaCgpIHtcbiAgICAgICAgaWYgKFwiY2xvc2VkXCIgIT09IHRoaXMucmVhZHlTdGF0ZSAmJlxuICAgICAgICAgICAgdGhpcy50cmFuc3BvcnQud3JpdGFibGUgJiZcbiAgICAgICAgICAgICF0aGlzLnVwZ3JhZGluZyAmJlxuICAgICAgICAgICAgdGhpcy53cml0ZUJ1ZmZlci5sZW5ndGgpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhY2tldHMgPSB0aGlzLl9nZXRXcml0YWJsZVBhY2tldHMoKTtcbiAgICAgICAgICAgIGRlYnVnKFwiZmx1c2hpbmcgJWQgcGFja2V0cyBpbiBzb2NrZXRcIiwgcGFja2V0cy5sZW5ndGgpO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BvcnQuc2VuZChwYWNrZXRzKTtcbiAgICAgICAgICAgIC8vIGtlZXAgdHJhY2sgb2YgY3VycmVudCBsZW5ndGggb2Ygd3JpdGVCdWZmZXJcbiAgICAgICAgICAgIC8vIHNwbGljZSB3cml0ZUJ1ZmZlciBhbmQgY2FsbGJhY2tCdWZmZXIgb24gYGRyYWluYFxuICAgICAgICAgICAgdGhpcy5fcHJldkJ1ZmZlckxlbiA9IHBhY2tldHMubGVuZ3RoO1xuICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJmbHVzaFwiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBFbnN1cmUgdGhlIGVuY29kZWQgc2l6ZSBvZiB0aGUgd3JpdGVCdWZmZXIgaXMgYmVsb3cgdGhlIG1heFBheWxvYWQgdmFsdWUgc2VudCBieSB0aGUgc2VydmVyIChvbmx5IGZvciBIVFRQXG4gICAgICogbG9uZy1wb2xsaW5nKVxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfZ2V0V3JpdGFibGVQYWNrZXRzKCkge1xuICAgICAgICBjb25zdCBzaG91bGRDaGVja1BheWxvYWRTaXplID0gdGhpcy5fbWF4UGF5bG9hZCAmJlxuICAgICAgICAgICAgdGhpcy50cmFuc3BvcnQubmFtZSA9PT0gXCJwb2xsaW5nXCIgJiZcbiAgICAgICAgICAgIHRoaXMud3JpdGVCdWZmZXIubGVuZ3RoID4gMTtcbiAgICAgICAgaWYgKCFzaG91bGRDaGVja1BheWxvYWRTaXplKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy53cml0ZUJ1ZmZlcjtcbiAgICAgICAgfVxuICAgICAgICBsZXQgcGF5bG9hZFNpemUgPSAxOyAvLyBmaXJzdCBwYWNrZXQgdHlwZVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMud3JpdGVCdWZmZXIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSB0aGlzLndyaXRlQnVmZmVyW2ldLmRhdGE7XG4gICAgICAgICAgICBpZiAoZGF0YSkge1xuICAgICAgICAgICAgICAgIHBheWxvYWRTaXplICs9IGJ5dGVMZW5ndGgoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaSA+IDAgJiYgcGF5bG9hZFNpemUgPiB0aGlzLl9tYXhQYXlsb2FkKSB7XG4gICAgICAgICAgICAgICAgZGVidWcoXCJvbmx5IHNlbmQgJWQgb3V0IG9mICVkIHBhY2tldHNcIiwgaSwgdGhpcy53cml0ZUJ1ZmZlci5sZW5ndGgpO1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLndyaXRlQnVmZmVyLnNsaWNlKDAsIGkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGF5bG9hZFNpemUgKz0gMjsgLy8gc2VwYXJhdG9yICsgcGFja2V0IHR5cGVcbiAgICAgICAgfVxuICAgICAgICBkZWJ1ZyhcInBheWxvYWQgc2l6ZSBpcyAlZCAobWF4OiAlZClcIiwgcGF5bG9hZFNpemUsIHRoaXMuX21heFBheWxvYWQpO1xuICAgICAgICByZXR1cm4gdGhpcy53cml0ZUJ1ZmZlcjtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2hlY2tzIHdoZXRoZXIgdGhlIGhlYXJ0YmVhdCB0aW1lciBoYXMgZXhwaXJlZCBidXQgdGhlIHNvY2tldCBoYXMgbm90IHlldCBiZWVuIG5vdGlmaWVkLlxuICAgICAqXG4gICAgICogTm90ZTogdGhpcyBtZXRob2QgaXMgcHJpdmF0ZSBmb3Igbm93IGJlY2F1c2UgaXQgZG9lcyBub3QgcmVhbGx5IGZpdCB0aGUgV2ViU29ja2V0IEFQSSwgYnV0IGlmIHdlIHB1dCBpdCBpbiB0aGVcbiAgICAgKiBgd3JpdGUoKWAgbWV0aG9kIHRoZW4gdGhlIG1lc3NhZ2Ugd291bGQgbm90IGJlIGJ1ZmZlcmVkIGJ5IHRoZSBTb2NrZXQuSU8gY2xpZW50LlxuICAgICAqXG4gICAgICogQHJldHVybiB7Ym9vbGVhbn1cbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIC8qIHByaXZhdGUgKi8gX2hhc1BpbmdFeHBpcmVkKCkge1xuICAgICAgICBpZiAoIXRoaXMuX3BpbmdUaW1lb3V0VGltZSlcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICBjb25zdCBoYXNFeHBpcmVkID0gRGF0ZS5ub3coKSA+IHRoaXMuX3BpbmdUaW1lb3V0VGltZTtcbiAgICAgICAgaWYgKGhhc0V4cGlyZWQpIHtcbiAgICAgICAgICAgIGRlYnVnKFwidGhyb3R0bGVkIHRpbWVyIGRldGVjdGVkLCBzY2hlZHVsaW5nIGNvbm5lY3Rpb24gY2xvc2VcIik7XG4gICAgICAgICAgICB0aGlzLl9waW5nVGltZW91dFRpbWUgPSAwO1xuICAgICAgICAgICAgbmV4dFRpY2soKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuX29uQ2xvc2UoXCJwaW5nIHRpbWVvdXRcIik7XG4gICAgICAgICAgICB9LCB0aGlzLnNldFRpbWVvdXRGbik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGhhc0V4cGlyZWQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgbWVzc2FnZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7U3RyaW5nfSBtc2cgLSBtZXNzYWdlLlxuICAgICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zLlxuICAgICAqIEBwYXJhbSB7RnVuY3Rpb259IGZuIC0gY2FsbGJhY2sgZnVuY3Rpb24uXG4gICAgICogQHJldHVybiB7U29ja2V0fSBmb3IgY2hhaW5pbmcuXG4gICAgICovXG4gICAgd3JpdGUobXNnLCBvcHRpb25zLCBmbikge1xuICAgICAgICB0aGlzLl9zZW5kUGFja2V0KFwibWVzc2FnZVwiLCBtc2csIG9wdGlvbnMsIGZuKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgbWVzc2FnZS4gQWxpYXMgb2Yge0BsaW5rIFNvY2tldCN3cml0ZX0uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge1N0cmluZ30gbXNnIC0gbWVzc2FnZS5cbiAgICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucy5cbiAgICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBmbiAtIGNhbGxiYWNrIGZ1bmN0aW9uLlxuICAgICAqIEByZXR1cm4ge1NvY2tldH0gZm9yIGNoYWluaW5nLlxuICAgICAqL1xuICAgIHNlbmQobXNnLCBvcHRpb25zLCBmbikge1xuICAgICAgICB0aGlzLl9zZW5kUGFja2V0KFwibWVzc2FnZVwiLCBtc2csIG9wdGlvbnMsIGZuKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgcGFja2V0LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtTdHJpbmd9IHR5cGU6IHBhY2tldCB0eXBlLlxuICAgICAqIEBwYXJhbSB7U3RyaW5nfSBkYXRhLlxuICAgICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zLlxuICAgICAqIEBwYXJhbSB7RnVuY3Rpb259IGZuIC0gY2FsbGJhY2sgZnVuY3Rpb24uXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfc2VuZFBhY2tldCh0eXBlLCBkYXRhLCBvcHRpb25zLCBmbikge1xuICAgICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgZGF0YSkge1xuICAgICAgICAgICAgZm4gPSBkYXRhO1xuICAgICAgICAgICAgZGF0YSA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2Ygb3B0aW9ucykge1xuICAgICAgICAgICAgZm4gPSBvcHRpb25zO1xuICAgICAgICAgICAgb3B0aW9ucyA9IG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKFwiY2xvc2luZ1wiID09PSB0aGlzLnJlYWR5U3RhdGUgfHwgXCJjbG9zZWRcIiA9PT0gdGhpcy5yZWFkeVN0YXRlKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgICAgIG9wdGlvbnMuY29tcHJlc3MgPSBmYWxzZSAhPT0gb3B0aW9ucy5jb21wcmVzcztcbiAgICAgICAgY29uc3QgcGFja2V0ID0ge1xuICAgICAgICAgICAgdHlwZTogdHlwZSxcbiAgICAgICAgICAgIGRhdGE6IGRhdGEsXG4gICAgICAgICAgICBvcHRpb25zOiBvcHRpb25zLFxuICAgICAgICB9O1xuICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcInBhY2tldENyZWF0ZVwiLCBwYWNrZXQpO1xuICAgICAgICB0aGlzLndyaXRlQnVmZmVyLnB1c2gocGFja2V0KTtcbiAgICAgICAgaWYgKGZuKVxuICAgICAgICAgICAgdGhpcy5vbmNlKFwiZmx1c2hcIiwgZm4pO1xuICAgICAgICB0aGlzLmZsdXNoKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENsb3NlcyB0aGUgY29ubmVjdGlvbi5cbiAgICAgKi9cbiAgICBjbG9zZSgpIHtcbiAgICAgICAgY29uc3QgY2xvc2UgPSAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLl9vbkNsb3NlKFwiZm9yY2VkIGNsb3NlXCIpO1xuICAgICAgICAgICAgZGVidWcoXCJzb2NrZXQgY2xvc2luZyAtIHRlbGxpbmcgdHJhbnNwb3J0IHRvIGNsb3NlXCIpO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BvcnQuY2xvc2UoKTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgY2xlYW51cEFuZENsb3NlID0gKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5vZmYoXCJ1cGdyYWRlXCIsIGNsZWFudXBBbmRDbG9zZSk7XG4gICAgICAgICAgICB0aGlzLm9mZihcInVwZ3JhZGVFcnJvclwiLCBjbGVhbnVwQW5kQ2xvc2UpO1xuICAgICAgICAgICAgY2xvc2UoKTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3Qgd2FpdEZvclVwZ3JhZGUgPSAoKSA9PiB7XG4gICAgICAgICAgICAvLyB3YWl0IGZvciB1cGdyYWRlIHRvIGZpbmlzaCBzaW5jZSB3ZSBjYW4ndCBzZW5kIHBhY2tldHMgd2hpbGUgcGF1c2luZyBhIHRyYW5zcG9ydFxuICAgICAgICAgICAgdGhpcy5vbmNlKFwidXBncmFkZVwiLCBjbGVhbnVwQW5kQ2xvc2UpO1xuICAgICAgICAgICAgdGhpcy5vbmNlKFwidXBncmFkZUVycm9yXCIsIGNsZWFudXBBbmRDbG9zZSk7XG4gICAgICAgIH07XG4gICAgICAgIGlmIChcIm9wZW5pbmdcIiA9PT0gdGhpcy5yZWFkeVN0YXRlIHx8IFwib3BlblwiID09PSB0aGlzLnJlYWR5U3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMucmVhZHlTdGF0ZSA9IFwiY2xvc2luZ1wiO1xuICAgICAgICAgICAgaWYgKHRoaXMud3JpdGVCdWZmZXIubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5vbmNlKFwiZHJhaW5cIiwgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy51cGdyYWRpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHdhaXRGb3JVcGdyYWRlKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0aGlzLnVwZ3JhZGluZykge1xuICAgICAgICAgICAgICAgIHdhaXRGb3JVcGdyYWRlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBjbG9zZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYWxsZWQgdXBvbiB0cmFuc3BvcnQgZXJyb3JcbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgX29uRXJyb3IoZXJyKSB7XG4gICAgICAgIGRlYnVnKFwic29ja2V0IGVycm9yICVqXCIsIGVycik7XG4gICAgICAgIFNvY2tldFdpdGhvdXRVcGdyYWRlLnByaW9yV2Vic29ja2V0U3VjY2VzcyA9IGZhbHNlO1xuICAgICAgICBpZiAodGhpcy5vcHRzLnRyeUFsbFRyYW5zcG9ydHMgJiZcbiAgICAgICAgICAgIHRoaXMudHJhbnNwb3J0cy5sZW5ndGggPiAxICYmXG4gICAgICAgICAgICB0aGlzLnJlYWR5U3RhdGUgPT09IFwib3BlbmluZ1wiKSB7XG4gICAgICAgICAgICBkZWJ1ZyhcInRyeWluZyBuZXh0IHRyYW5zcG9ydFwiKTtcbiAgICAgICAgICAgIHRoaXMudHJhbnNwb3J0cy5zaGlmdCgpO1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX29wZW4oKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImVycm9yXCIsIGVycik7XG4gICAgICAgIHRoaXMuX29uQ2xvc2UoXCJ0cmFuc3BvcnQgZXJyb3JcIiwgZXJyKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHVwb24gdHJhbnNwb3J0IGNsb3NlLlxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfb25DbG9zZShyZWFzb24sIGRlc2NyaXB0aW9uKSB7XG4gICAgICAgIGlmIChcIm9wZW5pbmdcIiA9PT0gdGhpcy5yZWFkeVN0YXRlIHx8XG4gICAgICAgICAgICBcIm9wZW5cIiA9PT0gdGhpcy5yZWFkeVN0YXRlIHx8XG4gICAgICAgICAgICBcImNsb3NpbmdcIiA9PT0gdGhpcy5yZWFkeVN0YXRlKSB7XG4gICAgICAgICAgICBkZWJ1Zygnc29ja2V0IGNsb3NlIHdpdGggcmVhc29uOiBcIiVzXCInLCByZWFzb24pO1xuICAgICAgICAgICAgLy8gY2xlYXIgdGltZXJzXG4gICAgICAgICAgICB0aGlzLmNsZWFyVGltZW91dEZuKHRoaXMuX3BpbmdUaW1lb3V0VGltZXIpO1xuICAgICAgICAgICAgLy8gc3RvcCBldmVudCBmcm9tIGZpcmluZyBhZ2FpbiBmb3IgdHJhbnNwb3J0XG4gICAgICAgICAgICB0aGlzLnRyYW5zcG9ydC5yZW1vdmVBbGxMaXN0ZW5lcnMoXCJjbG9zZVwiKTtcbiAgICAgICAgICAgIC8vIGVuc3VyZSB0cmFuc3BvcnQgd29uJ3Qgc3RheSBvcGVuXG4gICAgICAgICAgICB0aGlzLnRyYW5zcG9ydC5jbG9zZSgpO1xuICAgICAgICAgICAgLy8gaWdub3JlIGZ1cnRoZXIgdHJhbnNwb3J0IGNvbW11bmljYXRpb25cbiAgICAgICAgICAgIHRoaXMudHJhbnNwb3J0LnJlbW92ZUFsbExpc3RlbmVycygpO1xuICAgICAgICAgICAgaWYgKHdpdGhFdmVudExpc3RlbmVycykge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLl9iZWZvcmV1bmxvYWRFdmVudExpc3RlbmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlbW92ZUV2ZW50TGlzdGVuZXIoXCJiZWZvcmV1bmxvYWRcIiwgdGhpcy5fYmVmb3JldW5sb2FkRXZlbnRMaXN0ZW5lciwgZmFsc2UpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAodGhpcy5fb2ZmbGluZUV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaSA9IE9GRkxJTkVfRVZFTlRfTElTVEVORVJTLmluZGV4T2YodGhpcy5fb2ZmbGluZUV2ZW50TGlzdGVuZXIpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaSAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlYnVnKFwicmVtb3ZpbmcgbGlzdGVuZXIgZm9yIHRoZSAnb2ZmbGluZScgZXZlbnRcIik7XG4gICAgICAgICAgICAgICAgICAgICAgICBPRkZMSU5FX0VWRU5UX0xJU1RFTkVSUy5zcGxpY2UoaSwgMSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBzZXQgcmVhZHkgc3RhdGVcbiAgICAgICAgICAgIHRoaXMucmVhZHlTdGF0ZSA9IFwiY2xvc2VkXCI7XG4gICAgICAgICAgICAvLyBjbGVhciBzZXNzaW9uIGlkXG4gICAgICAgICAgICB0aGlzLmlkID0gbnVsbDtcbiAgICAgICAgICAgIC8vIGVtaXQgY2xvc2UgZXZlbnRcbiAgICAgICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwiY2xvc2VcIiwgcmVhc29uLCBkZXNjcmlwdGlvbik7XG4gICAgICAgICAgICAvLyBjbGVhbiBidWZmZXJzIGFmdGVyLCBzbyB1c2VycyBjYW4gc3RpbGxcbiAgICAgICAgICAgIC8vIGdyYWIgdGhlIGJ1ZmZlcnMgb24gYGNsb3NlYCBldmVudFxuICAgICAgICAgICAgdGhpcy53cml0ZUJ1ZmZlciA9IFtdO1xuICAgICAgICAgICAgdGhpcy5fcHJldkJ1ZmZlckxlbiA9IDA7XG4gICAgICAgIH1cbiAgICB9XG59XG5Tb2NrZXRXaXRob3V0VXBncmFkZS5wcm90b2NvbCA9IHByb3RvY29sO1xuLyoqXG4gKiBUaGlzIGNsYXNzIHByb3ZpZGVzIGEgV2ViU29ja2V0LWxpa2UgaW50ZXJmYWNlIHRvIGNvbm5lY3QgdG8gYW4gRW5naW5lLklPIHNlcnZlci4gVGhlIGNvbm5lY3Rpb24gd2lsbCBiZSBlc3RhYmxpc2hlZFxuICogd2l0aCBvbmUgb2YgdGhlIGF2YWlsYWJsZSBsb3ctbGV2ZWwgdHJhbnNwb3J0cywgbGlrZSBIVFRQIGxvbmctcG9sbGluZywgV2ViU29ja2V0IG9yIFdlYlRyYW5zcG9ydC5cbiAqXG4gKiBUaGlzIGNsYXNzIGNvbWVzIHdpdGggYW4gdXBncmFkZSBtZWNoYW5pc20sIHdoaWNoIG1lYW5zIHRoYXQgb25jZSB0aGUgY29ubmVjdGlvbiBpcyBlc3RhYmxpc2hlZCB3aXRoIHRoZSBmaXJzdFxuICogbG93LWxldmVsIHRyYW5zcG9ydCwgaXQgd2lsbCB0cnkgdG8gdXBncmFkZSB0byBhIGJldHRlciB0cmFuc3BvcnQuXG4gKlxuICogSW4gb3JkZXIgdG8gYWxsb3cgdHJlZS1zaGFraW5nLCB0aGVyZSBhcmUgbm8gdHJhbnNwb3J0cyBpbmNsdWRlZCwgdGhhdCdzIHdoeSB0aGUgYHRyYW5zcG9ydHNgIG9wdGlvbiBpcyBtYW5kYXRvcnkuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IFNvY2tldFdpdGhVcGdyYWRlLCBXZWJTb2NrZXQgfSBmcm9tIFwiZW5naW5lLmlvLWNsaWVudFwiO1xuICpcbiAqIGNvbnN0IHNvY2tldCA9IG5ldyBTb2NrZXRXaXRoVXBncmFkZSh7XG4gKiAgIHRyYW5zcG9ydHM6IFtXZWJTb2NrZXRdXG4gKiB9KTtcbiAqXG4gKiBzb2NrZXQub24oXCJvcGVuXCIsICgpID0+IHtcbiAqICAgc29ja2V0LnNlbmQoXCJoZWxsb1wiKTtcbiAqIH0pO1xuICpcbiAqIEBzZWUgU29ja2V0V2l0aG91dFVwZ3JhZGVcbiAqIEBzZWUgU29ja2V0XG4gKi9cbmV4cG9ydCBjbGFzcyBTb2NrZXRXaXRoVXBncmFkZSBleHRlbmRzIFNvY2tldFdpdGhvdXRVcGdyYWRlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5fdXBncmFkZXMgPSBbXTtcbiAgICB9XG4gICAgb25PcGVuKCkge1xuICAgICAgICBzdXBlci5vbk9wZW4oKTtcbiAgICAgICAgaWYgKFwib3BlblwiID09PSB0aGlzLnJlYWR5U3RhdGUgJiYgdGhpcy5vcHRzLnVwZ3JhZGUpIHtcbiAgICAgICAgICAgIGRlYnVnKFwic3RhcnRpbmcgdXBncmFkZSBwcm9iZXNcIik7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuX3VwZ3JhZGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fcHJvYmUodGhpcy5fdXBncmFkZXNbaV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFByb2JlcyBhIHRyYW5zcG9ydC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7U3RyaW5nfSBuYW1lIC0gdHJhbnNwb3J0IG5hbWVcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIF9wcm9iZShuYW1lKSB7XG4gICAgICAgIGRlYnVnKCdwcm9iaW5nIHRyYW5zcG9ydCBcIiVzXCInLCBuYW1lKTtcbiAgICAgICAgbGV0IHRyYW5zcG9ydCA9IHRoaXMuY3JlYXRlVHJhbnNwb3J0KG5hbWUpO1xuICAgICAgICBsZXQgZmFpbGVkID0gZmFsc2U7XG4gICAgICAgIFNvY2tldFdpdGhvdXRVcGdyYWRlLnByaW9yV2Vic29ja2V0U3VjY2VzcyA9IGZhbHNlO1xuICAgICAgICBjb25zdCBvblRyYW5zcG9ydE9wZW4gPSAoKSA9PiB7XG4gICAgICAgICAgICBpZiAoZmFpbGVkKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGRlYnVnKCdwcm9iZSB0cmFuc3BvcnQgXCIlc1wiIG9wZW5lZCcsIG5hbWUpO1xuICAgICAgICAgICAgdHJhbnNwb3J0LnNlbmQoW3sgdHlwZTogXCJwaW5nXCIsIGRhdGE6IFwicHJvYmVcIiB9XSk7XG4gICAgICAgICAgICB0cmFuc3BvcnQub25jZShcInBhY2tldFwiLCAobXNnKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGZhaWxlZClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGlmIChcInBvbmdcIiA9PT0gbXNnLnR5cGUgJiYgXCJwcm9iZVwiID09PSBtc2cuZGF0YSkge1xuICAgICAgICAgICAgICAgICAgICBkZWJ1ZygncHJvYmUgdHJhbnNwb3J0IFwiJXNcIiBwb25nJywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudXBncmFkaW5nID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJ1cGdyYWRpbmdcIiwgdHJhbnNwb3J0KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCF0cmFuc3BvcnQpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIFNvY2tldFdpdGhvdXRVcGdyYWRlLnByaW9yV2Vic29ja2V0U3VjY2VzcyA9XG4gICAgICAgICAgICAgICAgICAgICAgICBcIndlYnNvY2tldFwiID09PSB0cmFuc3BvcnQubmFtZTtcbiAgICAgICAgICAgICAgICAgICAgZGVidWcoJ3BhdXNpbmcgY3VycmVudCB0cmFuc3BvcnQgXCIlc1wiJywgdGhpcy50cmFuc3BvcnQubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudHJhbnNwb3J0LnBhdXNlKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmYWlsZWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKFwiY2xvc2VkXCIgPT09IHRoaXMucmVhZHlTdGF0ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWJ1ZyhcImNoYW5naW5nIHRyYW5zcG9ydCBhbmQgc2VuZGluZyB1cGdyYWRlIHBhY2tldFwiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFudXAoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VHJhbnNwb3J0KHRyYW5zcG9ydCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc3BvcnQuc2VuZChbeyB0eXBlOiBcInVwZ3JhZGVcIiB9XSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcInVwZ3JhZGVcIiwgdHJhbnNwb3J0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zcG9ydCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVwZ3JhZGluZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mbHVzaCgpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGRlYnVnKCdwcm9iZSB0cmFuc3BvcnQgXCIlc1wiIGZhaWxlZCcsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlcnIgPSBuZXcgRXJyb3IoXCJwcm9iZSBlcnJvclwiKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICAgICAgICAgICAgICBlcnIudHJhbnNwb3J0ID0gdHJhbnNwb3J0Lm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwidXBncmFkZUVycm9yXCIsIGVycik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGZ1bmN0aW9uIGZyZWV6ZVRyYW5zcG9ydCgpIHtcbiAgICAgICAgICAgIGlmIChmYWlsZWQpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgLy8gQW55IGNhbGxiYWNrIGNhbGxlZCBieSB0cmFuc3BvcnQgc2hvdWxkIGJlIGlnbm9yZWQgc2luY2Ugbm93XG4gICAgICAgICAgICBmYWlsZWQgPSB0cnVlO1xuICAgICAgICAgICAgY2xlYW51cCgpO1xuICAgICAgICAgICAgdHJhbnNwb3J0LmNsb3NlKCk7XG4gICAgICAgICAgICB0cmFuc3BvcnQgPSBudWxsO1xuICAgICAgICB9XG4gICAgICAgIC8vIEhhbmRsZSBhbnkgZXJyb3IgdGhhdCBoYXBwZW5zIHdoaWxlIHByb2JpbmdcbiAgICAgICAgY29uc3Qgb25lcnJvciA9IChlcnIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKFwicHJvYmUgZXJyb3I6IFwiICsgZXJyKTtcbiAgICAgICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgICAgIGVycm9yLnRyYW5zcG9ydCA9IHRyYW5zcG9ydC5uYW1lO1xuICAgICAgICAgICAgZnJlZXplVHJhbnNwb3J0KCk7XG4gICAgICAgICAgICBkZWJ1ZygncHJvYmUgdHJhbnNwb3J0IFwiJXNcIiBmYWlsZWQgYmVjYXVzZSBvZiBlcnJvcjogJXMnLCBuYW1lLCBlcnIpO1xuICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJ1cGdyYWRlRXJyb3JcIiwgZXJyb3IpO1xuICAgICAgICB9O1xuICAgICAgICBmdW5jdGlvbiBvblRyYW5zcG9ydENsb3NlKCkge1xuICAgICAgICAgICAgb25lcnJvcihcInRyYW5zcG9ydCBjbG9zZWRcIik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gV2hlbiB0aGUgc29ja2V0IGlzIGNsb3NlZCB3aGlsZSB3ZSdyZSBwcm9iaW5nXG4gICAgICAgIGZ1bmN0aW9uIG9uY2xvc2UoKSB7XG4gICAgICAgICAgICBvbmVycm9yKFwic29ja2V0IGNsb3NlZFwiKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBXaGVuIHRoZSBzb2NrZXQgaXMgdXBncmFkZWQgd2hpbGUgd2UncmUgcHJvYmluZ1xuICAgICAgICBmdW5jdGlvbiBvbnVwZ3JhZGUodG8pIHtcbiAgICAgICAgICAgIGlmICh0cmFuc3BvcnQgJiYgdG8ubmFtZSAhPT0gdHJhbnNwb3J0Lm5hbWUpIHtcbiAgICAgICAgICAgICAgICBkZWJ1ZygnXCIlc1wiIHdvcmtzIC0gYWJvcnRpbmcgXCIlc1wiJywgdG8ubmFtZSwgdHJhbnNwb3J0Lm5hbWUpO1xuICAgICAgICAgICAgICAgIGZyZWV6ZVRyYW5zcG9ydCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIFJlbW92ZSBhbGwgbGlzdGVuZXJzIG9uIHRoZSB0cmFuc3BvcnQgYW5kIG9uIHNlbGZcbiAgICAgICAgY29uc3QgY2xlYW51cCA9ICgpID0+IHtcbiAgICAgICAgICAgIHRyYW5zcG9ydC5yZW1vdmVMaXN0ZW5lcihcIm9wZW5cIiwgb25UcmFuc3BvcnRPcGVuKTtcbiAgICAgICAgICAgIHRyYW5zcG9ydC5yZW1vdmVMaXN0ZW5lcihcImVycm9yXCIsIG9uZXJyb3IpO1xuICAgICAgICAgICAgdHJhbnNwb3J0LnJlbW92ZUxpc3RlbmVyKFwiY2xvc2VcIiwgb25UcmFuc3BvcnRDbG9zZSk7XG4gICAgICAgICAgICB0aGlzLm9mZihcImNsb3NlXCIsIG9uY2xvc2UpO1xuICAgICAgICAgICAgdGhpcy5vZmYoXCJ1cGdyYWRpbmdcIiwgb251cGdyYWRlKTtcbiAgICAgICAgfTtcbiAgICAgICAgdHJhbnNwb3J0Lm9uY2UoXCJvcGVuXCIsIG9uVHJhbnNwb3J0T3Blbik7XG4gICAgICAgIHRyYW5zcG9ydC5vbmNlKFwiZXJyb3JcIiwgb25lcnJvcik7XG4gICAgICAgIHRyYW5zcG9ydC5vbmNlKFwiY2xvc2VcIiwgb25UcmFuc3BvcnRDbG9zZSk7XG4gICAgICAgIHRoaXMub25jZShcImNsb3NlXCIsIG9uY2xvc2UpO1xuICAgICAgICB0aGlzLm9uY2UoXCJ1cGdyYWRpbmdcIiwgb251cGdyYWRlKTtcbiAgICAgICAgaWYgKHRoaXMuX3VwZ3JhZGVzLmluZGV4T2YoXCJ3ZWJ0cmFuc3BvcnRcIikgIT09IC0xICYmXG4gICAgICAgICAgICBuYW1lICE9PSBcIndlYnRyYW5zcG9ydFwiKSB7XG4gICAgICAgICAgICAvLyBmYXZvciBXZWJUcmFuc3BvcnRcbiAgICAgICAgICAgIHRoaXMuc2V0VGltZW91dEZuKCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIWZhaWxlZCkge1xuICAgICAgICAgICAgICAgICAgICB0cmFuc3BvcnQub3BlbigpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIDIwMCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0cmFuc3BvcnQub3BlbigpO1xuICAgICAgICB9XG4gICAgfVxuICAgIG9uSGFuZHNoYWtlKGRhdGEpIHtcbiAgICAgICAgdGhpcy5fdXBncmFkZXMgPSB0aGlzLl9maWx0ZXJVcGdyYWRlcyhkYXRhLnVwZ3JhZGVzKTtcbiAgICAgICAgc3VwZXIub25IYW5kc2hha2UoZGF0YSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEZpbHRlcnMgdXBncmFkZXMsIHJldHVybmluZyBvbmx5IHRob3NlIG1hdGNoaW5nIGNsaWVudCB0cmFuc3BvcnRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBcnJheX0gdXBncmFkZXMgLSBzZXJ2ZXIgdXBncmFkZXNcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIF9maWx0ZXJVcGdyYWRlcyh1cGdyYWRlcykge1xuICAgICAgICBjb25zdCBmaWx0ZXJlZFVwZ3JhZGVzID0gW107XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdXBncmFkZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGlmICh+dGhpcy50cmFuc3BvcnRzLmluZGV4T2YodXBncmFkZXNbaV0pKVxuICAgICAgICAgICAgICAgIGZpbHRlcmVkVXBncmFkZXMucHVzaCh1cGdyYWRlc1tpXSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZpbHRlcmVkVXBncmFkZXM7XG4gICAgfVxufVxuLyoqXG4gKiBUaGlzIGNsYXNzIHByb3ZpZGVzIGEgV2ViU29ja2V0LWxpa2UgaW50ZXJmYWNlIHRvIGNvbm5lY3QgdG8gYW4gRW5naW5lLklPIHNlcnZlci4gVGhlIGNvbm5lY3Rpb24gd2lsbCBiZSBlc3RhYmxpc2hlZFxuICogd2l0aCBvbmUgb2YgdGhlIGF2YWlsYWJsZSBsb3ctbGV2ZWwgdHJhbnNwb3J0cywgbGlrZSBIVFRQIGxvbmctcG9sbGluZywgV2ViU29ja2V0IG9yIFdlYlRyYW5zcG9ydC5cbiAqXG4gKiBUaGlzIGNsYXNzIGNvbWVzIHdpdGggYW4gdXBncmFkZSBtZWNoYW5pc20sIHdoaWNoIG1lYW5zIHRoYXQgb25jZSB0aGUgY29ubmVjdGlvbiBpcyBlc3RhYmxpc2hlZCB3aXRoIHRoZSBmaXJzdFxuICogbG93LWxldmVsIHRyYW5zcG9ydCwgaXQgd2lsbCB0cnkgdG8gdXBncmFkZSB0byBhIGJldHRlciB0cmFuc3BvcnQuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IFNvY2tldCB9IGZyb20gXCJlbmdpbmUuaW8tY2xpZW50XCI7XG4gKlxuICogY29uc3Qgc29ja2V0ID0gbmV3IFNvY2tldCgpO1xuICpcbiAqIHNvY2tldC5vbihcIm9wZW5cIiwgKCkgPT4ge1xuICogICBzb2NrZXQuc2VuZChcImhlbGxvXCIpO1xuICogfSk7XG4gKlxuICogQHNlZSBTb2NrZXRXaXRob3V0VXBncmFkZVxuICogQHNlZSBTb2NrZXRXaXRoVXBncmFkZVxuICovXG5leHBvcnQgY2xhc3MgU29ja2V0IGV4dGVuZHMgU29ja2V0V2l0aFVwZ3JhZGUge1xuICAgIGNvbnN0cnVjdG9yKHVyaSwgb3B0cyA9IHt9KSB7XG4gICAgICAgIGNvbnN0IG8gPSB0eXBlb2YgdXJpID09PSBcIm9iamVjdFwiID8gdXJpIDogb3B0cztcbiAgICAgICAgaWYgKCFvLnRyYW5zcG9ydHMgfHxcbiAgICAgICAgICAgIChvLnRyYW5zcG9ydHMgJiYgdHlwZW9mIG8udHJhbnNwb3J0c1swXSA9PT0gXCJzdHJpbmdcIikpIHtcbiAgICAgICAgICAgIG8udHJhbnNwb3J0cyA9IChvLnRyYW5zcG9ydHMgfHwgW1wicG9sbGluZ1wiLCBcIndlYnNvY2tldFwiLCBcIndlYnRyYW5zcG9ydFwiXSlcbiAgICAgICAgICAgICAgICAubWFwKCh0cmFuc3BvcnROYW1lKSA9PiBERUZBVUxUX1RSQU5TUE9SVFNbdHJhbnNwb3J0TmFtZV0pXG4gICAgICAgICAgICAgICAgLmZpbHRlcigodCkgPT4gISF0KTtcbiAgICAgICAgfVxuICAgICAgICBzdXBlcih1cmksIG8pO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6WyJ0cmFuc3BvcnRzIiwiREVGQVVMVF9UUkFOU1BPUlRTIiwiaW5zdGFsbFRpbWVyRnVuY3Rpb25zIiwiYnl0ZUxlbmd0aCIsImRlY29kZSIsInBhcnNlIiwiRW1pdHRlciIsInByb3RvY29sIiwiY3JlYXRlQ29va2llSmFyIiwiZGVmYXVsdEJpbmFyeVR5cGUiLCJuZXh0VGljayIsImRlYnVnTW9kdWxlIiwiZGVidWciLCJ3aXRoRXZlbnRMaXN0ZW5lcnMiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIk9GRkxJTkVfRVZFTlRfTElTVEVORVJTIiwibGVuZ3RoIiwiZm9yRWFjaCIsImxpc3RlbmVyIiwiU29ja2V0V2l0aG91dFVwZ3JhZGUiLCJjb25zdHJ1Y3RvciIsInVyaSIsIm9wdHMiLCJiaW5hcnlUeXBlIiwid3JpdGVCdWZmZXIiLCJfcHJldkJ1ZmZlckxlbiIsIl9waW5nSW50ZXJ2YWwiLCJfcGluZ1RpbWVvdXQiLCJfbWF4UGF5bG9hZCIsIl9waW5nVGltZW91dFRpbWUiLCJJbmZpbml0eSIsInBhcnNlZFVyaSIsImhvc3RuYW1lIiwiaG9zdCIsInNlY3VyZSIsInBvcnQiLCJxdWVyeSIsImxvY2F0aW9uIiwiX3RyYW5zcG9ydHNCeU5hbWUiLCJ0IiwidHJhbnNwb3J0TmFtZSIsInByb3RvdHlwZSIsIm5hbWUiLCJwdXNoIiwiT2JqZWN0IiwiYXNzaWduIiwicGF0aCIsImFnZW50Iiwid2l0aENyZWRlbnRpYWxzIiwidXBncmFkZSIsInRpbWVzdGFtcFBhcmFtIiwicmVtZW1iZXJVcGdyYWRlIiwiYWRkVHJhaWxpbmdTbGFzaCIsInJlamVjdFVuYXV0aG9yaXplZCIsInBlck1lc3NhZ2VEZWZsYXRlIiwidGhyZXNob2xkIiwidHJhbnNwb3J0T3B0aW9ucyIsImNsb3NlT25CZWZvcmV1bmxvYWQiLCJyZXBsYWNlIiwiX2JlZm9yZXVubG9hZEV2ZW50TGlzdGVuZXIiLCJ0cmFuc3BvcnQiLCJyZW1vdmVBbGxMaXN0ZW5lcnMiLCJjbG9zZSIsIl9vZmZsaW5lRXZlbnRMaXN0ZW5lciIsIl9vbkNsb3NlIiwiZGVzY3JpcHRpb24iLCJfY29va2llSmFyIiwiX29wZW4iLCJjcmVhdGVUcmFuc3BvcnQiLCJFSU8iLCJpZCIsInNpZCIsInNvY2tldCIsInNldFRpbWVvdXRGbiIsImVtaXRSZXNlcnZlZCIsInByaW9yV2Vic29ja2V0U3VjY2VzcyIsImluZGV4T2YiLCJyZWFkeVN0YXRlIiwib3BlbiIsInNldFRyYW5zcG9ydCIsIm9uIiwiX29uRHJhaW4iLCJiaW5kIiwiX29uUGFja2V0IiwiX29uRXJyb3IiLCJyZWFzb24iLCJvbk9wZW4iLCJmbHVzaCIsInBhY2tldCIsInR5cGUiLCJkYXRhIiwib25IYW5kc2hha2UiLCJKU09OIiwiX3NlbmRQYWNrZXQiLCJfcmVzZXRQaW5nVGltZW91dCIsImVyciIsIkVycm9yIiwiY29kZSIsInBpbmdJbnRlcnZhbCIsInBpbmdUaW1lb3V0IiwibWF4UGF5bG9hZCIsImNsZWFyVGltZW91dEZuIiwiX3BpbmdUaW1lb3V0VGltZXIiLCJkZWxheSIsIkRhdGUiLCJub3ciLCJhdXRvVW5yZWYiLCJ1bnJlZiIsInNwbGljZSIsIndyaXRhYmxlIiwidXBncmFkaW5nIiwicGFja2V0cyIsIl9nZXRXcml0YWJsZVBhY2tldHMiLCJzZW5kIiwic2hvdWxkQ2hlY2tQYXlsb2FkU2l6ZSIsInBheWxvYWRTaXplIiwiaSIsInNsaWNlIiwiX2hhc1BpbmdFeHBpcmVkIiwiaGFzRXhwaXJlZCIsIndyaXRlIiwibXNnIiwib3B0aW9ucyIsImZuIiwidW5kZWZpbmVkIiwiY29tcHJlc3MiLCJvbmNlIiwiY2xlYW51cEFuZENsb3NlIiwib2ZmIiwid2FpdEZvclVwZ3JhZGUiLCJ0cnlBbGxUcmFuc3BvcnRzIiwic2hpZnQiLCJTb2NrZXRXaXRoVXBncmFkZSIsImFyZ3VtZW50cyIsIl91cGdyYWRlcyIsIl9wcm9iZSIsImZhaWxlZCIsIm9uVHJhbnNwb3J0T3BlbiIsInBhdXNlIiwiY2xlYW51cCIsImZyZWV6ZVRyYW5zcG9ydCIsIm9uZXJyb3IiLCJlcnJvciIsIm9uVHJhbnNwb3J0Q2xvc2UiLCJvbmNsb3NlIiwib251cGdyYWRlIiwidG8iLCJyZW1vdmVMaXN0ZW5lciIsIl9maWx0ZXJVcGdyYWRlcyIsInVwZ3JhZGVzIiwiZmlsdGVyZWRVcGdyYWRlcyIsIlNvY2tldCIsIm8iLCJtYXAiLCJmaWx0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js":
/*!********************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transport.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transport: () => (/* binding */ Transport),\n/* harmony export */   TransportError: () => (/* binding */ TransportError)\n/* harmony export */ });\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contrib/parseqs.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_4__(\"engine.io-client:transport\"); // debug()\nclass TransportError extends Error {\n    constructor(reason, description, context){\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nclass Transport extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__.Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */ constructor(opts){\n        super();\n        this.writable = false;\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.installTimerFunctions)(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */ onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */ open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */ close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */ send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        } else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n            debug(\"transport is not open, discarding packets\");\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */ onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */ onData(data) {\n        const packet = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_0__.decodePacket)(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */ onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */ onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */ pause(onPause) {}\n    createUri(schema, query = {}) {\n        return schema + \"://\" + this._hostname() + this._port() + this.opts.path + this._query(query);\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port && (this.opts.secure && Number(this.opts.port !== 443) || !this.opts.secure && Number(this.opts.port) !== 80)) {\n            return \":\" + this.opts.port;\n        } else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = (0,_contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_3__.encode)(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transports: () => (/* binding */ transports)\n/* harmony export */ });\n/* harmony import */ var _polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling-xhr.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\");\n/* harmony import */ var _websocket_node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./websocket.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\");\n/* harmony import */ var _webtransport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webtransport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\");\n\n\n\nconst transports = {\n    websocket: _websocket_node_js__WEBPACK_IMPORTED_MODULE_1__.WS,\n    webtransport: _webtransport_js__WEBPACK_IMPORTED_MODULE_2__.WT,\n    polling: _polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_0__.XHR\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdHJhbnNwb3J0cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBQ0g7QUFDRjtBQUNoQyxNQUFNRyxhQUFhO0lBQ3RCQyxXQUFXSCxrREFBRUE7SUFDYkksY0FBY0gsZ0RBQUVBO0lBQ2hCSSxTQUFTTixxREFBR0E7QUFDaEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9lbmdpbmUuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy90cmFuc3BvcnRzL2luZGV4LmpzP2QzMTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgWEhSIH0gZnJvbSBcIi4vcG9sbGluZy14aHIubm9kZS5qc1wiO1xuaW1wb3J0IHsgV1MgfSBmcm9tIFwiLi93ZWJzb2NrZXQubm9kZS5qc1wiO1xuaW1wb3J0IHsgV1QgfSBmcm9tIFwiLi93ZWJ0cmFuc3BvcnQuanNcIjtcbmV4cG9ydCBjb25zdCB0cmFuc3BvcnRzID0ge1xuICAgIHdlYnNvY2tldDogV1MsXG4gICAgd2VidHJhbnNwb3J0OiBXVCxcbiAgICBwb2xsaW5nOiBYSFIsXG59O1xuIl0sIm5hbWVzIjpbIlhIUiIsIldTIiwiV1QiLCJ0cmFuc3BvcnRzIiwid2Vic29ja2V0Iiwid2VidHJhbnNwb3J0IiwicG9sbGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* binding */ Fetch)\n/* harmony export */ });\n/* harmony import */ var _polling_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\");\n\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */ class Fetch extends _polling_js__WEBPACK_IMPORTED_MODULE_0__.Polling {\n    doPoll() {\n        this._fetch().then((res)=>{\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data)=>this.onData(data));\n        }).catch((err)=>{\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data).then((res)=>{\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        }).catch((err)=>{\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\"\n        }).then((res)=>{\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdHJhbnNwb3J0cy9wb2xsaW5nLWZldGNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBQ3ZDOzs7Ozs7OztDQVFDLEdBQ00sTUFBTUMsY0FBY0QsZ0RBQU9BO0lBQzlCRSxTQUFTO1FBQ0wsSUFBSSxDQUFDQyxNQUFNLEdBQ05DLElBQUksQ0FBQyxDQUFDQztZQUNQLElBQUksQ0FBQ0EsSUFBSUMsRUFBRSxFQUFFO2dCQUNULE9BQU8sSUFBSSxDQUFDQyxPQUFPLENBQUMsb0JBQW9CRixJQUFJRyxNQUFNLEVBQUVIO1lBQ3hEO1lBQ0FBLElBQUlJLElBQUksR0FBR0wsSUFBSSxDQUFDLENBQUNNLE9BQVMsSUFBSSxDQUFDQyxNQUFNLENBQUNEO1FBQzFDLEdBQ0tFLEtBQUssQ0FBQyxDQUFDQztZQUNSLElBQUksQ0FBQ04sT0FBTyxDQUFDLG9CQUFvQk07UUFDckM7SUFDSjtJQUNBQyxRQUFRSixJQUFJLEVBQUVLLFFBQVEsRUFBRTtRQUNwQixJQUFJLENBQUNaLE1BQU0sQ0FBQ08sTUFDUE4sSUFBSSxDQUFDLENBQUNDO1lBQ1AsSUFBSSxDQUFDQSxJQUFJQyxFQUFFLEVBQUU7Z0JBQ1QsT0FBTyxJQUFJLENBQUNDLE9BQU8sQ0FBQyxxQkFBcUJGLElBQUlHLE1BQU0sRUFBRUg7WUFDekQ7WUFDQVU7UUFDSixHQUNLSCxLQUFLLENBQUMsQ0FBQ0M7WUFDUixJQUFJLENBQUNOLE9BQU8sQ0FBQyxxQkFBcUJNO1FBQ3RDO0lBQ0o7SUFDQVYsT0FBT08sSUFBSSxFQUFFO1FBQ1QsSUFBSU07UUFDSixNQUFNQyxTQUFTUCxTQUFTUTtRQUN4QixNQUFNQyxVQUFVLElBQUlDLFFBQVEsSUFBSSxDQUFDQyxJQUFJLENBQUNDLFlBQVk7UUFDbEQsSUFBSUwsUUFBUTtZQUNSRSxRQUFRSSxHQUFHLENBQUMsZ0JBQWdCO1FBQ2hDO1FBQ0NQLENBQUFBLEtBQUssSUFBSSxDQUFDUSxNQUFNLENBQUNDLFVBQVUsTUFBTSxRQUFRVCxPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdVLGFBQWEsQ0FBQ1A7UUFDcEYsT0FBT1EsTUFBTSxJQUFJLENBQUNDLEdBQUcsSUFBSTtZQUNyQkMsUUFBUVosU0FBUyxTQUFTO1lBQzFCYSxNQUFNYixTQUFTUCxPQUFPO1lBQ3RCUztZQUNBWSxhQUFhLElBQUksQ0FBQ1YsSUFBSSxDQUFDVyxlQUFlLEdBQUcsWUFBWTtRQUN6RCxHQUFHNUIsSUFBSSxDQUFDLENBQUNDO1lBQ0wsSUFBSVc7WUFDSix5REFBeUQ7WUFDeERBLENBQUFBLEtBQUssSUFBSSxDQUFDUSxNQUFNLENBQUNDLFVBQVUsTUFBTSxRQUFRVCxPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdpQixZQUFZLENBQUM1QixJQUFJYyxPQUFPLENBQUNlLFlBQVk7WUFDM0csT0FBTzdCO1FBQ1g7SUFDSjtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2VuZ2luZS5pby1jbGllbnQvYnVpbGQvZXNtLWRlYnVnL3RyYW5zcG9ydHMvcG9sbGluZy1mZXRjaC5qcz84MzFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvbGxpbmcgfSBmcm9tIFwiLi9wb2xsaW5nLmpzXCI7XG4vKipcbiAqIEhUVFAgbG9uZy1wb2xsaW5nIGJhc2VkIG9uIHRoZSBidWlsdC1pbiBgZmV0Y2goKWAgbWV0aG9kLlxuICpcbiAqIFVzYWdlOiBicm93c2VyLCBOb2RlLmpzIChzaW5jZSB2MTgpLCBEZW5vLCBCdW5cbiAqXG4gKiBAc2VlIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9mZXRjaFxuICogQHNlZSBodHRwczovL2Nhbml1c2UuY29tL2ZldGNoXG4gKiBAc2VlIGh0dHBzOi8vbm9kZWpzLm9yZy9hcGkvZ2xvYmFscy5odG1sI2ZldGNoXG4gKi9cbmV4cG9ydCBjbGFzcyBGZXRjaCBleHRlbmRzIFBvbGxpbmcge1xuICAgIGRvUG9sbCgpIHtcbiAgICAgICAgdGhpcy5fZmV0Y2goKVxuICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4ge1xuICAgICAgICAgICAgaWYgKCFyZXMub2spIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5vbkVycm9yKFwiZmV0Y2ggcmVhZCBlcnJvclwiLCByZXMuc3RhdHVzLCByZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmVzLnRleHQoKS50aGVuKChkYXRhKSA9PiB0aGlzLm9uRGF0YShkYXRhKSk7XG4gICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycikgPT4ge1xuICAgICAgICAgICAgdGhpcy5vbkVycm9yKFwiZmV0Y2ggcmVhZCBlcnJvclwiLCBlcnIpO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZG9Xcml0ZShkYXRhLCBjYWxsYmFjaykge1xuICAgICAgICB0aGlzLl9mZXRjaChkYXRhKVxuICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4ge1xuICAgICAgICAgICAgaWYgKCFyZXMub2spIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5vbkVycm9yKFwiZmV0Y2ggd3JpdGUgZXJyb3JcIiwgcmVzLnN0YXR1cywgcmVzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhbGxiYWNrKCk7XG4gICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycikgPT4ge1xuICAgICAgICAgICAgdGhpcy5vbkVycm9yKFwiZmV0Y2ggd3JpdGUgZXJyb3JcIiwgZXJyKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF9mZXRjaChkYXRhKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgaXNQb3N0ID0gZGF0YSAhPT0gdW5kZWZpbmVkO1xuICAgICAgICBjb25zdCBoZWFkZXJzID0gbmV3IEhlYWRlcnModGhpcy5vcHRzLmV4dHJhSGVhZGVycyk7XG4gICAgICAgIGlmIChpc1Bvc3QpIHtcbiAgICAgICAgICAgIGhlYWRlcnMuc2V0KFwiY29udGVudC10eXBlXCIsIFwidGV4dC9wbGFpbjtjaGFyc2V0PVVURi04XCIpO1xuICAgICAgICB9XG4gICAgICAgIChfYSA9IHRoaXMuc29ja2V0Ll9jb29raWVKYXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5hcHBlbmRDb29raWVzKGhlYWRlcnMpO1xuICAgICAgICByZXR1cm4gZmV0Y2godGhpcy51cmkoKSwge1xuICAgICAgICAgICAgbWV0aG9kOiBpc1Bvc3QgPyBcIlBPU1RcIiA6IFwiR0VUXCIsXG4gICAgICAgICAgICBib2R5OiBpc1Bvc3QgPyBkYXRhIDogbnVsbCxcbiAgICAgICAgICAgIGhlYWRlcnMsXG4gICAgICAgICAgICBjcmVkZW50aWFsczogdGhpcy5vcHRzLndpdGhDcmVkZW50aWFscyA/IFwiaW5jbHVkZVwiIDogXCJvbWl0XCIsXG4gICAgICAgIH0pLnRoZW4oKHJlcykgPT4ge1xuICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgLy8gQHRzLWlnbm9yZSBnZXRTZXRDb29raWUoKSB3YXMgYWRkZWQgaW4gTm9kZS5qcyB2MTkuNy4wXG4gICAgICAgICAgICAoX2EgPSB0aGlzLnNvY2tldC5fY29va2llSmFyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucGFyc2VDb29raWVzKHJlcy5oZWFkZXJzLmdldFNldENvb2tpZSgpKTtcbiAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6WyJQb2xsaW5nIiwiRmV0Y2giLCJkb1BvbGwiLCJfZmV0Y2giLCJ0aGVuIiwicmVzIiwib2siLCJvbkVycm9yIiwic3RhdHVzIiwidGV4dCIsImRhdGEiLCJvbkRhdGEiLCJjYXRjaCIsImVyciIsImRvV3JpdGUiLCJjYWxsYmFjayIsIl9hIiwiaXNQb3N0IiwidW5kZWZpbmVkIiwiaGVhZGVycyIsIkhlYWRlcnMiLCJvcHRzIiwiZXh0cmFIZWFkZXJzIiwic2V0Iiwic29ja2V0IiwiX2Nvb2tpZUphciIsImFwcGVuZENvb2tpZXMiLCJmZXRjaCIsInVyaSIsIm1ldGhvZCIsImJvZHkiLCJjcmVkZW50aWFscyIsIndpdGhDcmVkZW50aWFscyIsInBhcnNlQ29va2llcyIsImdldFNldENvb2tpZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseXHR: () => (/* binding */ BaseXHR),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   XHR: () => (/* binding */ XHR)\n/* harmony export */ });\n/* harmony import */ var _polling_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var _contrib_has_cors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contrib/has-cors.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_5__(\"engine.io-client:polling\"); // debug()\nfunction empty() {}\nclass BaseXHR extends _polling_js__WEBPACK_IMPORTED_MODULE_0__.Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */ constructor(opts){\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */ doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context)=>{\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */ doPoll() {\n        debug(\"xhr poll\");\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context)=>{\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nclass Request extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__.Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */ constructor(createRequest, uri, opts){\n        super();\n        this.createRequest = createRequest;\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.installTimerFunctions)(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */ _create() {\n        var _a;\n        const opts = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.pick)(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = this._xhr = this.createRequest(opts);\n        try {\n            debug(\"xhr open %s: %s\", this._method, this._uri);\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for(let i in this._opts.extraHeaders){\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            } catch (e) {}\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                } catch (e) {}\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            } catch (e) {}\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = ()=>{\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(// @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState) return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                } else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(()=>{\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            debug(\"xhr data %s\", this._data);\n            xhr.send(this._data);\n        } catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(()=>{\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */ _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */ _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            } catch (e) {}\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */ _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */ abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */ if (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    } else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for(let i in Request.requests){\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = function() {\n    const xhr = newRequest({\n        xdomain: false\n    });\n    return xhr && xhr.responseType !== null;\n}();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */ class XHR extends BaseXHR {\n    constructor(opts){\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, {\n            xd: this.xd\n        }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || _contrib_has_cors_js__WEBPACK_IMPORTED_MODULE_4__.hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    } catch (e) {}\n    if (!xdomain) {\n        try {\n            return new _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim[[\n                \"Active\"\n            ].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        } catch (e) {}\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("var xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   XHR: () => (/* binding */ XHR)\n/* harmony export */ });\n/* harmony import */ var xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! xmlhttprequest-ssl */ \"(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\");\n/* harmony import */ var _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polling-xhr.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\");\n\n\nconst XMLHttpRequest = xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__ || /*#__PURE__*/ (xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__, 2)));\n/**\n * HTTP long-polling based on the `XMLHttpRequest` object provided by the `xmlhttprequest-ssl` package.\n *\n * Usage: Node.js, Deno (compat), Bun (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */ class XHR extends _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__.BaseXHR {\n    request(opts = {}) {\n        var _a;\n        Object.assign(opts, {\n            xd: this.xd,\n            cookieJar: (_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar\n        }, this.opts);\n        return new _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__.Request((opts)=>new XMLHttpRequest(opts), this.uri(), opts);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Polling: () => (/* binding */ Polling)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"engine.io-client:polling\"); // debug()\nclass Polling extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    constructor(){\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */ doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */ pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = ()=>{\n            debug(\"paused\");\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                debug(\"we are currently polling - waiting to pause\");\n                total++;\n                this.once(\"pollComplete\", function() {\n                    debug(\"pre-pause polling complete\");\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                debug(\"we are currently writing - waiting to pause\");\n                total++;\n                this.once(\"drain\", function() {\n                    debug(\"pre-pause writing complete\");\n                    --total || pause();\n                });\n            }\n        } else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */ _poll() {\n        debug(\"polling\");\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */ onData(data) {\n        debug(\"polling got data %s\", data);\n        const callback = (packet)=>{\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({\n                    description: \"transport closed by the server\"\n                });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.decodePayload)(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            } else {\n                debug('ignoring poll - transport state \"%s\"', this.readyState);\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */ doClose() {\n        const close = ()=>{\n            debug(\"writing close packet\");\n            this.write([\n                {\n                    type: \"close\"\n                }\n            ]);\n        };\n        if (\"open\" === this.readyState) {\n            debug(\"transport open - closing\");\n            close();\n        } else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            debug(\"transport not open - deferring close\");\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */ write(packets) {\n        this.writable = false;\n        (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.encodePayload)(packets, (data)=>{\n            this.doWrite(data, ()=>{\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */ uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.randomString)();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/websocket.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseWS: () => (/* binding */ BaseWS),\n/* harmony export */   WS: () => (/* binding */ WS)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_4__(\"engine.io-client:websocket\"); // debug()\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" && typeof navigator.product === \"string\" && navigator.product.toLowerCase() === \"reactnative\";\nclass BaseWS extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative ? {} : (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.pick)(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        } catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */ addEventListeners() {\n        this.ws.onopen = ()=>{\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent)=>this.onClose({\n                description: \"websocket connection closed\",\n                context: closeEvent\n            });\n        this.ws.onmessage = (ev)=>this.onData(ev.data);\n        this.ws.onerror = (e)=>this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for(let i = 0; i < packets.length; i++){\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.encodePacket)(packet, this.supportsBinary, (data)=>{\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                } catch (e) {\n                    debug(\"websocket closed before onclose event\");\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_3__.nextTick)(()=>{\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = ()=>{};\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */ uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.randomString)();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim.WebSocket || _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */ class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative ? protocols ? new WebSocketCtor(uri, protocols) : new WebSocketCtor(uri) : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js":
/*!************************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WS: () => (/* binding */ WS)\n/* harmony export */ });\n/* harmony import */ var ws__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ws */ \"(ssr)/./node_modules/ws/wrapper.mjs\");\n/* harmony import */ var _websocket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./websocket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\");\n\n\n/**\n * WebSocket transport based on the `WebSocket` object provided by the `ws` package.\n *\n * Usage: Node.js, Deno (compat), Bun (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n */ class WS extends _websocket_js__WEBPACK_IMPORTED_MODULE_1__.BaseWS {\n    createSocket(uri, protocols, opts) {\n        var _a;\n        if ((_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar) {\n            opts.headers = opts.headers || {};\n            opts.headers.cookie = typeof opts.headers.cookie === \"string\" ? [\n                opts.headers.cookie\n            ] : opts.headers.cookie || [];\n            for (const [name, cookie] of this.socket._cookieJar.cookies){\n                opts.headers.cookie.push(`${name}=${cookie.value}`);\n            }\n        }\n        return new ws__WEBPACK_IMPORTED_MODULE_0__.WebSocket(uri, protocols, opts);\n    }\n    doWrite(packet, data) {\n        const opts = {};\n        if (packet.options) {\n            opts.compress = packet.options.compress;\n        }\n        if (this.opts.perMessageDeflate) {\n            const len = // @ts-ignore\n            \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n            }\n        }\n        this.ws.send(data, opts);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WT: () => (/* binding */ WT)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/engine.io-client/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"engine.io-client:webtransport\"); // debug()\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */ class WT extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        } catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed.then(()=>{\n            debug(\"transport closed gracefully\");\n            this.onClose();\n        }).catch((err)=>{\n            debug(\"transport closed due to %s\", err);\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(()=>{\n            this._transport.createBidirectionalStream().then((stream)=>{\n                const decoderStream = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.createPacketDecoderStream)(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.createPacketEncoderStream)();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = ()=>{\n                    reader.read().then(({ done, value })=>{\n                        if (done) {\n                            debug(\"session is closed\");\n                            return;\n                        }\n                        debug(\"received chunk: %o\", value);\n                        this.onPacket(value);\n                        read();\n                    }).catch((err)=>{\n                        debug(\"an error occurred while reading: %s\", err);\n                    });\n                };\n                read();\n                const packet = {\n                    type: \"open\"\n                };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(()=>this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for(let i = 0; i < packets.length; i++){\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(()=>{\n                if (lastPacket) {\n                    (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_1__.nextTick)(()=>{\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js":
/*!***************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/util.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   byteLength: () => (/* binding */ byteLength),\n/* harmony export */   installTimerFunctions: () => (/* binding */ installTimerFunctions),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   randomString: () => (/* binding */ randomString)\n/* harmony export */ });\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n\nfunction pick(obj, ...attr) {\n    return attr.reduce((acc, k)=>{\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.clearTimeout;\nfunction installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n    } else {\n        obj.setTimeoutFn = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.setTimeout.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n        obj.clearTimeoutFn = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.clearTimeout.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nfunction byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for(let i = 0, l = str.length; i < l; i++){\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        } else if (c < 0x800) {\n            length += 2;\n        } else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        } else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */ function randomString() {\n    return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\n");

/***/ })

};
;