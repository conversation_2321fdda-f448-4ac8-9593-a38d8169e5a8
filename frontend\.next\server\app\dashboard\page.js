(()=>{var e={};e.id=702,e.ids=[702],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},4300:e=>{"use strict";e.exports=require("buffer")},2081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},6224:e=>{"use strict";e.exports=require("tty")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},948:()=>{},4065:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>o});var a=s(482),r=s(9108),i=s(2563),n=s.n(i),l=s(8300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2225)),"D:\\Project\\wa\\frontend\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2917)),"D:\\Project\\wa\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Project\\wa\\frontend\\app\\dashboard\\page.tsx"],m="/dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4903:(e,t,s)=>{Promise.resolve().then(s.bind(s,8247))},2601:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,6840,23)),Promise.resolve().then(s.t.bind(s,8771,23)),Promise.resolve().then(s.t.bind(s,3225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,3982,23))},2576:()=>{},8247:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(2295),r=s(3729),i=s(7916),n=s(8272),l=s(1179),c=s(800);let o=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}),d=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),m=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),x=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),h=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});var u=s(8470);function p(){var e;let[t,s]=(0,r.useState)(null),[p,y]=(0,r.useState)(null),[j,f]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,u.io)("http://localhost:3000");y(e),e.on("connect",()=>{e.emit("request-stats")}),e.on("stats-update",e=>{s(e),f(!1)});let t=setInterval(()=>{e.emit("request-stats")},3e4);return()=>{e.close(),clearInterval(t)}},[]);let g=e=>`${(e/1024/1024).toFixed(1)} MB`,b=e=>new Date(e).toLocaleString("id-ID",{timeZone:"Asia/Jakarta",day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),v=async()=>{try{let e=await fetch("/api/clear-all-memory",{method:"POST"});(await e.json()).success&&p?.emit("request-stats")}catch(e){console.error("Error clearing memory:",e)}};return j?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(n.Z,{className:"w-8 h-8 animate-spin text-blue-500 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Memuat dashboard..."})]})}):a.jsx("div",{className:"min-h-screen p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Dashboard WhatsApp Bot AI"}),a.jsx("p",{className:"text-gray-600",children:"Monitor dan kelola bot WhatsApp Anda secara real-time"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Status Bot"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t?.botStatus==="ready"?a.jsx(l.Z,{className:"w-5 h-5 text-green-500"}):a.jsx(c.Z,{className:"w-5 h-5 text-yellow-500"}),a.jsx("span",{className:`font-semibold ${t?.botStatus==="ready"?"text-green-600":"text-yellow-600"}`,children:t?.botStatus==="ready"?"Aktif":"Tidak Aktif"})]})]}),a.jsx("div",{className:"p-3 bg-blue-50 rounded-xl",children:a.jsx(o,{className:"w-6 h-6 text-blue-500"})})]})}),a.jsx(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Pengguna Aktif"}),a.jsx("p",{className:"text-2xl font-bold text-gray-800",children:t?.totalActiveUsers||0})]}),a.jsx("div",{className:"p-3 bg-green-50 rounded-xl",children:a.jsx(d,{className:"w-6 h-6 text-green-500"})})]})}),a.jsx(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Uptime"}),a.jsx("p",{className:"text-2xl font-bold text-gray-800",children:t?.uptime?(e=t.uptime,`${Math.floor(e/3600)}j ${Math.floor(e%3600/60)}m`):"0j 0m"})]}),a.jsx("div",{className:"p-3 bg-purple-50 rounded-xl",children:a.jsx(m,{className:"w-6 h-6 text-purple-500"})})]})}),a.jsx(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"card",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Memory"}),a.jsx("p",{className:"text-2xl font-bold text-gray-800",children:t?.memory?g(t.memory.heapUsed):"0 MB"})]}),a.jsx("div",{className:"p-3 bg-orange-50 rounded-xl",children:a.jsx(x,{className:"w-6 h-6 text-orange-500"})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Manajemen Memory"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("p",{children:"Terakhir dibersihkan:"}),a.jsx("p",{className:"font-medium",children:t?.lastGlobalClear?b(t.lastGlobalClear):"Belum pernah"})]}),(0,a.jsxs)("button",{onClick:v,className:"btn-secondary w-full inline-flex items-center justify-center space-x-2",children:[a.jsx(h,{className:"w-4 h-4"}),a.jsx("span",{children:"Bersihkan Semua Memory"})]})]})]}),(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Informasi Sistem"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Heap Total:"}),a.jsx("span",{className:"font-medium",children:t?.memory?g(t.memory.heapTotal):"0 MB"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"RSS:"}),a.jsx("span",{className:"font-medium",children:t?.memory?g(t.memory.rss):"0 MB"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"External:"}),a.jsx("span",{className:"font-medium",children:t?.memory?g(t.memory.external):"0 MB"})]})]})]}),(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Aksi Cepat"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>p?.emit("request-stats"),className:"btn-primary w-full inline-flex items-center justify-center space-x-2",children:[a.jsx(n.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Refresh Data"})]}),a.jsx("a",{href:"/",className:"btn-secondary w-full inline-flex items-center justify-center space-x-2",children:a.jsx("span",{children:"Kembali ke QR Scanner"})})]})]})]}),t?.userList&&t.userList.length>0&&(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},className:"card",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Daftar Pengguna Aktif"}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[a.jsx("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Nomor"}),a.jsx("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Pesan"}),a.jsx("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Aktivitas Terakhir"}),a.jsx("th",{className:"text-left py-2 px-3 font-medium text-gray-600",children:"Bergabung"})]})}),a.jsx("tbody",{children:t.userList.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100",children:[a.jsx("td",{className:"py-2 px-3 font-mono text-xs",children:e.phone}),a.jsx("td",{className:"py-2 px-3",children:a.jsx("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs",children:e.messageCount})}),a.jsx("td",{className:"py-2 px-3 text-gray-600",children:b(e.lastActivity)}),a.jsx("td",{className:"py-2 px-3 text-gray-600",children:b(e.createdAt)})]},e.phone))})]})})]})]})})}},2225:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5036);let r=(0,s(6843).createProxy)(String.raw`D:\Project\wa\frontend\components\Dashboard.tsx`),{__esModule:i,$$typeof:n}=r,l=r.default;function c(){return a.jsx(l,{})}},2917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>n});var a=s(5036),r=s(265),i=s.n(r);s(7272);let n={title:"WhatsApp Bot AI - QR Scanner",description:"Professional WhatsApp Bot AI dengan interface modern untuk scan QR code",keywords:["whatsapp","bot","ai","qr","scanner"],authors:[{name:"WhatsApp Bot AI Team"}],viewport:"width=device-width, initial-scale=1"};function l({children:e}){return(0,a.jsxs)("html",{lang:"id",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("meta",{name:"theme-color",content:"#25D366"})]}),a.jsx("body",{className:i().className,children:a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",children:e})})]})}},7272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[869,270],()=>s(4065));module.exports=a})();