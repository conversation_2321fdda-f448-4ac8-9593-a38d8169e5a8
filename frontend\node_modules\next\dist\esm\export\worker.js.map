{"version": 3, "sources": ["../../src/export/worker.ts"], "names": ["process", "env", "NEXT_IS_EXPORT_WORKER", "extname", "join", "dirname", "sep", "fs", "loadComponents", "isDynamicRoute", "normalizePagePath", "requireFontManifest", "normalizeLocalePath", "trace", "setHttpClientAndAgentOptions", "isError", "addRequestMeta", "normalizeAppPath", "createRequestResponseMocks", "isAppRouteRoute", "hasNextSupport", "exportAppRoute", "exportAppPage", "exportPages", "getParams", "createIncrementalCache", "isPostpone", "isMissingPostponeDataError", "isDynamicUsageError", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "exportPageImpl", "input", "fileWriter", "dir", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeFonts", "optimizeCss", "disableOptimizedLoading", "debugOutput", "isrMemoryCacheSize", "fetchCache", "fetchCacheKeyPrefix", "incremental<PERSON>ache<PERSON>andlerPath", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "enabledDirectories", "__NEXT_EXPERIMENTAL_REACT", "page", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "query", "originalQuery", "req", "pathname", "isDynamic", "outDir", "params", "filePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "normalizedPage", "res", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "htmlFilename", "pageExt", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "htmlFilepath", "mkdir", "recursive", "incrementalCache", "experimental", "ppr", "flushToDisk", "undefined", "components", "isAppPath", "fontManifest", "supportsDynamicHTML", "originalPathname", "isRevalidate", "err", "console", "error", "stack", "exportPage", "httpAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "push", "exportPageSpan", "parentSpanId", "start", "Date", "now", "result", "traceAsyncFn", "duration", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "on"], "mappings": "AASA,OAAO,6BAA4B;AAEnCA,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAEpC,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,QAAQ,OAAM;AAClD,OAAOC,QAAQ,cAAa;AAC5B,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,KAAK,QAAQ,WAAU;AAChC,SAASC,4BAA4B,QAAQ,iCAAgC;AAC7E,OAAOC,aAAa,kBAAiB;AACrC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,gBAAgB,QAAQ,uCAAsC;AAEvE,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,cAAc,QAAQ,uBAAsB;AACrD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,UAAU,QAAQ,yCAAwC;AACnE,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,mCAAkC;AAEtE,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,eAAeC,eACbC,KAAsB,EACtBC,UAAsB;IAEtB,MAAM,EACJC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,aAAa,EACbC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,kBAAkB,EAClBC,UAAU,EACVC,mBAAmB,EACnBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EACnB,GAAGrB;IAEJ,IAAIkB,yBAAyB;QAC3BrD,QAAQC,GAAG,CAACwD,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,oGAAoG;IACpG,gDAAgD;IAChD,yCAAyC;IAEzC,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGzB;IAEJ,IAAI;YAwEoB0B;QAvEtB,IAAIF,QAAQ;YAAE,GAAGC,aAAa;QAAC;QAC/B,MAAME,WAAWjD,iBAAiByC;QAClC,MAAMS,YAAY1D,eAAeiD;QACjC,MAAMU,SAASR,WAAWxD,KAAKoC,SAAS,gBAAgBL,MAAMiC,MAAM;QAEpE,IAAIC;QAEJ,MAAMC,WAAW5D,kBAAkB4B;QACnC,MAAMiC,UAAU,CAAC,EAAED,SAAS,IAAI,CAAC;QACjC,IAAIE,gBAAgBD;QAEpB,IAAIE,cAAcV,MAAMW,aAAa,IAAIpC;QACzC,OAAOyB,MAAMW,aAAa;QAE1B,IAAIC,SAASZ,MAAMa,YAAY,IAAIzC,MAAM0C,UAAU,CAACF,MAAM;QAC1D,OAAOZ,MAAMa,YAAY;QAEzB,IAAIzC,MAAM0C,UAAU,CAACF,MAAM,EAAE;YAC3B,MAAMG,mBAAmBlE,oBACvB0B,MACAH,MAAM0C,UAAU,CAACE,OAAO;YAG1B,IAAID,iBAAiBE,cAAc,EAAE;gBACnCP,cAAcK,iBAAiBZ,QAAQ;gBACvCS,SAASG,iBAAiBE,cAAc;gBAExC,IAAIL,WAAWxC,MAAM0C,UAAU,CAACI,aAAa,EAAE;oBAC7CT,gBAAgB,CAAC,EAAE9D,kBAAkB+D,aAAa,IAAI,CAAC;gBACzD;YACF;QACF;QAEA,gEAAgE;QAChE,0DAA0D;QAC1D,MAAMS,qBAAqBC,OAAOC,IAAI,CAACpB,eAAeqB,MAAM,GAAG;QAE/D,iDAAiD;QACjD,MAAM,EAAEnB,UAAUoB,gBAAgB,EAAE,GAAG1E,oBACrC0B,MACAH,MAAM0C,UAAU,CAACE,OAAO;QAG1B,IAAIZ,aAAaT,SAAS4B,kBAAkB;YAC1C,MAAMC,iBAAiB3B,WAAW3C,iBAAiByC,QAAQA;YAE3DW,SAAS7C,UAAU+D,gBAAgBd;YACnC,IAAIJ,QAAQ;gBACVN,QAAQ;oBACN,GAAGA,KAAK;oBACR,GAAGM,MAAM;gBACX;YACF;QACF;QAEA,MAAM,EAAEJ,GAAG,EAAEuB,GAAG,EAAE,GAAGtE,2BAA2B;YAAEuE,KAAKhB;QAAY;QAEnE,6DAA6D;QAC7D,KAAK,MAAMiB,cAAc;YAAC;YAAK;SAAI,CAAE;YACnC,IACE;gBACE,CAAC,CAAC,EAAEA,WAAW,CAAC;gBAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;gBACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;aAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMnB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEiB,EAAE,CAAC,KAAKnB,cACxD;gBACAe,IAAIE,UAAU,GAAGA;YACnB;QACF;QAEA,+DAA+D;QAC/D,IAAInC,iBAAiB,GAACU,WAAAA,IAAIwB,GAAG,qBAAPxB,SAAS4B,QAAQ,CAAC,OAAM;YAC5C5B,IAAIwB,GAAG,IAAI;QACb;QAEA,IACEd,UACAjC,eACAP,MAAM0C,UAAU,CAACiB,aAAa,IAC9B3D,MAAM0C,UAAU,CAACiB,aAAa,CAACH,IAAI,CACjC,CAACI;gBACgCA;mBAA/BA,GAAGd,aAAa,KAAKN,YAAUoB,cAAAA,GAAGhB,OAAO,qBAAVgB,YAAYC,QAAQ,CAACrB,UAAU;YAElE;YACA3D,eAAeiD,KAAK,kBAAkB;QACxC;QAEApC,UAAUoE,SAAS,CAAC;YAClBtD;YACAuD,qBAAqB/D,MAAM0C,UAAU,CAACsB,aAAa;QACrD;QAEA,MAAMC,kBAAkB,CAACR,IACvBhD,aAAa,CAAC,EAAEgD,EAAE,EAAEtF,IAAI,UAAU,CAAC,GAAG,CAAC,EAAEsF,EAAE,KAAK,CAAC;QAEnD,IAAIS,eAAeD,gBAAgB9B;QAEnC,gFAAgF;QAChF,wBAAwB;QACxB,MAAMgC,UAAUnC,aAAaP,WAAW,KAAKzD,QAAQuD;QACrD,MAAM6C,UAAUpC,aAAaP,WAAW,KAAKzD,QAAQmC;QAErD,6CAA6C;QAC7C,IAAIA,SAAS,aAAa;YACxB+D,eAAe/D;QACjB,OAEK,IAAIgE,YAAYC,WAAWA,YAAY,IAAI;YAC9C,MAAMC,iBAAiB;gBAAC;gBAAQ;aAAO,CAACb,IAAI,CAC1C,CAACC,IAAMA,MAAMtD,QAAQsD,MAAMtD,OAAO;YAEpC,mFAAmF;YACnF,8CAA8C;YAC9C,MAAMmE,gBAAgB,CAACD,kBAAkBlE,KAAKuD,QAAQ,CAAC;YACvDQ,eAAeI,gBAAgBL,gBAAgB9D,QAAQA;QACzD,OAAO,IAAIA,SAAS,KAAK;YACvB,+CAA+C;YAC/C+D,eAAe;QACjB;QAEA,MAAMK,UAAUtG,KAAKgE,QAAQ/D,QAAQgG;QACrC,IAAIM,eAAevG,KAAKgE,QAAQiC;QAEhC,MAAM9F,GAAGqG,KAAK,CAACF,SAAS;YAAEG,WAAW;QAAK;QAE1C,mEAAmE;QACnE,gCAAgC;QAChC,MAAMC,mBACJlD,YAAYV,aACRzB,uBAAuB;YACrB2B;YACAH;YACAE;YACAX;YACAH;YACAmB;YACA,kCAAkC;YAClCuD,cAAc;gBAAEC,KAAK;YAAM;YAC3B,6DAA6D;YAC7D,+BAA+B;YAC/BC,aAAa,CAAC7F;QAChB,KACA8F;QAEN,qBAAqB;QACrB,IAAItD,YAAYzC,gBAAgBuC,OAAO;YACrC,OAAO,MAAMrC,eACX4C,KACAuB,KACAnB,QACAX,MACAoD,kBACAtE,SACAmE,cACAvE;QAEJ;QAEA,MAAM+E,aAAa,MAAM3G,eAAe;YACtCgC;YACAkB;YACA0D,WAAWxD;QACb;QAEA,MAAMiB,aAA+B;YACnC,GAAGsC,UAAU;YACb,GAAGhF,MAAM0C,UAAU;YACnBN,SAASC;YACTH;YACAxB;YACAC;YACAC;YACAsE,cAAcxE,gBAAgBlC,oBAAoB6B,WAAW;YAC7DmC;YACA2C,qBAAqB;YACrBC,kBAAkB7D;QACpB;QAEA,IAAItC,gBAAgB;YAClByD,WAAW2C,YAAY,GAAG;QAC5B;QAEA,mBAAmB;QACnB,IAAI5D,UAAU;YACZ,qEAAqE;YACrE,cAAc;YACdiB,WAAWiC,gBAAgB,GAAGA;YAE9B,OAAO,MAAMxF,cACX2C,KACAuB,KACA9B,MACApB,MACA4B,UACAH,OACAc,YACA8B,cACA3D,aACAc,gBACA1B;QAEJ;QAEA,OAAO,MAAMb,YACX0C,KACAuB,KACAlD,MACAoB,MACAK,OACA4C,cACAN,cACA9B,SACA3B,YACAwB,QACAd,kBACAb,cACAC,aACAyB,WACAe,oBACAL,YACAsC,YACA/E;IAEJ,EAAE,OAAOqF,KAAK;QACZ,sFAAsF;QACtF,IAAI,CAAC9F,2BAA2B8F,MAAM;YACpCC,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAErF,KAAK,gEAAgE,CAAC,GAC1GvB,CAAAA,QAAQ0G,QAAQA,IAAIG,KAAK,GAAGH,IAAIG,KAAK,GAAGH,GAAE;QAEjD;QAEA,OAAO;YAAEE,OAAO;QAAK;IACvB;AACF;AAEA,eAAe,eAAeE,WAC5B1F,KAAsB;IAEtB,4BAA4B;IAC5BrB,6BAA6B;QAC3BgH,kBAAkB3F,MAAM2F,gBAAgB;IAC1C;IAEA,MAAMC,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACA3F,MACA4F,SACAC,kBAAkB,OAAO;QAEzB,MAAM5H,GAAGqG,KAAK,CAACvG,QAAQiC,OAAO;YAAEuE,WAAW;QAAK;QAChD,MAAMtG,GAAG6H,SAAS,CAAC9F,MAAM4F,SAASC;QAClCJ,MAAMM,IAAI,CAAC;YAAEJ;YAAM3F;QAAK;IAC1B;IAEA,MAAMgG,iBAAiBzH,MAAM,sBAAsBsB,MAAMoG,YAAY;IAErE,MAAMC,QAAQC,KAAKC,GAAG;IAEtB,mBAAmB;IACnB,MAAMC,SAAS,MAAML,eAAeM,YAAY,CAAC;QAC/C,OAAO,MAAM1G,eAAeC,OAAO6F;IACrC;IAEA,kDAAkD;IAClD,IAAI,CAACW,QAAQ;IAEb,iDAAiD;IACjD,IAAI,WAAWA,QAAQ;QACrB,OAAO;YAAEhB,OAAOgB,OAAOhB,KAAK;YAAEkB,UAAUJ,KAAKC,GAAG,KAAKF;YAAOT,OAAO,EAAE;QAAC;IACxE;IAEA,sCAAsC;IACtC,OAAO;QACLc,UAAUJ,KAAKC,GAAG,KAAKF;QACvBT;QACAe,gBAAgBH,OAAOG,cAAc;QACrCC,YAAYJ,OAAOI,UAAU;QAC7BC,UAAUL,OAAOK,QAAQ;QACzBC,aAAaN,OAAOM,WAAW;QAC/BC,iBAAiBP,OAAOO,eAAe;QACvCC,cAAcR,OAAOQ,YAAY;IACnC;AACF;AAEAnJ,QAAQoJ,EAAE,CAAC,sBAAsB,CAAC3B;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAI/F,WAAW+F,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAI7F,oBAAoB6F,MAAM;QAC5B;IACF;IAEAC,QAAQC,KAAK,CAACF;AAChB;AAEAzH,QAAQoJ,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D"}