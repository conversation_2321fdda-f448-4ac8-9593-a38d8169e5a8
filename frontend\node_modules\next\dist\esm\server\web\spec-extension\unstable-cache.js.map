{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["staticGenerationAsyncStorage", "_staticGenerationAsyncStorage", "CACHE_ONE_YEAR", "addImplicitTags", "validateTags", "unstable_cache", "cb", "keyParts", "options", "fetch", "__nextGetStaticStore", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "postpone", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isUnstableCacheCallback", "isStaticGeneration", "tags", "tag", "includes", "push", "implicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "kindHint", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": "AAKA,SAASA,gCAAgCC,6BAA6B,QAAQ,sEAAqE;AACnJ,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,eAAe,EAAEC,YAAY,QAAQ,wBAAuB;AAIrE,OAAO,SAASC,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMR,+BACJ,CAAA,AAACS,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CR;IAE7C,IAAIO,QAAQG,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAEN,GAAGO,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QACJhB,gDAAAA,6BAA8BiB,QAAQ;QAExC,IAAID,SAAS,OAAOR,QAAQG,UAAU,KAAK,UAAU;YACnD,yEAAyE;YACzE,IAAIH,QAAQG,UAAU,KAAK,GAAG;gBAC5B,0DAA0D;gBAC1DK,MAAME,QAAQ,oBAAdF,MAAME,QAAQ,MAAdF,OAAiB;gBAEjB,+BAA+B;gBAC/BA,MAAML,UAAU,GAAG;YACnB,gIAAgI;YAClI,OAAO,IAAI,OAAOK,MAAML,UAAU,KAAK,UAAU;gBAC/C,IAAIK,MAAML,UAAU,GAAGH,QAAQG,UAAU,EAAE;oBACzCK,MAAML,UAAU,GAAGH,QAAQG,UAAU;gBACvC;YACA,oDAAoD;YACtD,OAAO;gBACLK,MAAML,UAAU,GAAGH,QAAQG,UAAU;YACvC;QACF;QAEA,MAAMQ,mBAGJH,CAAAA,yBAAAA,MAAOG,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIP,MACR,CAAC,sDAAsD,EAAEN,GAAGO,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMS,YAAY,CAAC,EAAEhB,GAAGO,QAAQ,GAAG,CAAC,EAClCU,MAAMC,OAAO,CAACjB,aAAaA,SAASkB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACZ,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOf,6BAA6B4B,GAAG,CACrC;YACE,GAAGZ,KAAK;YACR,8DAA8D;YAC9D,8CAA8C;YAC9Ca,YAAY;YACZC,aAAad,CAAAA,yBAAAA,MAAOc,WAAW,KAAI;YACnCC,yBAAyB;YACzBC,oBAAoBhB,CAAAA,yBAAAA,MAAOgB,kBAAkB,MAAK;YAClDd,QAAQ,EAAEF,yBAAAA,MAAOE,QAAQ;QAC3B,GACA;YACE,MAAMe,OAAO7B,aACXI,QAAQyB,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAE3B,GAAGO,QAAQ,GAAG,CAAC;YAGnC,IAAIU,MAAMC,OAAO,CAACS,SAASjB,OAAO;gBAChC,IAAI,CAACA,MAAMiB,IAAI,EAAE;oBACfjB,MAAMiB,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAMC,OAAOD,KAAM;oBACtB,IAAI,CAACjB,MAAMiB,IAAI,CAACE,QAAQ,CAACD,MAAM;wBAC7BlB,MAAMiB,IAAI,CAACG,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAerB,QAAQb,gBAAgBa,SAAS,EAAE;YAExD,MAAMsB,WAAW,OAAMnB,oCAAAA,iBAAkBoB,aAAa,CAACjB;YACvD,MAAMkB,aACJF,YACA,sDAAsD;YACtD,4CAA4C;YAC5CtB,CAAAA,yBAAAA,MAAOa,UAAU,MAAK,oBACtB,CACEb,CAAAA,CAAAA,yBAAAA,MAAOyB,oBAAoB,KAAItB,iBAAiBsB,oBAAoB,AAAD,KAEpE,OAAMtB,oCAAAA,iBAAkBuB,GAAG,CAACJ,UAAU;gBACrCK,UAAU;gBACVhC,YAAYH,QAAQG,UAAU;gBAC9BsB;gBACAW,UAAUP;YACZ;YAEF,MAAMQ,iBAAiB;gBACrB,MAAMC,SAAS,MAAMxC,MAAMS;gBAE3B,IAAIuB,YAAYnB,kBAAkB;oBAChC,MAAMA,iBAAiB4B,GAAG,CACxBT,UACA;wBACEU,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAMzB,KAAKC,SAAS,CAACmB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACA1C,YACE,OAAOH,QAAQG,UAAU,KAAK,WAC1BT,iBACAM,QAAQG,UAAU;oBAC1B,GACA;wBACEA,YAAYH,QAAQG,UAAU;wBAC9BkB,YAAY;wBACZI;oBACF;gBAEJ;gBACA,OAAOa;YACT;YAEA,IAAI,CAACN,cAAc,CAACA,WAAWc,KAAK,EAAE;gBACpC,OAAOT;YACT;YAEA,IAAIL,WAAWc,KAAK,CAACN,IAAI,KAAK,SAAS;gBACrCO,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAElC,UAAU,CAAC;gBAE1D,OAAOuB;YACT;YACA,IAAIY;YACJ,MAAMC,UAAUlB,WAAWkB,OAAO;YAElC,IAAIlB,YAAY;gBACd,MAAMmB,UAAUnB,WAAWc,KAAK,CAACL,IAAI;gBACrCQ,cAAc/B,KAAKkC,KAAK,CAACD,QAAQR,IAAI;YACvC;YAEA,IAAIO,SAAS;gBACX,IAAI,CAAC1C,OAAO;oBACV,OAAO6B;gBACT,OAAO;oBACL,IAAI,CAAC7B,MAAM6C,kBAAkB,EAAE;wBAC7B7C,MAAM6C,kBAAkB,GAAG,CAAC;oBAC9B;oBACA7C,MAAM6C,kBAAkB,CAACvC,UAAU,GAAGuB,iBAAiBiB,KAAK,CAC1D,CAACC,MACCR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAElC,UAAU,CAAC,EAAEyC;gBAEjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAO3C;AACT"}