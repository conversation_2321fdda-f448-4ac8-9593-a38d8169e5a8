exports.id=270,exports.ids=[270],exports.modules={2466:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(9687)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},9687:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(8476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},7978:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(2466):e.exports=s(7854)},7854:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(9687)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},814:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),i=t.indexOf("--");return -1!==r&&(-1===i||r<i)}},8476:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,i,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===n&&isFinite(e))return s.long?(r=Math.abs(e))>=864e5?t(e,r,864e5,"day"):r>=36e5?t(e,r,36e5,"hour"):r>=6e4?t(e,r,6e4,"minute"):r>=1e3?t(e,r,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},2295:(e,t,s)=>{"use strict";e.exports=s(6372).vendored["react-ssr"].ReactJsxRuntime},1938:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(4889)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},4889:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(8476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},4974:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(1938):e.exports=s(575)},575:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(4889)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},1393:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(2701)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},2701:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(8476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},2428:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(1393):e.exports=s(5919)},5919:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(2701)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},125:(e,t,s)=>{"use strict";let r;let i=s(2037),n=s(6224),o=s(814),{env:a}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function h(e,t){if(0===r)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===r)return 0;let s=r||0;if("dumb"===a.TERM)return s;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:s;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:s}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),"FORCE_COLOR"in a&&(r="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return l(h(e,e&&e.isTTY))},stdout:l(h(!0,n.isatty(1))),stderr:l(h(!0,n.isatty(2)))}},4602:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(2710),i=Buffer[Symbol.species];function n(e,t,s,r,i){for(let n=0;n<i;n++)s[r+n]=e[n]^t[3&n]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,n),n+=r.length}return n<t?new i(s.buffer,s.byteOffset,n):s},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new i(t):ArrayBuffer.isView(t)?s=new i(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(8359);e.exports.mask=function(e,s,r,i,o){o<48?n(e,s,r,i,o):t.mask(e,s,r,i,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},2710:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},4208:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:i}=s(2710),n=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),h=Symbol("kReason"),c=Symbol("kTarget"),u=Symbol("kType"),d=Symbol("kWasClean");class p{constructor(e){this[c]=null,this[u]=e}get target(){return this[c]}get type(){return this[u]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[h]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[h]}get wasClean(){return this[d]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function y(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:m,Event:p,EventTarget:{addEventListener(e,t,s={}){let n;for(let n of this.listeners(e))if(!s[r]&&n[i]===t&&!n[r])return;if("message"===e)n=function(e,s){let r=new g("message",{data:s?e:e.toString()});r[c]=this,y(t,this,r)};else if("close"===e)n=function(e,s){let r=new f("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[c]=this,y(t,this,r)};else if("error"===e)n=function(e){let s=new m("error",{error:e,message:e.message});s[c]=this,y(t,this,s)};else{if("open"!==e)return;n=function(){let e=new p("open");e[c]=this,y(t,this,e)}}n[r]=!!s[r],n[i]=t,s.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[i]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:g}},8581:(e,t,s)=>{"use strict";let{tokenChars:r}=s(8591);function i(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let n=Object.create(null),o=Object.create(null),a=!1,l=!1,h=!1,c=-1,u=-1,d=-1,p=0;for(;p<e.length;p++)if(u=e.charCodeAt(p),void 0===t){if(-1===d&&1===r[u])-1===c&&(c=p);else if(0!==p&&(32===u||9===u))-1===d&&-1!==c&&(d=p);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let s=e.slice(c,d);44===u?(i(n,s,o),o=Object.create(null)):t=s,c=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`)}else if(void 0===s){if(-1===d&&1===r[u])-1===c&&(c=p);else if(32===u||9===u)-1===d&&-1!==c&&(d=p);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p),i(o,e.slice(c,d),!0),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),c=d=-1}else if(61===u&&-1!==c&&-1===d)s=e.slice(c,p),c=d=-1;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(l){if(1!==r[u])throw SyntaxError(`Unexpected character at index ${p}`);-1===c?c=p:a||(a=!0),l=!1}else if(h){if(1===r[u])-1===c&&(c=p);else if(34===u&&-1!==c)h=!1,d=p;else if(92===u)l=!0;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(34===u&&61===e.charCodeAt(p-1))h=!0;else if(-1===d&&1===r[u])-1===c&&(c=p);else if(-1!==c&&(32===u||9===u))-1===d&&(d=p);else if(59===u||44===u){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let r=e.slice(c,d);a&&(r=r.replace(/\\/g,""),a=!1),i(o,s,r),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),s=void 0,c=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===c||h||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===d&&(d=p);let f=e.slice(c,d);return void 0===t?i(n,f,o):(void 0===s?i(o,f,!0):a?i(o,s,f.replace(/\\/g,"")):i(o,s,f),i(n,t,o)),n}}},4059:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},8617:(e,t,s)=>{"use strict";let r;let i=s(9796),n=s(4602),o=s(4059),{kStatusCode:a}=s(2710),l=Buffer[Symbol.species],h=Buffer.from([0,0,255,255]),c=Symbol("permessage-deflate"),u=Symbol("total-length"),d=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class m{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[c]=this,this._inflate[u]=0,this._inflate[p]=[],this._inflate.on("error",v),this._inflate.on("data",y)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(h),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,s(e);return}let i=n.concat(this._inflate[p],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,i)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[p]=[],this._deflate.on("data",g)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[p],this._deflate[u]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[u]=0,this._deflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function g(e){this[p].push(e),this[u]+=e.length}function y(e){if(this[u]+=e.length,this[c]._maxPayload<1||this[u]<=this[c]._maxPayload){this[p].push(e);return}this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",y),this.reset()}function v(e){this[c]._inflate=null,e[a]=1007,this[d](e)}e.exports=m},1250:(e,t,s)=>{"use strict";let{Writable:r}=s(2781),i=s(8617),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=s(2710),{concat:h,toArrayBuffer:c,unmask:u}=s(4602),{isValidStatusCode:d,isValidUTF8:p}=s(8591),f=Buffer[Symbol.species];class m extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new f(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[i.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=4294967296*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?h(s,t):"arraybuffer"===this._binaryType?c(h(s,t)):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=h(s,t);if(!this._skipUTF8Validation&&!p(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!d(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,i){this._loop=!1,this._errored=!0;let n=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=i,n[a]=r,n}}e.exports=m},8022:(e,t,s)=>{"use strict";let r;let{Duplex:i}=s(2781),{randomFillSync:n}=s(6113),o=s(8617),{EMPTY_BUFFER:a}=s(2710),{isValidStatusCode:l}=s(8591),{mask:h,toBuffer:c}=s(4602),u=Symbol("kByteLength"),d=Buffer.alloc(4),p=8192;class f{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,i;let o=!1,a=2,l=!1;t.mask&&(s=t.maskBuffer||d,t.generateMask?t.generateMask(s):(8192===p&&(void 0===r&&(r=Buffer.alloc(8192)),n(r,0,8192),p=0),s[0]=r[p++],s[1]=r[p++],s[2]=r[p++],s[3]=r[p++]),l=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?i=(!t.mask||l)&&void 0!==t[u]?t[u]:(e=Buffer.from(e)).length:(i=e.length,o=t.mask&&t.readOnly&&!l);let c=i;i>=65536?(a+=8,c=127):i>125&&(a+=2,c=126);let f=Buffer.allocUnsafe(o?i+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=c,126===c?f.writeUInt16BE(i,2):127===c&&(f[2]=f[3]=0,f.writeUIntBE(i,4,6)),t.mask)?(f[1]|=128,f[a-4]=s[0],f[a-3]=s[1],f[a-2]=s[2],f[a-1]=s[3],l)?[f,e]:o?(h(e,s,f,a,i),[f]):(h(e,s,e,0,i),[f,e]):[f,e]}close(e,t,s,r){let i;if(void 0===e)i=a;else if("number"==typeof e&&l(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let n={[u]:i.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,i,!1,n,r]):this.sendFrame(f.frame(i,n),r)}ping(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=c(e)).length,i=c.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}pong(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=c(e)).length,i=c.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}send(e,t,s){let r,i;let n=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=c(e)).length,i=c.readOnly),this._firstFragment?(this._firstFragment=!1,l&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=r>=n._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0),n){let n={[u]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:l};this._deflating?this.enqueue([this.dispatch,e,this._compress,n,s]):this.dispatch(e,this._compress,n,s)}else this.sendFrame(f.frame(e,{[u]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:!1}),s)}dispatch(e,t,s,r){if(!t){this.sendFrame(f.frame(e,s),r);return}let i=this._extensions[o.extensionName];this._bufferedBytes+=s[u],this._deflating=!0,i.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof r&&r(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],r=s[s.length-1];"function"==typeof r&&r(e)}return}this._bufferedBytes-=s[u],this._deflating=!1,s.readOnly=!1,this.sendFrame(f.frame(t,s),r),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][u],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][u],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},9057:(e,t,s)=>{"use strict";let{Duplex:r}=s(2781);function i(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(i,a);return}let n=!1;e.once("error",function(e){n=!0,r(e)}),e.once("close",function(){n||r(t),process.nextTick(i,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",n),a.on("error",o),a}},5626:(e,t,s)=>{"use strict";let{tokenChars:r}=s(8591);e.exports={parse:function(e){let t=new Set,s=-1,i=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===i&&1===r[o])-1===s&&(s=n);else if(0!==n&&(32===o||9===o))-1===i&&-1!==s&&(i=n);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${n}`);-1===i&&(i=n);let r=e.slice(s,i);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=i=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===s||-1!==i)throw SyntaxError("Unexpected end of input");let o=e.slice(s,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},8591:(e,t,s)=>{"use strict";let{isUtf8:r}=s(4300);function i(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?i(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(948);e.exports.isValidUTF8=function(e){return e.length<32?i(e):t(e)}}catch(e){}},3054:(e,t,s)=>{"use strict";let r=s(2361),i=s(3685),{Duplex:n}=s(2781),{createHash:o}=s(6113),a=s(8581),l=s(8617),h=s(5626),c=s(6458),{GUID:u,kWebSocket:d}=s(2710),p=/^[+/0-9A-Za-z]{22}==$/;class f extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:c,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let s=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",g);let i=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){v(this,e,t,405,"Invalid HTTP method");return}if(void 0===n||"websocket"!==n.toLowerCase()){v(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!p.test(i)){v(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){v(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){y(t,400);return}let c=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==c)try{u=h.parse(c)}catch(s){v(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==d){let s=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(d);e[l.extensionName]&&(s.accept(e[l.extensionName]),f[l.extensionName]=s)}catch(s){v(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(n,(n,o,a,l)=>{if(!n)return y(t,o||401,a,l);this.completeUpgrade(f,i,u,e,t,s,r)});return}if(!this.options.verifyClient(n))return y(t,401)}this.completeUpgrade(f,i,u,e,t,s,r)}completeUpgrade(e,t,s,r,i,n,h){if(!i.readable||!i.writable)return i.destroy();if(i[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(i,503);let c=o("sha1").update(t+u).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${c}`],f=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,s=a.format({[l.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${s}`),f._extensions=e}this.emit("headers",p,r),i.write(p.concat("\r\n").join("\r\n")),i.removeListener("error",g),f.setSocket(i,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),h(f,r)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function y(e,t,s,r){s=s||i.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function v(e,t,s,r,i){if(e.listenerCount("wsClientError")){let r=Error(i);Error.captureStackTrace(r,v),e.emit("wsClientError",r,s,t)}else y(s,r,i)}e.exports=f},6458:(e,t,s)=>{"use strict";let r=s(2361),i=s(5687),n=s(3685),o=s(1808),a=s(4404),{randomBytes:l,createHash:h}=s(6113),{Duplex:c,Readable:u}=s(2781),{URL:d}=s(7310),p=s(8617),f=s(1250),m=s(8022),{BINARY_TYPES:g,EMPTY_BUFFER:y,GUID:v,kForOnEventAttribute:b,kListener:_,kStatusCode:C,kWebSocket:x,NOOP:w}=s(2710),{EventTarget:{addEventListener:E,removeEventListener:k}}=s(4208),{format:S,parse:T}=s(8581),{toBuffer:O}=s(4602),P=Symbol("kAborted"),A=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],F=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class L extends r{constructor(e,t,s){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=L.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,o){let a,c,u,f;let m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:A[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!A.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${A.join(", ")})`);if(s instanceof d)a=s;else try{a=new d(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||y?y&&!a.pathname?c="The URL's pathname is empty":a.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',c){let e=SyntaxError(c);if(0===t._redirects)throw e;D(t,e);return}let b=g?443:80,_=l(16).toString("base64"),C=g?i.request:n.request,x=new Set;if(m.createConnection=m.createConnection||(g?B:N),m.defaultPort=m.defaultPort||b,m.port=a.port||b,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":_,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(u=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=S({[p.extensionName]:u.offer()})),r.length){for(let e of r){if("string"!=typeof e||!F.test(e)||x.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");x.add(e)}m.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),y){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=g,t._originalHostOrSocketPath=y?m.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),f=t._req=C(m),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=C(m);m.timeout&&f.on("timeout",()=>{M(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[P]||(f=t._req=null,D(t,e))}),f.on("response",i=>{let n=i.headers.location,a=i.statusCode;if(n&&m.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>m.maxRedirects){M(t,f,"Maximum redirects exceeded");return}f.abort();try{i=new d(n,s)}catch(e){D(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,i,r,o)}else t.emit("unexpected-response",f,i)||M(t,f,`Unexpected server response: ${i.statusCode}`)}),f.on("upgrade",(e,s,r)=>{let i;if(t.emit("upgrade",e),t.readyState!==L.CONNECTING)return;f=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase()){M(t,s,"Invalid Upgrade header");return}let o=h("sha1").update(_+v).digest("base64");if(e.headers["sec-websocket-accept"]!==o){M(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?x.size?x.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":x.size&&(i="Server sent no subprotocol"),i){M(t,s,i);return}a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!u){M(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=T(l)}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==p.extensionName){M(t,s,"Server indicated an extension that was not requested");return}try{u.accept(e[p.extensionName])}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=u}t.setSocket(s,r,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(f,t):f.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new f({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new m(e,this._extensions,s.generateMask),this._receiver=r,this._socket=e,r[x]=this,e[x]=this,r.on("conclude",j),r.on("drain",I),r.on("error",U),r.on("message",$),r.on("ping",q),r.on("pong",H),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",G),e.on("data",Z),e.on("end",Y),e.on("error",X),this._readyState=L.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===L.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=L.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){V(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,s)}pong(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){V(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,s)}resume(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){V(this,e,s);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(r.compress=!1),this._sender.send(e||y,r,s)}terminate(){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=L.CLOSING,this._socket.destroy())}}}function D(e,t){e._readyState=L.CLOSING,e.emit("error",t),e.emitClose()}function N(e){return e.path=e.socketPath,o.connect(e)}function B(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function M(e,t,s){e._readyState=L.CLOSING;let r=Error(s);Error.captureStackTrace(r,M),t.setHeader?(t[P]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(D,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function V(e,t,s){if(t){let s=O(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function j(e,t){let s=this[x];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[x]&&(s._socket.removeListener("data",Z),process.nextTick(z,s._socket),1005===e?s.close():s.close(e,t))}function I(){let e=this[x];e.isPaused||e._socket.resume()}function U(e){let t=this[x];void 0!==t._socket[x]&&(t._socket.removeListener("data",Z),process.nextTick(z,t._socket),t.close(e[C])),t.emit("error",e)}function W(){this[x].emitClose()}function $(e,t){this[x].emit("message",e,t)}function q(e){let t=this[x];t._autoPong&&t.pong(e,!this._isServer,w),t.emit("ping",e)}function H(e){this[x].emit("pong",e)}function z(e){e.resume()}function G(){let e;let t=this[x];this.removeListener("close",G),this.removeListener("data",Z),this.removeListener("end",Y),t._readyState=L.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[x]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",W),t._receiver.on("finish",W))}function Z(e){this[x]._receiver.write(e)||this.pause()}function Y(){let e=this[x];e._readyState=L.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[x];this.removeListener("error",X),this.on("error",w),e&&(e._readyState=L.CLOSING,this.destroy())}Object.defineProperty(L,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(L.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(L,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(L.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(L,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(L.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(L,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(L.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(L.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(L.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[_];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),L.prototype.addEventListener=E,L.prototype.removeEventListener=k,e.exports=L},5574:(e,t,s)=>{/**
 * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.
 *
 * This can be used with JS designed for browsers to improve reuse of code and
 * allow the use of existing libraries.
 *
 * Usage: include("XMLHttpRequest.js") and use XMLHttpRequest per W3C specs.
 *
 * <AUTHOR> DeFelippi <<EMAIL>>
 * @contributor David Ellis <<EMAIL>>
 * @license MIT
 */var r=s(7147),i=s(7310),n=s(2081).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,l=s(3685),h=s(5687),c={},u=!1,d={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},d),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],m=["TRACE","TRACK","CONNECT"],g=!1,y=!1,v=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,s,r,i){if(this.abort(),y=!1,v=!1,!(e&&-1===m.indexOf(e)))throw Error("SecurityError: Request method not allowed");c={method:e,url:t.toString(),async:"boolean"!=typeof s||s,user:r||null,password:i||null},_(this.OPENED)},this.setDisableHeaderCheck=function(e){u=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!u&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(g)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!y?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||y)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(s){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(g)throw Error("INVALID_STATE_ERR: send has already been called");var u,d=!1,f=!1,m=i.parse(c.url);switch(m.protocol){case"https:":d=!0;case"http:":u=m.hostname;break;case"file:":f=!0;break;case void 0:case"":u="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==c.method)throw Error("XMLHttpRequest: Only GET method is supported");if(c.async)r.readFile(unescape(m.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,_(a.DONE))});else try{this.response=r.readFileSync(unescape(m.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,_(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var v=m.port||(d?443:80),b=m.pathname+(m.search?m.search:"");if(p.Host=u,d&&443===v||80===v||(p.Host+=":"+m.port),c.user){void 0===c.password&&(c.password="");var C=new Buffer(c.user+":"+c.password);p.Authorization="Basic "+C.toString("base64")}"GET"===c.method||"HEAD"===c.method?s=null:s?(p["Content-Length"]=Buffer.isBuffer(s)?s.length:Buffer.byteLength(s),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===c.method&&(p["Content-Length"]=0);var x=e.agent||!1,w={host:u,port:v,path:b,method:c.method,headers:p,agent:x};if(d&&(w.pfx=e.pfx,w.key=e.key,w.passphrase=e.passphrase,w.cert=e.cert,w.ca=e.ca,w.ciphers=e.ciphers,w.rejectUnauthorized=!1!==e.rejectUnauthorized),y=!1,c.async){var E=d?h.request:l.request;g=!0,a.dispatchEvent("readystatechange");var k=function(s){if(302===(o=s).statusCode||303===o.statusCode||307===o.statusCode){c.url=o.headers.location;var r=i.parse(c.url);u=r.hostname;var n={hostname:r.hostname,port:r.port,path:r.path,method:303===o.statusCode?"GET":c.method,headers:p};d&&(n.pfx=e.pfx,n.key=e.key,n.passphrase=e.passphrase,n.cert=e.cert,n.ca=e.ca,n.ciphers=e.ciphers,n.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=E(n,k).on("error",S)).end();return}_(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}g&&_(a.LOADING)}),o.on("end",function(){g&&(g=!1,_(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},S=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return E(w,k).on("error",S);a.handleError(e)};t=E(w,k).on("error",S),e.autoUnref&&t.on("socket",e=>{e.unref()}),s&&t.write(s),t.end(),a.dispatchEvent("loadstart")}else{var T=".node-xmlhttprequest-content-"+process.pid,O=".node-xmlhttprequest-sync-"+process.pid;r.writeFileSync(O,"","utf8");for(var P="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(d?"s":"")+".request;var options = "+JSON.stringify(w)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+T+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+O+"');});response.on('error', function(error) {fs.writeFileSync('"+T+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+O+"');});}).on('error', function(error) {fs.writeFileSync('"+T+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+O+"');});"+(s?"req.write('"+JSON.stringify(s).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",A=n(process.argv[0],["-e",P]);r.existsSync(O););if(a.responseText=r.readFileSync(T,"utf8"),A.stdin.end(),r.unlinkSync(T),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var R=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(R,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var F=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:F.data.headers},a.responseText=F.data.text,a.response=Buffer.from(F.data.data,"base64"),_(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,y=!0,_(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},d),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),y=v=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||g)&&this.readyState!==this.DONE&&(g=!1,_(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&c.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,s=b[e].length;t<s;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var _=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!v)&&(a.readyState=e,(c.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=v?"abort":y?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},8272:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(3729);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},1179:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(3729);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},800:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(3729);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},6828:(e,t,s)=>{"use strict";s.d(t,{p:()=>r});let r=(0,s(3729).createContext)({})},5986:(e,t,s)=>{"use strict";s.d(t,{O:()=>r});let r=(0,s(3729).createContext)(null)},228:(e,t,s)=>{"use strict";s.d(t,{Pn:()=>a,Wi:()=>o,frameData:()=>l,S6:()=>h});var r=s(254);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let n=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:h}=function(e,t){let s=!1,r=!0,o={delta:0,timestamp:0,isProcessing:!1},a=n.reduce((e,t)=>(e[t]=function(e){let t=new i,s=new i,r=0,n=!1,o=!1,a=new WeakSet,l={schedule:(e,i=!1,o=!1)=>{let l=o&&n,h=l?t:s;return i&&a.add(e),h.add(e)&&l&&n&&(r=t.order.length),e},cancel:e=>{s.remove(e),a.delete(e)},process:i=>{if(n){o=!0;return}if(n=!0,[t,s]=[s,t],s.clear(),r=t.order.length)for(let s=0;s<r;s++){let r=t.order[s];r(i),a.has(r)&&(l.schedule(r),e())}n=!1,o&&(o=!1,l.process(i))}};return l}(()=>s=!0),e),{}),l=e=>a[e].process(o),h=()=>{let i=performance.now();s=!1,o.delta=r?1e3/60:Math.max(Math.min(i-o.timestamp,40),1),o.timestamp=i,o.isProcessing=!0,n.forEach(l),o.isProcessing=!1,s&&t&&(r=!1,e(h))},c=()=>{s=!0,r=!0,o.isProcessing||e(h)};return{schedule:n.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,i=!1)=>(s||c(),r.schedule(e,t,i)),e},{}),cancel:e=>n.forEach(t=>a[t].cancel(e)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r.Z,!0)},7916:(e,t,s)=>{"use strict";s.d(t,{E:()=>iW});var r=s(3729);let i=(0,r.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),n=(0,r.createContext)({});var o=s(5986),a=s(9038);let l=(0,r.createContext)({strict:!1}),h=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),c="data-"+h("framerAppearId");function u(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function d(e){return"string"==typeof e||Array.isArray(e)}function p(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let f=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...f];function g(e){return p(e.animate)||m.some(t=>d(e[t]))}function y(e){return!!(g(e)||e.variants)}function v(e){return Array.isArray(e)?e.join(" "):e}let b={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},_={};for(let e in b)_[e]={isEnabled:t=>b[e].some(e=>!!t[e])};var C=s(9398),x=s(6828);let w=(0,r.createContext)({}),E=Symbol.for("motionComponentSymbol"),k=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function S(e){if("string"!=typeof e||e.includes("-"));else if(k.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let T={},O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(O);function A(e,{layout:t,layoutId:s}){return P.has(e)||e.startsWith("origin")||(t||void 0!==s)&&(!!T[e]||"opacity"===e)}let R=e=>!!(e&&e.getVelocity),F={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},L=O.length,D=e=>t=>"string"==typeof t&&t.startsWith(e),N=D("--"),B=D("var(--"),M=(e,t)=>t&&"number"==typeof e?t.transform(e):e,V=(e,t,s)=>Math.min(Math.max(s,e),t),j={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...j,transform:e=>V(0,1,e)},U={...j,default:1},W=e=>Math.round(1e5*e)/1e5,$=/(-)?([\d]*\.?[\d])+/g,q=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,H=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function z(e){return"string"==typeof e}let G=e=>({test:t=>z(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),Z=G("deg"),Y=G("%"),X=G("px"),K=G("vh"),J=G("vw"),Q={...Y,parse:e=>Y.parse(e)/100,transform:e=>Y.transform(100*e)},ee={...j,transform:Math.round},et={borderWidth:X,borderTopWidth:X,borderRightWidth:X,borderBottomWidth:X,borderLeftWidth:X,borderRadius:X,radius:X,borderTopLeftRadius:X,borderTopRightRadius:X,borderBottomRightRadius:X,borderBottomLeftRadius:X,width:X,maxWidth:X,height:X,maxHeight:X,size:X,top:X,right:X,bottom:X,left:X,padding:X,paddingTop:X,paddingRight:X,paddingBottom:X,paddingLeft:X,margin:X,marginTop:X,marginRight:X,marginBottom:X,marginLeft:X,rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:Z,skewX:Z,skewY:Z,distance:X,translateX:X,translateY:X,translateZ:X,x:X,y:X,z:X,perspective:X,transformPerspective:X,opacity:I,originX:Q,originY:Q,originZ:X,zIndex:ee,fillOpacity:I,strokeOpacity:I,numOctaves:ee};function es(e,t,s,r){let{style:i,vars:n,transform:o,transformOrigin:a}=e,l=!1,h=!1,c=!0;for(let e in t){let s=t[e];if(N(e)){n[e]=s;continue}let r=et[e],u=M(s,r);if(P.has(e)){if(l=!0,o[e]=u,!c)continue;s!==(r.default||0)&&(c=!1)}else e.startsWith("origin")?(h=!0,a[e]=u):i[e]=u}if(!t.transform&&(l||r?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:s=!0},r,i){let n="";for(let t=0;t<L;t++){let s=O[t];if(void 0!==e[s]){let t=F[s]||s;n+=`${t}(${e[s]}) `}}return t&&!e.z&&(n+="translateZ(0)"),n=n.trim(),i?n=i(e,r?"":n):s&&r&&(n="none"),n}(e.transform,s,c,r):i.transform&&(i.transform="none")),h){let{originX:e="50%",originY:t="50%",originZ:s=0}=a;i.transformOrigin=`${e} ${t} ${s}`}}let er=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ei(e,t,s){for(let r in t)R(t[r])||A(r,s)||(e[r]=t[r])}let en=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eo(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||en.has(e)}let ea=e=>!eo(e);try{!function(e){e&&(ea=t=>t.startsWith("on")?!eo(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function el(e,t,s){return"string"==typeof e?e:X.transform(t+s*e)}let eh={offset:"stroke-dashoffset",array:"stroke-dasharray"},ec={offset:"strokeDashoffset",array:"strokeDasharray"};function eu(e,{attrX:t,attrY:s,attrScale:r,originX:i,originY:n,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...h},c,u,d){if(es(e,h,c,d),u){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f,dimensions:m}=e;p.transform&&(m&&(f.transform=p.transform),delete p.transform),m&&(void 0!==i||void 0!==n||f.transform)&&(f.transformOrigin=function(e,t,s){let r=el(t,e.x,e.width),i=el(s,e.y,e.height);return`${r} ${i}`}(m,void 0!==i?i:.5,void 0!==n?n:.5)),void 0!==t&&(p.x=t),void 0!==s&&(p.y=s),void 0!==r&&(p.scale=r),void 0!==o&&function(e,t,s=1,r=0,i=!0){e.pathLength=1;let n=i?eh:ec;e[n.offset]=X.transform(-r);let o=X.transform(t),a=X.transform(s);e[n.array]=`${o} ${a}`}(p,o,a,l,!1)}let ed=()=>({...er(),attrs:{}}),ep=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ef(e,{style:t,vars:s},r,i){for(let n in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),s)e.style.setProperty(n,s[n])}let em=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function eg(e,t,s,r){for(let s in ef(e,t,void 0,r),t.attrs)e.setAttribute(em.has(s)?s:h(s),t.attrs[s])}function ey(e,t){let{style:s}=e,r={};for(let i in s)(R(s[i])||t.style&&R(t.style[i])||A(i,e))&&(r[i]=s[i]);return r}function ev(e,t){let s=ey(e,t);for(let r in e)(R(e[r])||R(t[r]))&&(s[-1!==O.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return s}function eb(e,t,s,r={},i={}){return"function"==typeof t&&(t=t(void 0!==s?s:e.custom,r,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==s?s:e.custom,r,i)),t}var e_=s(207);let eC=e=>Array.isArray(e),ex=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ew=e=>eC(e)?e[e.length-1]||0:e;function eE(e){let t=R(e)?e.get():e;return ex(t)?t.toValue():t}let ek=e=>(t,s)=>{let i=(0,r.useContext)(n),a=(0,r.useContext)(o.O),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:s},r,i,n){let o={latestValues:function(e,t,s,r){let i={},n=r(e,{});for(let e in n)i[e]=eE(n[e]);let{initial:o,animate:a}=e,l=g(e),h=y(e);t&&h&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===a&&(a=t.animate));let c=!!s&&!1===s.initial,u=(c=c||!1===o)?a:o;return u&&"boolean"!=typeof u&&!p(u)&&(Array.isArray(u)?u:[u]).forEach(t=>{let s=eb(e,t);if(!s)return;let{transitionEnd:r,transition:n,...o}=s;for(let e in o){let t=o[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let e in r)i[e]=r[e]}),i}(r,i,n,e),renderState:t()};return s&&(o.mount=e=>s(r,e,o)),o})(e,t,i,a);return s?l():(0,e_.h)(l)};var eS=s(228);let eT={useVisualState:ek({scrapeMotionValuesFromProps:ev,createRenderState:ed,onMount:(e,t,{renderState:s,latestValues:r})=>{eS.Wi.read(()=>{try{s.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){s.dimensions={x:0,y:0,width:0,height:0}}}),eS.Wi.render(()=>{eu(s,r,{enableHardwareAcceleration:!1},ep(t.tagName),e.transformTemplate),eg(t,s)})}})},eO={useVisualState:ek({scrapeMotionValuesFromProps:ey,createRenderState:er})};function eP(e,t,s,r={passive:!0}){return e.addEventListener(t,s,r),()=>e.removeEventListener(t,s)}let eA=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eR(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eF=e=>t=>eA(t)&&e(t,eR(t));function eL(e,t,s,r){return eP(e,t,eF(s),r)}let eD=(e,t)=>s=>t(e(s)),eN=(...e)=>e.reduce(eD);function eB(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eM=eB("dragHorizontal"),eV=eB("dragVertical");function ej(e){let t=!1;if("y"===e)t=eV();else if("x"===e)t=eM();else{let e=eM(),s=eV();e&&s?t=()=>{e(),s()}:(e&&e(),s&&s())}return t}function eI(){let e=ej(!0);return!e||(e(),!1)}class eU{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eW(e,t){let s="onHover"+(t?"Start":"End");return eL(e.current,"pointer"+(t?"enter":"leave"),(r,i)=>{if("touch"===r.pointerType||eI())return;let n=e.getProps();e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",t),n[s]&&eS.Wi.update(()=>n[s](r,i))},{passive:!e.getProps()[s]})}class e$ extends eU{mount(){this.unmount=eN(eW(this.node,!0),eW(this.node,!1))}unmount(){}}class eq extends eU{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eN(eP(this.node.current,"focus",()=>this.onFocus()),eP(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eH=(e,t)=>!!t&&(e===t||eH(e,t.parentElement));var ez=s(254);function eG(e,t){if(!t)return;let s=new PointerEvent("pointer"+e);t(s,eR(s))}class eZ extends eU{constructor(){super(...arguments),this.removeStartListeners=ez.Z,this.removeEndListeners=ez.Z,this.removeAccessibleListeners=ez.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let s=this.node.getProps(),r=eL(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:s,onTapCancel:r,globalTapTarget:i}=this.node.getProps();eS.Wi.update(()=>{i||eH(this.node.current,e.target)?s&&s(e,t):r&&r(e,t)})},{passive:!(s.onTap||s.onPointerUp)}),i=eL(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=eN(r,i),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eP(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eP(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eG("up",(e,t)=>{let{onTap:s}=this.node.getProps();s&&eS.Wi.update(()=>s(e,t))})}),eG("down",(e,t)=>{this.startPress(e,t)}))}),t=eP(this.node.current,"blur",()=>{this.isPressing&&eG("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eN(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:s,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&eS.Wi.update(()=>s(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eI()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:s}=this.node.getProps();s&&eS.Wi.update(()=>s(e,t))}mount(){let e=this.node.getProps(),t=eL(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=eP(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eN(t,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let eY=new WeakMap,eX=new WeakMap,eK=e=>{let t=eY.get(e.target);t&&t(e)},eJ=e=>{e.forEach(eK)},eQ={some:0,all:1};class e0 extends eU{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:s,amount:r="some",once:i}=e,n={root:t?t.current:void 0,rootMargin:s,threshold:"number"==typeof r?r:eQ[r]};return function(e,t,s){let r=function({root:e,...t}){let s=e||document;eX.has(s)||eX.set(s,{});let r=eX.get(s),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(eJ,{root:e,...t})),r[i]}(t);return eY.set(e,s),r.observe(e),()=>{eY.delete(e),r.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:s,onViewportLeave:r}=this.node.getProps(),n=t?s:r;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return s=>e[s]!==t[s]}(e,t))&&this.startObserver()}unmount(){}}function e1(e,t){if(!Array.isArray(t))return!1;let s=t.length;if(s!==e.length)return!1;for(let r=0;r<s;r++)if(t[r]!==e[r])return!1;return!0}function e3(e,t,s){let r=e.getProps();return eb(r,t,void 0!==s?s:r.custom,function(e){let t={};return e.values.forEach((e,s)=>t[s]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,s)=>t[s]=e.getVelocity()),t}(e))}var e2=s(7222);let e6=e=>1e3*e,e9=e=>e/1e3,e4={current:!1},e8=e=>Array.isArray(e)&&"number"==typeof e[0],e5=([e,t,s,r])=>`cubic-bezier(${e}, ${t}, ${s}, ${r})`,e7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e5([0,.65,.55,1]),circOut:e5([.55,0,1,.45]),backIn:e5([.31,.01,.66,-.59]),backOut:e5([.33,1.53,.69,.99])},te=(e,t,s)=>(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e;function tt(e,t,s,r){if(e===t&&s===r)return ez.Z;let i=t=>(function(e,t,s,r,i){let n,o;let a=0;do(n=te(o=t+(s-t)/2,r,i)-e)>0?s=o:t=o;while(Math.abs(n)>1e-7&&++a<12);return o})(t,0,1,e,s);return e=>0===e||1===e?e:te(i(e),t,r)}let ts=tt(.42,0,1,1),tr=tt(0,0,.58,1),ti=tt(.42,0,.58,1),tn=e=>Array.isArray(e)&&"number"!=typeof e[0],to=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ta=e=>t=>1-e(1-t),tl=e=>1-Math.sin(Math.acos(e)),th=ta(tl),tc=to(tl),tu=tt(.33,1.53,.69,.99),td=ta(tu),tp=to(td),tf={linear:ez.Z,easeIn:ts,easeInOut:ti,easeOut:tr,circIn:tl,circInOut:tc,circOut:th,backIn:td,backInOut:tp,backOut:tu,anticipate:e=>(e*=2)<1?.5*td(e):.5*(2-Math.pow(2,-10*(e-1)))},tm=e=>{if(Array.isArray(e)){(0,e2.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,s,r,i]=e;return tt(t,s,r,i)}return"string"==typeof e?((0,e2.k)(void 0!==tf[e],`Invalid easing type '${e}'`),tf[e]):e},tg=(e,t)=>s=>!!(z(s)&&H.test(s)&&s.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(s,t)),ty=(e,t,s)=>r=>{if(!z(r))return r;let[i,n,o,a]=r.match($);return{[e]:parseFloat(i),[t]:parseFloat(n),[s]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tv=e=>V(0,255,e),tb={...j,transform:e=>Math.round(tv(e))},t_={test:tg("rgb","red"),parse:ty("red","green","blue"),transform:({red:e,green:t,blue:s,alpha:r=1})=>"rgba("+tb.transform(e)+", "+tb.transform(t)+", "+tb.transform(s)+", "+W(I.transform(r))+")"},tC={test:tg("#"),parse:function(e){let t="",s="",r="",i="";return e.length>5?(t=e.substring(1,3),s=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),s=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,s+=s,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:t_.transform},tx={test:tg("hsl","hue"),parse:ty("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:s,alpha:r=1})=>"hsla("+Math.round(e)+", "+Y.transform(W(t))+", "+Y.transform(W(s))+", "+W(I.transform(r))+")"},tw={test:e=>t_.test(e)||tC.test(e)||tx.test(e),parse:e=>t_.test(e)?t_.parse(e):tx.test(e)?tx.parse(e):tC.parse(e),transform:e=>z(e)?e:e.hasOwnProperty("red")?t_.transform(e):tx.transform(e)},tE=(e,t,s)=>-s*e+s*t+e;function tk(e,t,s){return(s<0&&(s+=1),s>1&&(s-=1),s<1/6)?e+(t-e)*6*s:s<.5?t:s<2/3?e+(t-e)*(2/3-s)*6:e}let tS=(e,t,s)=>{let r=e*e;return Math.sqrt(Math.max(0,s*(t*t-r)+r))},tT=[tC,t_,tx],tO=e=>tT.find(t=>t.test(e));function tP(e){let t=tO(e);(0,e2.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let s=t.parse(e);return t===tx&&(s=function({hue:e,saturation:t,lightness:s,alpha:r}){e/=360,s/=100;let i=0,n=0,o=0;if(t/=100){let r=s<.5?s*(1+t):s+t-s*t,a=2*s-r;i=tk(a,r,e+1/3),n=tk(a,r,e),o=tk(a,r,e-1/3)}else i=n=o=s;return{red:Math.round(255*i),green:Math.round(255*n),blue:Math.round(255*o),alpha:r}}(s)),s}let tA=(e,t)=>{let s=tP(e),r=tP(t),i={...s};return e=>(i.red=tS(s.red,r.red,e),i.green=tS(s.green,r.green,e),i.blue=tS(s.blue,r.blue,e),i.alpha=tE(s.alpha,r.alpha,e),t_.transform(i))},tR={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:ez.Z},tF={regex:q,countKey:"Colors",token:"${c}",parse:tw.parse},tL={regex:$,countKey:"Numbers",token:"${n}",parse:j.parse};function tD(e,{regex:t,countKey:s,token:r,parse:i}){let n=e.tokenised.match(t);n&&(e["num"+s]=n.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...n.map(i)))}function tN(e){let t=e.toString(),s={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return s.value.includes("var(--")&&tD(s,tR),tD(s,tF),tD(s,tL),s}function tB(e){return tN(e).values}function tM(e){let{values:t,numColors:s,numVars:r,tokenised:i}=tN(e),n=t.length;return e=>{let t=i;for(let i=0;i<n;i++)t=i<r?t.replace(tR.token,e[i]):i<r+s?t.replace(tF.token,tw.transform(e[i])):t.replace(tL.token,W(e[i]));return t}}let tV=e=>"number"==typeof e?0:e,tj={test:function(e){var t,s;return isNaN(e)&&z(e)&&((null===(t=e.match($))||void 0===t?void 0:t.length)||0)+((null===(s=e.match(q))||void 0===s?void 0:s.length)||0)>0},parse:tB,createTransformer:tM,getAnimatableNone:function(e){let t=tB(e);return tM(e)(t.map(tV))}},tI=(e,t)=>s=>`${s>0?t:e}`;function tU(e,t){return"number"==typeof e?s=>tE(e,t,s):tw.test(e)?tA(e,t):e.startsWith("var(")?tI(e,t):tq(e,t)}let tW=(e,t)=>{let s=[...e],r=s.length,i=e.map((e,s)=>tU(e,t[s]));return e=>{for(let t=0;t<r;t++)s[t]=i[t](e);return s}},t$=(e,t)=>{let s={...e,...t},r={};for(let i in s)void 0!==e[i]&&void 0!==t[i]&&(r[i]=tU(e[i],t[i]));return e=>{for(let t in r)s[t]=r[t](e);return s}},tq=(e,t)=>{let s=tj.createTransformer(t),r=tN(e),i=tN(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?eN(tW(r.values,i.values),s):((0,e2.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tI(e,t))},tH=(e,t,s)=>{let r=t-e;return 0===r?1:(s-e)/r},tz=(e,t)=>s=>tE(e,t,s);function tG(e,t,{clamp:s=!0,ease:r,mixer:i}={}){let n=e.length;if((0,e2.k)(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,s){let r=[],i=s||function(e){if("number"==typeof e);else if("string"==typeof e)return tw.test(e)?tA:tq;else if(Array.isArray(e))return tW;else if("object"==typeof e)return t$;return tz}(e[0]),n=e.length-1;for(let s=0;s<n;s++){let n=i(e[s],e[s+1]);t&&(n=eN(Array.isArray(t)?t[s]||ez.Z:t,n)),r.push(n)}return r}(t,r,i),a=o.length,l=t=>{let s=0;if(a>1)for(;s<e.length-2&&!(t<e[s+1]);s++);let r=tH(e[s],e[s+1],t);return o[s](r)};return s?t=>l(V(e[0],e[n-1],t)):l}function tZ({duration:e=300,keyframes:t,times:s,ease:r="easeInOut"}){let i=tn(r)?r.map(tm):tm(r),n={done:!1,value:t[0]},o=tG((s&&s.length===t.length?s:function(e){let t=[0];return function(e,t){let s=e[e.length-1];for(let r=1;r<=t;r++){let i=tH(0,t,r);e.push(tE(s,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||ti).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(n.value=o(t),n.done=t>=e,n)}}function tY(e,t,s){var r,i;let n=Math.max(t-5,0);return r=s-e(n),(i=t-n)?1e3/i*r:0}function tX(e,t){return e*Math.sqrt(1-t*t)}let tK=["duration","bounce"],tJ=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function t0({keyframes:e,restDelta:t,restSpeed:s,...r}){let i;let n=e[0],o=e[e.length-1],a={done:!1,value:n},{stiffness:l,damping:h,mass:c,duration:u,velocity:d,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!tQ(e,tJ)&&tQ(e,tK)){let s=function({duration:e=800,bounce:t=.25,velocity:s=0,mass:r=1}){let i,n;(0,e2.K)(e<=e6(10),"Spring duration must be 10 seconds or less");let o=1-t;o=V(.05,1,o),e=V(.01,10,e9(e)),o<1?(i=t=>{let r=t*o,i=r*e;return .001-(r-s)/tX(t,o)*Math.exp(-i)},n=t=>{let r=t*o*e,n=Math.pow(o,2)*Math.pow(t,2)*e,a=tX(Math.pow(t,2),o);return(r*s+s-n)*Math.exp(-r)*(-i(t)+.001>0?-1:1)/a}):(i=t=>-.001+Math.exp(-t*e)*((t-s)*e+1),n=t=>e*e*(s-t)*Math.exp(-t*e));let a=function(e,t,s){let r=s;for(let s=1;s<12;s++)r-=e(r)/t(r);return r}(i,n,5/e);if(e=e6(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*o*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...s,mass:1}).isResolvedFromDuration=!0}return t}({...r,velocity:-e9(r.velocity||0)}),f=d||0,m=h/(2*Math.sqrt(l*c)),g=o-n,y=e9(Math.sqrt(l/c)),v=5>Math.abs(g);if(s||(s=v?.01:2),t||(t=v?.005:.5),m<1){let e=tX(y,m);i=t=>o-Math.exp(-m*y*t)*((f+m*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===m)i=e=>o-Math.exp(-y*e)*(g+(f+y*g)*e);else{let e=y*Math.sqrt(m*m-1);i=t=>{let s=Math.exp(-m*y*t),r=Math.min(e*t,300);return o-s*((f+m*y*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}return{calculatedDuration:p&&u||null,next:e=>{let r=i(e);if(p)a.done=e>=u;else{let n=f;0!==e&&(n=m<1?tY(i,e,r):0);let l=Math.abs(n)<=s,h=Math.abs(o-r)<=t;a.done=l&&h}return a.value=a.done?o:r,a}}}function t1({keyframes:e,velocity:t=0,power:s=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:c}){let u,d;let p=e[0],f={done:!1,value:p},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,y=s*t,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let _=e=>-y*Math.exp(-e/r),C=e=>b+_(e),x=e=>{let t=_(e),s=C(e);f.done=Math.abs(t)<=h,f.value=f.done?b:s},w=e=>{m(f.value)&&(u=e,d=t0({keyframes:[f.value,g(f.value)],velocity:tY(C,e,f.value),damping:i,stiffness:n,restDelta:h,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==u||(t=!0,x(e),w(e)),void 0!==u&&e>u)?d.next(e-u):(t||x(e),f)}}}let t3=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eS.Wi.update(t,!0),stop:()=>(0,eS.Pn)(t),now:()=>eS.frameData.isProcessing?eS.frameData.timestamp:performance.now()}};function t2(e){let t=0,s=e.next(t);for(;!s.done&&t<2e4;)t+=50,s=e.next(t);return t>=2e4?1/0:t}let t6={decay:t1,inertia:t1,tween:tZ,keyframes:tZ,spring:t0};function t9({autoplay:e=!0,delay:t=0,driver:s=t3,keyframes:r,type:i="keyframes",repeat:n=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:h,onComplete:c,onUpdate:u,...d}){let p,f,m,g,y,v=1,b=!1,_=()=>{f=new Promise(e=>{p=e})};_();let C=t6[i]||tZ;C!==tZ&&"number"!=typeof r[0]&&(g=tG([0,100],r,{clamp:!1}),r=[0,100]);let x=C({...d,keyframes:r});"mirror"===a&&(y=C({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let w="idle",E=null,k=null,S=null;null===x.calculatedDuration&&n&&(x.calculatedDuration=t2(x));let{calculatedDuration:T}=x,O=1/0,P=1/0;null!==T&&(P=(O=T+o)*(n+1)-o);let A=0,R=e=>{if(null===k)return;v>0&&(k=Math.min(k,e)),v<0&&(k=Math.min(e-P/v,k));let s=(A=null!==E?E:Math.round(e-k)*v)-t*(v>=0?1:-1),i=v>=0?s<0:s>P;A=Math.max(s,0),"finished"===w&&null===E&&(A=P);let l=A,h=x;if(n){let e=Math.min(A,P)/O,t=Math.floor(e),s=e%1;!s&&e>=1&&(s=1),1===s&&t--,(t=Math.min(t,n+1))%2&&("reverse"===a?(s=1-s,o&&(s-=o/O)):"mirror"===a&&(h=y)),l=V(0,1,s)*O}let c=i?{done:!1,value:r[0]}:h.next(l);g&&(c.value=g(c.value));let{done:d}=c;i||null===T||(d=v>=0?A>=P:A<=0);let p=null===E&&("finished"===w||"running"===w&&d);return u&&u(c.value),p&&D(),c},F=()=>{m&&m.stop(),m=void 0},L=()=>{w="idle",F(),p(),_(),k=S=null},D=()=>{w="finished",c&&c(),F(),p()},N=()=>{if(b)return;m||(m=s(R));let e=m.now();l&&l(),null!==E?k=e-E:k&&"finished"!==w||(k=e),"finished"===w&&_(),S=k,E=null,w="running",m.start()};e&&N();let B={then:(e,t)=>f.then(e,t),get time(){return e9(A)},set time(newTime){A=newTime=e6(newTime),null===E&&m&&0!==v?k=m.now()-newTime/v:E=newTime},get duration(){return e9(null===x.calculatedDuration?t2(x):x.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,B.time=e9(A)},get state(){return w},play:N,pause:()=>{w="paused",E=A},stop:()=>{b=!0,"idle"!==w&&(w="idle",h&&h(),L())},cancel:()=>{null!==S&&R(S),L()},complete:()=>{w="finished"},sample:e=>(k=0,R(e))};return B}let t4=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),t8=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),t5=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&e7[t]||e8(t)||Array.isArray(t)&&t.every(e))}(t.ease),t7={type:"spring",stiffness:500,damping:25,restSpeed:10},se=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),st={type:"keyframes",duration:.8},ss={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},sr=(e,{keyframes:t})=>t.length>2?st:P.has(e)?e.startsWith("scale")?se(t[1]):t7:ss,si=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tj.test(t)||"0"===t)&&!t.startsWith("url(")),sn=new Set(["brightness","contrast","saturate","opacity"]);function so(e){let[t,s]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=s.match($)||[];if(!r)return e;let i=s.replace(r,""),n=sn.has(t)?1:0;return r!==s&&(n*=100),t+"("+n+i+")"}let sa=/([a-z-]*)\(.*?\)/g,sl={...tj,getAnimatableNone:e=>{let t=e.match(sa);return t?t.map(so).join(" "):e}},sh={...et,color:tw,backgroundColor:tw,outlineColor:tw,fill:tw,stroke:tw,borderColor:tw,borderTopColor:tw,borderRightColor:tw,borderBottomColor:tw,borderLeftColor:tw,filter:sl,WebkitFilter:sl},sc=e=>sh[e];function su(e,t){let s=sc(e);return s!==sl&&(s=tj),s.getAnimatableNone?s.getAnimatableNone(t):void 0}let sd=e=>/^0[^.\s]+$/.test(e);function sp(e,t){return e[t]||e.default||e}let sf={skipAnimations:!1},sm=(e,t,s,r={})=>i=>{let n=sp(r,e)||{},o=n.delay||r.delay||0,{elapsed:a=0}=r;a-=e6(o);let l=function(e,t,s,r){let i,n;let o=si(t,s);i=Array.isArray(s)?[...s]:[null,s];let a=void 0!==r.from?r.from:e.get(),l=[];for(let e=0;e<i.length;e++){var h;null===i[e]&&(i[e]=0===e?a:i[e-1]),("number"==typeof(h=i[e])?0===h:null!==h?"none"===h||"0"===h||sd(h):void 0)&&l.push(e),"string"==typeof i[e]&&"none"!==i[e]&&"0"!==i[e]&&(n=i[e])}if(o&&l.length&&n)for(let e=0;e<l.length;e++)i[l[e]]=su(t,n);return i}(t,e,s,n),h=l[0],c=l[l.length-1],u=si(e,h),d=si(e,c);(0,e2.K)(u===d,`You are trying to animate ${e} from "${h}" to "${c}". ${h} is not an animatable value - to enable this animation set ${h} to a value animatable to ${c} via the \`style\` property.`);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...n,delay:-a,onUpdate:e=>{t.set(e),n.onUpdate&&n.onUpdate(e)},onComplete:()=>{i(),n.onComplete&&n.onComplete()}};if(!function({when:e,delay:t,delayChildren:s,staggerChildren:r,staggerDirection:i,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:h,...c}){return!!Object.keys(c).length}(n)&&(p={...p,...sr(e,p)}),p.duration&&(p.duration=e6(p.duration)),p.repeatDelay&&(p.repeatDelay=e6(p.repeatDelay)),!u||!d||e4.current||!1===n.type||sf.skipAnimations)return function({keyframes:e,delay:t,onUpdate:s,onComplete:r}){let i=()=>(s&&s(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ez.Z,pause:ez.Z,stop:ez.Z,then:e=>(e(),Promise.resolve()),cancel:ez.Z,complete:ez.Z});return t?t9({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(e4.current?{...p,delay:0}:p);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let s=function(e,t,{onUpdate:s,onComplete:r,...i}){let n,o;if(!(t4()&&t8.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let a=!1,l=!1,h=()=>{o=new Promise(e=>{n=e})};h();let{keyframes:c,duration:u=300,ease:d,times:p}=i;if(t5(t,i)){let e=t9({...i,repeat:0,delay:0}),t={done:!1,value:c[0]},s=[],r=0;for(;!t.done&&r<2e4;)t=e.sample(r),s.push(t.value),r+=10;p=void 0,c=s,u=r-10,d="linear"}let f=function(e,t,s,{delay:r=0,duration:i,repeat:n=0,repeatType:o="loop",ease:a,times:l}={}){let h={[t]:s};l&&(h.offset=l);let c=function e(t){if(t)return e8(t)?e5(t):Array.isArray(t)?t.map(e):e7[t]}(a);return Array.isArray(c)&&(h.easing=c),e.animate(h,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"})}(e.owner.current,t,c,{...i,duration:u,ease:d,times:p}),m=()=>{l=!1,f.cancel()},g=()=>{l=!0,eS.Wi.update(m),n(),h()};return f.onfinish=()=>{l||(e.set(function(e,{repeat:t,repeatType:s="loop"}){let r=t&&"loop"!==s&&t%2==1?0:e.length-1;return e[r]}(c,i)),r&&r(),g())},{then:(e,t)=>o.then(e,t),attachTimeline:e=>(f.timeline=e,f.onfinish=null,ez.Z),get time(){return e9(f.currentTime||0)},set time(newTime){f.currentTime=e6(newTime)},get speed(){return f.playbackRate},set speed(newSpeed){f.playbackRate=newSpeed},get duration(){return e9(u)},play:()=>{a||(f.play(),(0,eS.Pn)(m))},pause:()=>f.pause(),stop:()=>{if(a=!0,"idle"===f.playState)return;let{currentTime:t}=f;if(t){let s=t9({...i,autoplay:!1});e.setWithVelocity(s.sample(t-10).value,s.sample(t).value,10)}g()},complete:()=>{l||f.finish()},cancel:g}}(t,e,p);if(s)return s}return t9(p)};function sg(e){return!!(R(e)&&e.add)}let sy=e=>/^\-?\d*\.?\d+$/.test(e);function sv(e,t){-1===e.indexOf(t)&&e.push(t)}function sb(e,t){let s=e.indexOf(t);s>-1&&e.splice(s,1)}class s_{constructor(){this.subscriptions=[]}add(e){return sv(this.subscriptions,e),()=>sb(this.subscriptions,e)}notify(e,t,s){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](e,t,s);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,s)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let sC=e=>!isNaN(parseFloat(e)),sx={current:void 0};class sw{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:s,timestamp:r}=eS.frameData;this.lastUpdated!==r&&(this.timeDelta=s,this.lastUpdated=r,eS.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eS.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=sC(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new s_);let s=this.events[e].add(t);return"change"===e?()=>{s(),eS.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,s){this.set(t),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return sx.current&&sx.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?1e3/t*e:0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function sE(e,t){return new sw(e,t)}let sk=e=>t=>t.test(e),sS=[j,X,Y,Z,J,K,{test:e=>"auto"===e,parse:e=>e}],sT=e=>sS.find(sk(e)),sO=[...sS,tw,tj],sP=e=>sO.find(sk(e));function sA(e,t,{delay:s=0,transitionOverride:r,type:i}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t),l=e.getValue("willChange");r&&(n=r);let h=[],u=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let r=e.getValue(t),i=a[t];if(!r||void 0===i||u&&function({protectedKeys:e,needsAnimating:t},s){let r=e.hasOwnProperty(s)&&!0!==t[s];return t[s]=!1,r}(u,t))continue;let o={delay:s,elapsed:0,...sp(n||{},t)};if(window.HandoffAppearAnimations){let s=e.getProps()[c];if(s){let e=window.HandoffAppearAnimations(s,t,r,eS.Wi);null!==e&&(o.elapsed=e,o.isHandoff=!0)}}let d=!o.isHandoff&&!function(e,t){let s=e.get();if(!Array.isArray(t))return s!==t;for(let e=0;e<t.length;e++)if(t[e]!==s)return!0}(r,i);if("spring"===o.type&&(r.getVelocity()||o.velocity)&&(d=!1),r.animation&&(d=!1),d)continue;r.start(sm(t,r,i,e.shouldReduceMotion&&P.has(t)?{type:!1}:o));let p=r.animation;sg(l)&&(l.add(t),p.then(()=>l.remove(t))),h.push(p)}return o&&Promise.all(h).then(()=>{o&&function(e,t){let s=e3(e,t),{transitionEnd:r={},transition:i={},...n}=s?e.makeTargetAnimatable(s,!1):{};for(let t in n={...n,...r}){let s=ew(n[t]);e.hasValue(t)?e.getValue(t).set(s):e.addValue(t,sE(s))}}(e,o)}),h}function sR(e,t,s={}){let r=e3(e,t,s.custom),{transition:i=e.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(i=s.transitionOverride);let n=r?()=>Promise.all(sA(e,r,s)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=i;return function(e,t,s=0,r=0,i=1,n){let o=[],a=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(sF).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(sR(e,t,{...n,delay:s+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,n+r,o,a,s)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([n(),o(s.delay)]);{let[e,t]="beforeChildren"===a?[n,o]:[o,n];return e().then(()=>t())}}function sF(e,t){return e.sortNodePosition(t)}let sL=[...f].reverse(),sD=f.length;function sN(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class sB extends eU{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:s})=>(function(e,t,s={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>sR(e,t,s)));else if("string"==typeof t)r=sR(e,t,s);else{let i="function"==typeof t?e3(e,t,s.custom):t;r=Promise.all(sA(e,i,s))}return r.then(()=>e.notify("AnimationComplete",t))})(e,t,s))),s={animate:sN(!0),whileInView:sN(),whileHover:sN(),whileTap:sN(),whileDrag:sN(),whileFocus:sN(),exit:sN()},r=!0,i=(t,s)=>{let r=e3(e,s);if(r){let{transition:e,transitionEnd:s,...i}=r;t={...t,...i,...s}}return t};function n(n,o){let a=e.getProps(),l=e.getVariantContext(!0)||{},h=[],c=new Set,u={},f=1/0;for(let t=0;t<sD;t++){var m;let g=sL[t],y=s[g],v=void 0!==a[g]?a[g]:l[g],b=d(v),_=g===o?y.isActive:null;!1===_&&(f=t);let C=v===l[g]&&v!==a[g]&&b;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),y.protectedKeys={...u},!y.isActive&&null===_||!v&&!y.prevProp||p(v)||"boolean"==typeof v)continue;let x=(m=y.prevProp,("string"==typeof v?v!==m:!!Array.isArray(v)&&!e1(v,m))||g===o&&y.isActive&&!C&&b||t>f&&b),w=!1,E=Array.isArray(v)?v:[v],k=E.reduce(i,{});!1===_&&(k={});let{prevResolvedValues:S={}}=y,T={...S,...k},O=e=>{x=!0,c.has(e)&&(w=!0,c.delete(e)),y.needsAnimating[e]=!0};for(let e in T){let t=k[e],s=S[e];if(!u.hasOwnProperty(e))(eC(t)&&eC(s)?e1(t,s):t===s)?void 0!==t&&c.has(e)?O(e):y.protectedKeys[e]=!0:void 0!==t?O(e):c.add(e)}y.prevProp=v,y.prevResolvedValues=k,y.isActive&&(u={...u,...k}),r&&e.blockInitialAnimation&&(x=!1),x&&(!C||w)&&h.push(...E.map(e=>({animation:e,options:{type:g,...n}})))}if(c.size){let t={};c.forEach(s=>{let r=e.getBaseTarget(s);void 0!==r&&(t[s]=r)}),h.push({animation:t})}let g=!!h.length;return r&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(h):Promise.resolve()}return{animateChanges:n,setActive:function(t,r,i){var o;if(s[t].isActive===r)return Promise.resolve();null===(o=e.variantChildren)||void 0===o||o.forEach(e=>{var s;return null===(s=e.animationState)||void 0===s?void 0:s.setActive(t,r)}),s[t].isActive=r;let a=n(i,t);for(let e in s)s[e].protectedKeys={};return a},setAnimateFunction:function(s){t=s(e)},getState:()=>s}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),p(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let sM=0;class sV extends eU{constructor(){super(...arguments),this.id=sM++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:s}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let i=this.node.animationState.setActive("exit",!e,{custom:null!=s?s:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let sj=(e,t)=>Math.abs(e-t);class sI{constructor(e,t,{transformPagePoint:s,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=s$(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,s=function(e,t){return Math.sqrt(sj(e.x,t.x)**2+sj(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!s)return;let{point:r}=e,{timestamp:i}=eS.frameData;this.history.push({...r,timestamp:i});let{onStart:n,onMove:o}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=sU(t,this.transformPagePoint),eS.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:s,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=s$("pointercancel"===e.type?this.lastMoveEventInfo:sU(t,this.transformPagePoint),this.history);this.startEvent&&s&&s(e,n),r&&r(e,n)},!eA(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=s,this.contextWindow=r||window;let n=sU(eR(e),this.transformPagePoint),{point:o}=n,{timestamp:a}=eS.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,s$(n,this.history)),this.removeListeners=eN(eL(this.contextWindow,"pointermove",this.handlePointerMove),eL(this.contextWindow,"pointerup",this.handlePointerUp),eL(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eS.Pn)(this.updatePoint)}}function sU(e,t){return t?{point:t(e.point)}:e}function sW(e,t){return{x:e.x-t.x,y:e.y-t.y}}function s$({point:e},t){return{point:e,delta:sW(e,sq(t)),offset:sW(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,r=null,i=sq(e);for(;s>=0&&(r=e[s],!(i.timestamp-r.timestamp>e6(.1)));)s--;if(!r)return{x:0,y:0};let n=e9(i.timestamp-r.timestamp);if(0===n)return{x:0,y:0};let o={x:(i.x-r.x)/n,y:(i.y-r.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,0)}}function sq(e){return e[e.length-1]}function sH(e){return e.max-e.min}function sz(e,t=0,s=.01){return Math.abs(e-t)<=s}function sG(e,t,s,r=.5){e.origin=r,e.originPoint=tE(t.min,t.max,e.origin),e.scale=sH(s)/sH(t),(sz(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tE(s.min,s.max,e.origin)-e.originPoint,(sz(e.translate)||isNaN(e.translate))&&(e.translate=0)}function sZ(e,t,s,r){sG(e.x,t.x,s.x,r?r.originX:void 0),sG(e.y,t.y,s.y,r?r.originY:void 0)}function sY(e,t,s){e.min=s.min+t.min,e.max=e.min+sH(t)}function sX(e,t,s){e.min=t.min-s.min,e.max=e.min+sH(t)}function sK(e,t,s){sX(e.x,t.x,s.x),sX(e.y,t.y,s.y)}function sJ(e,t,s){return{min:void 0!==t?e.min+t:void 0,max:void 0!==s?e.max+s-(e.max-e.min):void 0}}function sQ(e,t){let s=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,r]=[r,s]),{min:s,max:r}}function s0(e,t,s){return{min:s1(e,t),max:s1(e,s)}}function s1(e,t){return"number"==typeof e?e:e[t]||0}let s3=()=>({translate:0,scale:1,origin:0,originPoint:0}),s2=()=>({x:s3(),y:s3()}),s6=()=>({min:0,max:0}),s9=()=>({x:s6(),y:s6()});function s4(e){return[e("x"),e("y")]}function s8({top:e,left:t,right:s,bottom:r}){return{x:{min:t,max:s},y:{min:e,max:r}}}function s5(e){return void 0===e||1===e}function s7({scale:e,scaleX:t,scaleY:s}){return!s5(e)||!s5(t)||!s5(s)}function re(e){return s7(e)||rt(e)||e.z||e.rotate||e.rotateX||e.rotateY}function rt(e){var t,s;return(t=e.x)&&"0%"!==t||(s=e.y)&&"0%"!==s}function rs(e,t,s,r,i){return void 0!==i&&(e=r+i*(e-r)),r+s*(e-r)+t}function rr(e,t=0,s=1,r,i){e.min=rs(e.min,t,s,r,i),e.max=rs(e.max,t,s,r,i)}function ri(e,{x:t,y:s}){rr(e.x,t.translate,t.scale,t.originPoint),rr(e.y,s.translate,s.scale,s.originPoint)}function rn(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function ro(e,t){e.min=e.min+t,e.max=e.max+t}function ra(e,t,[s,r,i]){let n=void 0!==t[i]?t[i]:.5,o=tE(e.min,e.max,n);rr(e,t[s],t[r],o,t.scale)}let rl=["x","scaleX","originX"],rh=["y","scaleY","originY"];function rc(e,t){ra(e.x,t,rl),ra(e.y,t,rh)}function ru(e,t){return s8(function(e,t){if(!t)return e;let s=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:s.y,left:s.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let rd=({current:e})=>e?e.ownerDocument.defaultView:null,rp=new WeakMap;class rf{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=s9(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new sI(e,{onSessionStart:e=>{let{dragSnapToOrigin:s}=this.getProps();s?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eR(e,"page").point)},onStart:(e,t)=>{let{drag:s,dragPropagation:r,onDragStart:i}=this.getProps();if(s&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ej(s),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),s4(e=>{let t=this.getAxisMotionValue(e).get()||0;if(Y.test(t)){let{projection:s}=this.visualElement;if(s&&s.layout){let r=s.layout.layoutBox[e];if(r){let e=sH(r);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),i&&eS.Wi.update(()=>i(e,t),!1,!0);let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:s,dragDirectionLock:r,onDirectionLock:i,onDrag:n}=this.getProps();if(!s&&!this.openGlobalLock)return;let{offset:o}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>s4(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:rd(this.visualElement)})}stop(e,t){let s=this.isDragging;if(this.cancel(),!s)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&eS.Wi.update(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,s){let{drag:r}=this.getProps();if(!s||!rm(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),n=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:s},r){return void 0!==t&&e<t?e=r?tE(t,e,r.min):Math.max(e,t):void 0!==s&&e>s&&(e=r?tE(s,e,r.max):Math.min(e,s)),e}(n,this.constraints[e],this.elastic[e])),i.set(n)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:s}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&u(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:s,bottom:r,right:i}){return{x:sJ(e.x,s,i),y:sJ(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:s0(e,"left","right"),y:s0(e,"top","bottom")}}(s),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&s4(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let s={};return void 0!==t.min&&(s.min=t.min-e.min),void 0!==t.max&&(s.max=t.max-e.min),s}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!u(t))return!1;let r=t.current;(0,e2.k)(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let n=function(e,t,s){let r=ru(e,s),{scroll:i}=t;return i&&(ro(r.x,i.offset.x),ro(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),o={x:sQ((e=i.layout.layoutBox).x,n.x),y:sQ(e.y,n.y)};if(s){let e=s(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=s8(e))}return o}startAnimation(e){let{drag:t,dragMomentum:s,dragElastic:r,dragTransition:i,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(s4(o=>{if(!rm(o,t,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let h={type:"inertia",velocity:s?e[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(e,t){let s=this.getAxisMotionValue(e);return s.start(sm(e,s,0,t))}stopAnimation(){s4(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){s4(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),s=this.visualElement.getProps();return s[t]||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){s4(t=>{let{drag:s}=this.getProps();if(!rm(t,s,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:s,max:n}=r.layout.layoutBox[t];i.set(e[t]-tE(s,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:s}=this.visualElement;if(!u(t)||!s||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};s4(e=>{let t=this.getAxisMotionValue(e);if(t){let s=t.get();r[e]=function(e,t){let s=.5,r=sH(e),i=sH(t);return i>r?s=tH(t.min,t.max-r,e.min):r>i&&(s=tH(e.min,e.max-i,t.min)),V(0,1,s)}({min:s,max:s},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),s4(t=>{if(!rm(t,e,null))return;let s=this.getAxisMotionValue(t),{min:i,max:n}=this.constraints[t];s.set(tE(i,n,r[t]))})}addListeners(){if(!this.visualElement.current)return;rp.set(this.visualElement,this);let e=eL(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:s=!0}=this.getProps();t&&s&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();u(e)&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,r=s.addEventListener("measure",t);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),t();let i=eP(window,"resize",()=>this.scalePositionWithinConstraints()),n=s.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(s4(t=>{let s=this.getAxisMotionValue(t);s&&(this.originPoint[t]+=e[t].translate,s.set(s.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:s=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:n=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:s,dragPropagation:r,dragConstraints:i,dragElastic:n,dragMomentum:o}}}function rm(e,t,s){return(!0===t||t===e)&&(null===s||s===e)}class rg extends eU{constructor(e){super(e),this.removeGroupControls=ez.Z,this.removeListeners=ez.Z,this.controls=new rf(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ez.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let ry=e=>(t,s)=>{e&&eS.Wi.update(()=>e(t,s))};class rv extends eU{constructor(){super(...arguments),this.removePointerDownListener=ez.Z}onPointerDown(e){this.session=new sI(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rd(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:s,onPanEnd:r}=this.node.getProps();return{onSessionStart:ry(e),onStart:ry(t),onMove:s,onEnd:(e,t)=>{delete this.session,r&&eS.Wi.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=eL(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r_(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rC={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!X.test(e))return e;e=parseFloat(e)}let s=r_(e,t.target.x),r=r_(e,t.target.y);return`${s}% ${r}%`}};class rx extends r.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s,layoutId:r}=this.props,{projection:i}=e;Object.assign(T,rE),i&&(t.group&&t.group.add(i),s&&s.register&&r&&s.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rb.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:s,drag:r,isPresent:i}=this.props,n=s.projection;return n&&(n.isPresent=i,r||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent===i||(i?n.promote():n.relegate()||eS.Wi.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),s&&s.deregister&&s.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rw(e){let[t,s]=function(){let e=(0,r.useContext)(o.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:s,register:i}=e,n=(0,r.useId)();return(0,r.useEffect)(()=>i(n),[]),!t&&s?[!1,()=>s&&s(n)]:[!0]}(),i=(0,r.useContext)(x.p);return r.createElement(rx,{...e,layoutGroup:i,switchLayoutGroup:(0,r.useContext)(w),isPresent:t,safeToRemove:s})}let rE={borderRadius:{...rC,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rC,borderTopRightRadius:rC,borderBottomLeftRadius:rC,borderBottomRightRadius:rC,boxShadow:{correct:(e,{treeScale:t,projectionDelta:s})=>{let r=tj.parse(e);if(r.length>5)return e;let i=tj.createTransformer(e),n="number"!=typeof r[0]?1:0,o=s.x.scale*t.x,a=s.y.scale*t.y;r[0+n]/=o,r[1+n]/=a;let l=tE(o,a,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),i(r)}}},rk=["TopLeft","TopRight","BottomLeft","BottomRight"],rS=rk.length,rT=e=>"string"==typeof e?parseFloat(e):e,rO=e=>"number"==typeof e||X.test(e);function rP(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rA=rF(0,.5,th),rR=rF(.5,.95,ez.Z);function rF(e,t,s){return r=>r<e?0:r>t?1:s(tH(e,t,r))}function rL(e,t){e.min=t.min,e.max=t.max}function rD(e,t){rL(e.x,t.x),rL(e.y,t.y)}function rN(e,t,s,r,i){return e-=t,e=r+1/s*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rB(e,t,[s,r,i],n,o){!function(e,t=0,s=1,r=.5,i,n=e,o=e){if(Y.test(t)&&(t=parseFloat(t),t=tE(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=tE(n.min,n.max,r);e===n&&(a-=t),e.min=rN(e.min,t,s,a,i),e.max=rN(e.max,t,s,a,i)}(e,t[s],t[r],t[i],t.scale,n,o)}let rM=["x","scaleX","originX"],rV=["y","scaleY","originY"];function rj(e,t,s,r){rB(e.x,t,rM,s?s.x:void 0,r?r.x:void 0),rB(e.y,t,rV,s?s.y:void 0,r?r.y:void 0)}function rI(e){return 0===e.translate&&1===e.scale}function rU(e){return rI(e.x)&&rI(e.y)}function rW(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function r$(e){return sH(e.x)/sH(e.y)}class rq{constructor(){this.members=[]}add(e){sv(this.members,e),e.scheduleRender()}remove(e){if(sb(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let s=this.members.findIndex(t=>e===t);if(0===s)return!1;for(let e=s;e>=0;e--){let s=this.members[e];if(!1!==s.isPresent){t=s;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,t&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:s}=e;t.onExitComplete&&t.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function rH(e,t,s){let r="",i=e.x.translate/t.x,n=e.y.translate/t.y;if((i||n)&&(r=`translate3d(${i}px, ${n}px, 0) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),s){let{rotate:e,rotateX:t,rotateY:i}=s;e&&(r+=`rotate(${e}deg) `),t&&(r+=`rotateX(${t}deg) `),i&&(r+=`rotateY(${i}deg) `)}let o=e.x.scale*t.x,a=e.y.scale*t.y;return(1!==o||1!==a)&&(r+=`scale(${o}, ${a})`),r||"none"}let rz=(e,t)=>e.depth-t.depth;class rG{constructor(){this.children=[],this.isDirty=!1}add(e){sv(this.children,e),this.isDirty=!0}remove(e){sb(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rz),this.isDirty=!1,this.children.forEach(e)}}let rZ=["","X","Y","Z"],rY={visibility:"hidden"},rX=0,rK={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function rJ({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},s=null==t?void 0:t()){this.id=rX++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rK.totalNodes=rK.resolvedTargetDeltas=rK.recalculatedProjection=0,this.nodes.forEach(r1),this.nodes.forEach(r5),this.nodes.forEach(r7),this.nodes.forEach(r3),window.MotionDebug&&window.MotionDebug.record(rK)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rG)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new s_),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let s=this.eventHandlers.get(e);s&&s.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,s=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:i,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),s&&(i||r)&&(this.isLayoutDirty=!0),e){let s;let r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,s&&s(),s=function(e,t){let s=performance.now(),r=({timestamp:i})=>{let n=i-s;n>=t&&((0,eS.Pn)(r),e(n-t))};return eS.Wi.read(r,!0),()=>(0,eS.Pn)(r)}(r,250),rb.hasAnimatedSinceResize&&(rb.hasAnimatedSinceResize=!1,this.nodes.forEach(r8))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:s,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||n.getDefaultTransition()||io,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!rW(this.targetLayout,r)||s,h=!t&&s;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,h);let t={...sp(i,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||r8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eS.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ie),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:s}=this.options;if(void 0===t&&!s)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r6);return}this.isUpdating||this.nodes.forEach(r9),this.isUpdating=!1,this.nodes.forEach(r4),this.nodes.forEach(rQ),this.nodes.forEach(r0),this.clearAllSnapshots();let e=performance.now();eS.frameData.delta=V(0,1e3/60,e-eS.frameData.timestamp),eS.frameData.timestamp=e,eS.frameData.isProcessing=!0,eS.S6.update.process(eS.frameData),eS.S6.preRender.process(eS.frameData),eS.S6.render.process(eS.frameData),eS.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(r2),this.sharedNodes.forEach(it)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eS.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eS.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=s9(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:r(this.instance),offset:s(this.instance)})}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!rU(this.projectionDelta),s=this.getTransformTemplate(),r=s?s(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&(t||re(this.latestValues)||n)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let s=this.measurePageBox(),r=this.removeElementScroll(s);return e&&(r=this.removeTransform(r)),ih((t=r).x),ih(t.y),{animationId:this.root.animationId,measuredBox:s,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return s9();let t=e.measureViewportBox(),{scroll:s}=this.root;return s&&(ro(t.x,s.offset.x),ro(t.y,s.offset.y)),t}removeElementScroll(e){let t=s9();rD(t,e);for(let s=0;s<this.path.length;s++){let r=this.path[s],{scroll:i,options:n}=r;if(r!==this.root&&i&&n.layoutScroll){if(i.isRoot){rD(t,e);let{scroll:s}=this.root;s&&(ro(t.x,-s.offset.x),ro(t.y,-s.offset.y))}ro(t.x,i.offset.x),ro(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){let s=s9();rD(s,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&rc(s,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),re(r.latestValues)&&rc(s,r.latestValues)}return re(this.latestValues)&&rc(s,this.latestValues),s}removeTransform(e){let t=s9();rD(t,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];if(!s.instance||!re(s.latestValues))continue;s7(s.latestValues)&&s.updateSnapshot();let r=s9();rD(r,s.measurePageBox()),rj(t,s.latestValues,s.snapshot?s.snapshot.layoutBox:void 0,r)}return re(this.latestValues)&&rj(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eS.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,s,r,i;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==n;if(!(e||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=eS.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=s9(),this.relativeTargetOrigin=s9(),sK(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=s9(),this.targetWithTransforms=s9()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),s=this.target,r=this.relativeTarget,i=this.relativeParent.target,sY(s.x,r.x,i.x),sY(s.y,r.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rD(this.target,this.layout.layoutBox),ri(this.target,this.targetDelta)):rD(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=s9(),this.relativeTargetOrigin=s9(),sK(this.relativeTargetOrigin,this.target,e.target),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rK.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||s7(this.parent.latestValues)||rt(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),s=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),s&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===eS.frameData.timestamp&&(r=!1),r)return;let{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||n))return;rD(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(e,t,s,r=!1){let i,n;let o=s.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){n=(i=s[a]).projectionDelta;let o=i.instance;(!o||!o.style||"contents"!==o.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rc(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,ri(e,n)),r&&re(i.latestValues)&&rc(e,i.latestValues))}t.x=rn(t.x),t.y=rn(t.y)}})(this.layoutCorrected,this.treeScale,this.path,s),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=s2(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=s2(),this.projectionDeltaWithTransform=s2());let h=this.projectionTransform;sZ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=rH(this.projectionDelta,this.treeScale),(this.projectionTransform!==h||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rK.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let s;let r=this.snapshot,i=r?r.latestValues:{},n={...this.latestValues},o=s2();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=s9(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),c=!h||h.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(ii));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(is(o.x,e.x,r),is(o.y,e.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,f;sK(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,ir(p.x,f.x,a.x,r),ir(p.y,f.y,a.y,r),s&&(h=this.relativeTarget,d=s,h.x.min===d.x.min&&h.x.max===d.x.max&&h.y.min===d.y.min&&h.y.max===d.y.max)&&(this.isProjectionDirty=!1),s||(s=s9()),rD(s,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,s,r,i,n){i?(e.opacity=tE(0,void 0!==s.opacity?s.opacity:1,rA(r)),e.opacityExit=tE(void 0!==t.opacity?t.opacity:1,0,rR(r))):n&&(e.opacity=tE(void 0!==t.opacity?t.opacity:1,void 0!==s.opacity?s.opacity:1,r));for(let i=0;i<rS;i++){let n=`border${rk[i]}Radius`,o=rP(t,n),a=rP(s,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rO(o)===rO(a)?(e[n]=Math.max(tE(rT(o),rT(a),r),0),(Y.test(a)||Y.test(o))&&(e[n]+="%")):e[n]=a)}(t.rotate||s.rotate)&&(e.rotate=tE(t.rotate||0,s.rotate||0,r))}(n,i,this.latestValues,r,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eS.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eS.Wi.update(()=>{rb.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,s){let r=R(e)?e:sE(e);return r.start(sm("",r,1e3,s)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:s,layout:r,latestValues:i}=e;if(t&&s&&r){if(this!==e&&this.layout&&r&&ic(this.options.animationType,this.layout.layoutBox,r.layoutBox)){s=this.target||s9();let t=sH(this.layout.layoutBox.x);s.x.min=e.target.x.min,s.x.max=s.x.min+t;let r=sH(this.layout.layoutBox.y);s.y.min=e.target.y.min,s.y.max=s.y.min+r}rD(t,s),rc(t,i),sZ(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rq),this.sharedNodes.get(e).add(t);let s=t.options.initialPromotionConfig;t.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:s}={}){let r=this.getStack();r&&r.promote(this,s),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:s}=e;if((s.rotate||s.rotateX||s.rotateY||s.rotateZ)&&(t=!0),!t)return;let r={};for(let t=0;t<rZ.length;t++){let i="rotate"+rZ[t];s[i]&&(r[i]=s[i],e.setStaticValue(i,0))}for(let t in e.render(),r)e.setStaticValue(t,r[t]);e.scheduleRender()}getProjectionStyles(e){var t,s;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rY;let r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=eE(null==e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eE(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!re(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let o=n.animationValues||n.latestValues;this.applyTransformsToTarget(),r.transform=rH(this.projectionDeltaWithTransform,this.treeScale,o),i&&(r.transform=i(o,r.transform));let{x:a,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,n.animationValues?r.opacity=n===this?null!==(s=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==s?s:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=n===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,T){if(void 0===o[e])continue;let{correct:t,applyTo:s}=T[e],i="none"===r.transform?o[e]:t(o[e],n);if(s){let e=s.length;for(let t=0;t<e;t++)r[s[t]]=i}else r[e]=i}return this.options.layoutId&&(r.pointerEvents=n===this?eE(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(r6),this.root.sharedNodes.clear()}}}function rQ(e){e.updateLayout()}function r0(e){var t;let s=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&s&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,n=s.source!==e.layout.source;"size"===i?s4(e=>{let r=n?s.measuredBox[e]:s.layoutBox[e],i=sH(r);r.min=t[e].min,r.max=r.min+i}):ic(i,s.layoutBox,t)&&s4(r=>{let i=n?s.measuredBox[r]:s.layoutBox[r],o=sH(t[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});let o=s2();sZ(o,t,s.layoutBox);let a=s2();n?sZ(a,e.applyTransform(r,!0),s.measuredBox):sZ(a,t,s.layoutBox);let l=!rU(o),h=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:n}=r;if(i&&n){let o=s9();sK(o,s.layoutBox,i.layoutBox);let a=s9();sK(a,t,n.layoutBox),rW(o,a)||(h=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:s,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:h})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r1(e){rK.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r3(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r2(e){e.clearSnapshot()}function r6(e){e.clearMeasurements()}function r9(e){e.isLayoutDirty=!1}function r4(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function r8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function r5(e){e.resolveTargetDelta()}function r7(e){e.calcProjection()}function ie(e){e.resetRotation()}function it(e){e.removeLeadSnapshot()}function is(e,t,s){e.translate=tE(t.translate,0,s),e.scale=tE(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function ir(e,t,s,r){e.min=tE(t.min,s.min,r),e.max=tE(t.max,s.max,r)}function ii(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let io={duration:.45,ease:[.4,0,.1,1]},ia=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),il=ia("applewebkit/")&&!ia("chrome/")?Math.round:ez.Z;function ih(e){e.min=il(e.min),e.max=il(e.max)}function ic(e,t,s){return"position"===e||"preserve-aspect"===e&&!sz(r$(t),r$(s),.2)}let iu=rJ({attachResizeListener:(e,t)=>eP(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),id={current:void 0},ip=rJ({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!id.current){let e=new iu({});e.mount(window),e.setOptions({layoutScroll:!0}),id.current=e}return id.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),im=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ig(e,t,s=1){(0,e2.k)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,i]=function(e){let t=im.exec(e);if(!t)return[,];let[,s,r]=t;return[s,r]}(e);if(!r)return;let n=window.getComputedStyle(t).getPropertyValue(r);if(n){let e=n.trim();return sy(e)?parseFloat(e):e}return B(i)?ig(i,t,s+1):i}let iy=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),iv=e=>iy.has(e),ib=e=>Object.keys(e).some(iv),i_=e=>e===j||e===X,iC=(e,t)=>parseFloat(e.split(", ")[t]),ix=(e,t)=>(s,{transform:r})=>{if("none"===r||!r)return 0;let i=r.match(/^matrix3d\((.+)\)$/);if(i)return iC(i[1],t);{let t=r.match(/^matrix\((.+)\)$/);return t?iC(t[1],e):0}},iw=new Set(["x","y","z"]),iE=O.filter(e=>!iw.has(e)),ik={width:({x:e},{paddingLeft:t="0",paddingRight:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),height:({y:e},{paddingTop:t="0",paddingBottom:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ix(4,13),y:ix(5,14)};ik.translateX=ik.x,ik.translateY=ik.y;let iS=(e,t,s)=>{let r=t.measureViewportBox(),i=t.current,n=getComputedStyle(i),{display:o}=n,a={};"none"===o&&t.setStaticValue("display",e.display||"block"),s.forEach(e=>{a[e]=ik[e](r,n)}),t.render();let l=t.measureViewportBox();return s.forEach(s=>{let r=t.getValue(s);r&&r.jump(a[s]),e[s]=ik[s](l,n)}),e},iT=(e,t,s={},r={})=>{t={...t},r={...r};let i=Object.keys(t).filter(iv),n=[],o=!1,a=[];if(i.forEach(i=>{let l;let h=e.getValue(i);if(!e.hasValue(i))return;let c=s[i],u=sT(c),d=t[i];if(eC(d)){let e=d.length,t=null===d[0]?1:0;u=sT(c=d[t]);for(let s=t;s<e&&null!==d[s];s++)l?(0,e2.k)(sT(d[s])===l,"All keyframes must be of the same type"):(l=sT(d[s]),(0,e2.k)(l===u||i_(u)&&i_(l),"Keyframes must be of the same dimension as the current value"))}else l=sT(d);if(u!==l){if(i_(u)&&i_(l)){let e=h.get();"string"==typeof e&&h.set(parseFloat(e)),"string"==typeof d?t[i]=parseFloat(d):Array.isArray(d)&&l===X&&(t[i]=d.map(parseFloat))}else(null==u?void 0:u.transform)&&(null==l?void 0:l.transform)&&(0===c||0===d)?0===c?h.set(l.transform(c)):t[i]=u.transform(d):(o||(n=function(e){let t=[];return iE.forEach(s=>{let r=e.getValue(s);void 0!==r&&(t.push([s,r.get()]),r.set(s.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),o=!0),a.push(i),r[i]=void 0!==r[i]?r[i]:t[i],h.jump(d))}}),!a.length)return{target:t,transitionEnd:r};{let s=a.indexOf("height")>=0?window.pageYOffset:null,i=iS(t,e,a);return n.length&&n.forEach(([t,s])=>{e.getValue(t).set(s)}),e.render(),C.j&&null!==s&&window.scrollTo({top:s}),{target:i,transitionEnd:r}}},iO=(e,t,s,r)=>{let i=function(e,{...t},s){let r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:s};for(let i in s&&(s={...s}),e.values.forEach(e=>{let t=e.get();if(!B(t))return;let s=ig(t,r);s&&e.set(s)}),t){let e=t[i];if(!B(e))continue;let n=ig(e,r);n&&(t[i]=n,s||(s={}),void 0===s[i]&&(s[i]=e))}return{target:t,transitionEnd:s}}(e,t,r);return function(e,t,s,r){return ib(t)?iT(e,t,s,r):{target:t,transitionEnd:r}}(e,t=i.target,s,r=i.transitionEnd)},iP={current:null},iA={current:!1},iR=new WeakMap,iF=Object.keys(_),iL=iF.length,iD=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],iN=m.length;class iB{constructor({parent:e,props:t,presenceContext:s,reducedMotionConfig:r,visualState:i},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eS.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=i;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=a,this.parent=e,this.props=t,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=n,this.isControllingVariants=g(t),this.isVariantNode=y(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...h}=this.scrapeMotionValuesFromProps(t,{});for(let e in h){let t=h[e];void 0!==o[e]&&R(t)&&(t.set(o[e],!1),sg(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,iR.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),iA.current||function(){if(iA.current=!0,C.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>iP.current=e.matches;e.addListener(t),t()}else iP.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iP.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in iR.delete(this.current),this.projection&&this.projection.unmount(),(0,eS.Pn)(this.notifyUpdate),(0,eS.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let s=P.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eS.Wi.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},s,r,i){let n,o;for(let e=0;e<iL;e++){let s=iF[e],{isEnabled:r,Feature:i,ProjectionNode:a,MeasureLayout:l}=_[s];a&&(n=a),r(t)&&(!this.features[s]&&i&&(this.features[s]=new i(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&n){this.projection=new n(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:s,drag:r,dragConstraints:o,layoutScroll:a,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:s,alwaysMeasureLayout:!!r||o&&u(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof s?s:"both",initialPromotionConfig:i,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):s9()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<iD.length;t++){let s=iD[t];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);let r=e["on"+s];r&&(this.propEventSubscriptions[s]=this.on(s,r))}this.prevMotionValues=function(e,t,s){let{willChange:r}=t;for(let i in t){let n=t[i],o=s[i];if(R(n))e.addValue(i,n),sg(r)&&r.add(i);else if(R(o))e.addValue(i,sE(n,{owner:e})),sg(r)&&r.remove(i);else if(o!==n){if(e.hasValue(i)){let t=e.getValue(i);t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(i);e.addValue(i,sE(void 0!==t?t:n,{owner:e}))}}}for(let r in s)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<iN;e++){let s=m[e],r=this.props[s];(d(r)||!1===r)&&(t[s]=r)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return void 0===s&&void 0!==t&&(s=sE(t,{owner:this}),this.addValue(e,s)),s}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:s}=this.props,r="string"==typeof s||"object"==typeof s?null===(t=eb(this.props,s))||void 0===t?void 0:t[e]:void 0;if(s&&void 0!==r)return r;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||R(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new s_),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class iM extends iB{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:s}){delete t[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...s},{transformValues:r},i){let n=function(e,t,s){let r={};for(let i in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(i,t);if(void 0!==e)r[i]=e;else{let e=s.getValue(i);e&&(r[i]=e.get())}}return r}(s,e||{},this);if(r&&(t&&(t=r(t)),s&&(s=r(s)),n&&(n=r(n))),i){!function(e,t,s){var r,i;let n=Object.keys(t).filter(t=>!e.hasValue(t)),o=n.length;if(o)for(let a=0;a<o;a++){let o=n[a],l=t[o],h=null;Array.isArray(l)&&(h=l[0]),null===h&&(h=null!==(i=null!==(r=s[o])&&void 0!==r?r:e.readValue(o))&&void 0!==i?i:t[o]),null!=h&&("string"==typeof h&&(sy(h)||sd(h))?h=parseFloat(h):!sP(h)&&tj.test(l)&&(h=su(o,l)),e.addValue(o,sE(h,{owner:e})),void 0===s[o]&&(s[o]=h),null!==h&&e.setBaseTarget(o,h))}}(this,s,n);let e=iO(this,s,n,t);t=e.transitionEnd,s=e.target}return{transition:e,transitionEnd:t,...s}}}class iV extends iM{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(P.has(t)){let e=sc(t);return e&&e.default||0}{let s=window.getComputedStyle(e),r=(N(t)?s.getPropertyValue(t):s[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return ru(e,t)}build(e,t,s,r){es(e,t,s,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return ey(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;R(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,s,r){ef(e,t,s,r)}}class ij extends iM{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(P.has(t)){let e=sc(t);return e&&e.default||0}return t=em.has(t)?t:h(t),e.getAttribute(t)}measureInstanceViewportBox(){return s9()}scrapeMotionValuesFromProps(e,t){return ev(e,t)}build(e,t,s,r){eu(e,t,s,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,s,r){eg(e,t,s,r)}mount(e){this.isSVGTag=ep(e.tagName),super.mount(e)}}let iI=(e,t)=>S(e)?new ij(t,{enableHardwareAcceleration:!1}):new iV(t,{enableHardwareAcceleration:!0}),iU={animation:{Feature:sB},exit:{Feature:sV},inView:{Feature:e0},tap:{Feature:eZ},focus:{Feature:eq},hover:{Feature:e$},pan:{Feature:rv},drag:{Feature:rg,ProjectionNode:ip,MeasureLayout:rw},layout:{ProjectionNode:ip,MeasureLayout:rw}},iW=function(e){function t(t,s={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:s,useVisualState:h,Component:p}){e&&function(e){for(let t in e)_[t]={..._[t],...e[t]}}(e);let f=(0,r.forwardRef)(function(f,m){var y;let b;let _={...(0,r.useContext)(i),...f,layoutId:function({layoutId:e}){let t=(0,r.useContext)(x.p).id;return t&&void 0!==e?t+"-"+e:e}(f)},{isStatic:E}=_,k=function(e){let{initial:t,animate:s}=function(e,t){if(g(e)){let{initial:t,animate:s}=e;return{initial:!1===t||d(t)?t:void 0,animate:d(s)?s:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(n));return(0,r.useMemo)(()=>({initial:t,animate:s}),[v(t),v(s)])}(f),S=h(f,E);if(!E&&C.j){k.visualElement=function(e,t,s,h){let{visualElement:u}=(0,r.useContext)(n),d=(0,r.useContext)(l),p=(0,r.useContext)(o.O),f=(0,r.useContext)(i).reducedMotion,m=(0,r.useRef)();h=h||d.renderer,!m.current&&h&&(m.current=h(e,{visualState:t,parent:u,props:s,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:f}));let g=m.current;(0,r.useInsertionEffect)(()=>{g&&g.update(s,p)});let y=(0,r.useRef)(!!(s[c]&&!window.HandoffComplete));return(0,a.L)(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),(0,r.useEffect)(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(p,S,_,t);let s=(0,r.useContext)(w),h=(0,r.useContext)(l).strict;k.visualElement&&(b=k.visualElement.loadFeatures(_,h,e,s))}return r.createElement(n.Provider,{value:k},b&&k.visualElement?r.createElement(b,{visualElement:k.visualElement,..._}):null,s(p,f,(y=k.visualElement,(0,r.useCallback)(e=>{e&&S.mount&&S.mount(e),y&&(e?y.mount(e):y.unmount()),m&&("function"==typeof m?m(e):u(m)&&(m.current=e))},[y])),S,E,k.visualElement))});return f[E]=p,f}(e(t,s))}if("undefined"==typeof Proxy)return t;let s=new Map;return new Proxy(t,{get:(e,r)=>(s.has(r)||s.set(r,t(r)),s.get(r))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},s,i){return{...S(e)?eT:eO,preloadedFeatures:s,useRender:function(e=!1){return(t,s,i,{latestValues:n},o)=>{let a=(S(t)?function(e,t,s,i){let n=(0,r.useMemo)(()=>{let s=ed();return eu(s,t,{enableHardwareAcceleration:!1},ep(i),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){let t={};ei(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t,s){let i={},n=function(e,t,s){let i=e.style||{},n={};return ei(n,i,e),Object.assign(n,function({transformTemplate:e},t,s){return(0,r.useMemo)(()=>{let r=er();return es(r,t,{enableHardwareAcceleration:!s},e),Object.assign({},r.vars,r.style)},[t])}(e,t,s)),e.transformValues?e.transformValues(n):n}(e,t,s);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i})(s,n,o,t),l={...function(e,t,s){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ea(i)||!0===s&&eo(i)||!t&&!eo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(s,"string"==typeof t,e),...a,ref:i},{children:h}=s,c=(0,r.useMemo)(()=>R(h)?h.get():h,[h]);return(0,r.createElement)(t,{...l,children:c})}}(t),createVisualElement:i,Component:e}})(e,t,iU,iI))},7222:(e,t,s)=>{"use strict";s.d(t,{K:()=>i,k:()=>n});var r=s(254);let i=r.Z,n=r.Z},9398:(e,t,s)=>{"use strict";s.d(t,{j:()=>r});let r="undefined"!=typeof document},254:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=e=>e},207:(e,t,s)=>{"use strict";s.d(t,{h:()=>i});var r=s(3729);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},9038:(e,t,s)=>{"use strict";s.d(t,{L:()=>i});var r=s(3729);let i=s(9398).j?r.useLayoutEffect:r.useEffect},8470:(e,t,s)=>{"use strict";let r,i;s.d(t,{io:()=>eR});var n,o={};s.r(o),s.d(o,{Decoder:()=>e_,Encoder:()=>ev,PacketType:()=>n,protocol:()=>ey});var a=s(5574),l=s.t(a,2);let h=Object.create(null);h.open="0",h.close="1",h.ping="2",h.pong="3",h.message="4",h.upgrade="5",h.noop="6";let c=Object.create(null);Object.keys(h).forEach(e=>{c[h[e]]=e});let u={type:"error",data:"parser error"},d=({type:e,data:t},s,r)=>r(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+p(t,!0).toString("base64"):h[e]+(t||"")),p=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),f=(e,t)=>{if("string"!=typeof e)return{type:"message",data:m(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:m(Buffer.from(e.substring(1),"base64"),t)}:c[s]?e.length>1?{type:c[s],data:e.substring(1)}:{type:c[s]}:u},m=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),g=(e,t)=>{let s=e.length,r=Array(s),i=0;e.forEach((e,n)=>{d(e,!1,e=>{r[n]=e,++i===s&&t(r.join("\x1e"))})})},y=(e,t)=>{let s=e.split("\x1e"),r=[];for(let e=0;e<s.length;e++){let i=f(s[e],t);if(r.push(i),"error"===i.type)break}return r};function v(e){return e.reduce((e,t)=>e+t.length,0)}function b(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),r=0;for(let i=0;i<t;i++)s[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),s}function _(e){if(e)return function(e){for(var t in _.prototype)e[t]=_.prototype[t];return e}(e)}_.prototype.on=_.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},_.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},_.prototype.off=_.prototype.removeListener=_.prototype.removeAllListeners=_.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((s=r[i])===t||s.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},_.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,i=s.length;r<i;++r)s[r].apply(this,t)}return this},_.prototype.emitReserved=_.prototype.emit,_.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},_.prototype.hasListeners=function(e){return!!this.listeners(e).length};let C=process.nextTick,x=global;class w{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),s=t[0].indexOf("=");if(-1===s)return;let r=t[0].substring(0,s).trim();if(!r.length)return;let i=t[0].substring(s+1).trim();34===i.charCodeAt(0)&&(i=i.slice(1,-1));let n={name:r,value:i};for(let e=1;e<t.length;e++){let s=t[e].split("=");if(2!==s.length)continue;let r=s[0].trim(),i=s[1].trim();switch(r){case"Expires":n.expires=new Date(i);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(i,10)),n.expires=o}}return n}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,s)=>{var r;(null===(r=t.expires)||void 0===r?void 0:r.getTime())<e&&this._cookies.delete(s)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,s]of this.cookies)t.push(`${e}=${s.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,s]of this.cookies)e.append("cookie",`${t}=${s.value}`)}}function E(e,...t){return t.reduce((t,s)=>(e.hasOwnProperty(s)&&(t[s]=e[s]),t),{})}let k=x.setTimeout,S=x.clearTimeout;function T(e,t){t.useNativeTimers?(e.setTimeoutFn=k.bind(x),e.clearTimeoutFn=S.bind(x)):(e.setTimeoutFn=x.setTimeout.bind(x),e.clearTimeoutFn=x.clearTimeout.bind(x))}function O(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var P=s(7978);let A=P("engine.io-client:transport");class R extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class F extends _{constructor(e){super(),this.writable=!1,T(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new R(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):A("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=f(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let s in e)e.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t}(e);return t.length?"?"+t:""}}let L=P("engine.io-client:polling");class D extends F{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{L("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(L("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){L("pre-pause polling complete"),--e||t()})),this.writable||(L("we are currently writing - waiting to pause"),e++,this.once("drain",function(){L("pre-pause writing complete"),--e||t()}))}else t()}_poll(){L("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){L("polling got data %s",e),y(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():L('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{L("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(L("transport open - closing"),e()):(L("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,g(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=O()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let N=!1;try{N="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let B=N,M=P("engine.io-client:polling");function V(){}class j extends D{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,s=location.port;s||(s=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){let s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){M("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class I extends _{constructor(e,t,s){super(),this.createRequest=e,T(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var e;let t=E(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(t);try{M("xhr open %s: %s",this._method,this._uri),s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&s.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{s.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var e;3===s.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},M("xhr data %s",this._data),s.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=I.requestsCount++,I.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=V,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete I.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(I.requestsCount=0,I.requests={},"undefined"!=typeof document){if("function"==typeof attachEvent)attachEvent("onunload",U);else if("function"==typeof addEventListener){let e="onpagehide"in x?"pagehide":"unload";addEventListener(e,U,!1)}}function U(){for(let e in I.requests)I.requests.hasOwnProperty(e)&&I.requests[e].abort()}!function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||B))return new XMLHttpRequest}catch(e){}if(!t)try{return new x[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let W=a||l;class $ extends j{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null===(t=this.socket)||void 0===t?void 0:t._cookieJar},this.opts),new I(e=>new W(e),this.uri(),e)}}s(9057),s(1250),s(8022);var q=s(6458);s(3054);let H=P("engine.io-client:websocket"),z="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class G extends F{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,s=z?{}:E(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;d(s,this.supportsBinary,e=>{try{this.doWrite(s,e)}catch(e){H("websocket closed before onclose event")}r&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=O()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}x.WebSocket||x.MozWebSocket;class Z extends G{createSocket(e,t,s){var r;if(null===(r=this.socket)||void 0===r?void 0:r._cookieJar)for(let[e,t]of(s.headers=s.headers||{},s.headers.cookie="string"==typeof s.headers.cookie?[s.headers.cookie]:s.headers.cookie||[],this.socket._cookieJar.cookies))s.headers.cookie.push(`${e}=${t.value}`);return new q(e,t,s)}doWrite(e,t){let s={};e.options&&(s.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(s.compress=!1),this.ws.send(t,s)}}let Y=P("engine.io-client:webtransport");class X extends F{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{Y("transport closed gracefully"),this.onClose()}).catch(e=>{Y("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let s=[],r=0,n=-1,o=!1;return new TransformStream({transform(a,l){for(s.push(a);;){if(0===r){if(1>v(s))break;let e=b(s,1);o=(128&e[0])==128,r=(n=127&e[0])<126?3:126===n?1:2}else if(1===r){if(2>v(s))break;let e=b(s,2);n=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(8>v(s))break;let e=b(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){l.enqueue(u);break}n=4294967296*i+t.getUint32(4),r=3}else{if(v(s)<n)break;let e=b(s,n);l.enqueue(f(o?e:i.decode(e),t)),r=0}if(0===n||n>e){l.enqueue(u);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),n=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(p(e.data,!1));d(e,!0,e=>{r||(r=new TextEncoder),t(r.encode(e))})}(e,s=>{let r;let i=s.length;if(i<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,i);else if(i<65536){r=new Uint8Array(3);let e=new DataView(r.buffer);e.setUint8(0,126),e.setUint16(1,i)}else{r=new Uint8Array(9);let e=new DataView(r.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(s)})}});n.readable.pipeTo(e.writable),this._writer=n.writable.getWriter();let o=()=>{s.read().then(({done:e,value:t})=>{if(e){Y("session is closed");return}Y("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{Y("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;this._writer.write(s).then(()=>{r&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let K={websocket:Z,webtransport:X,polling:$},J=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Q=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ee(e){if(e.length>8e3)throw"URI too long";let t=e,s=e.indexOf("["),r=e.indexOf("]");-1!=s&&-1!=r&&(e=e.substring(0,s)+e.substring(s,r).replace(/:/g,";")+e.substring(r,e.length));let i=J.exec(e||""),n={},o=14;for(;o--;)n[Q[o]]=i[o]||"";return -1!=s&&-1!=r&&(n.source=t,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(e,t){let s=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&s.splice(0,1),"/"==t.slice(-1)&&s.splice(s.length-1,1),s}(0,n.path),n.queryKey=function(e,t){let s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(s[t]=r)}),s}(0,n.query),n}let et=P("engine.io-client:socket"),es="function"==typeof addEventListener&&"function"==typeof removeEventListener,er=[];es&&addEventListener("offline",()=>{et("closing %d connection(s) because the network was lost",er.length),er.forEach(e=>e())},!1);class ei extends _{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let s=ee(e);t.hostname=s.host,t.secure="https"===s.protocol||"wss"===s.protocol,t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=ee(t.host).host);T(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},s=e.split("&");for(let e=0,r=s.length;e<r;e++){let r=s[e].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}(this.opts.query)),es&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(et("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},er.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new w),this._open()}createTransport(e){et('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return et("options: %j",s),new this._transportsByName[e](s)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&ei.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){et("setting transport %s",e.name),this.transport&&(et("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){et("socket open"),this.readyState="open",ei.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(et('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else et('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();et("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let s=this.writeBuffer[t].data;if(s&&(e+="string"==typeof s?function(e){let t=0,s=0;for(let r=0,i=e.length;r<i;r++)(t=e.charCodeAt(r))<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),t>0&&e>this._maxPayload)return et("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return et("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(et("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,C(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let i={type:e,data:t,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){let e=()=>{this._onClose("forced close"),et("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(et("socket error %j",e),ei.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return et("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(et('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),es&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=er.indexOf(this._offlineEventListener);-1!==e&&(et("removing listener for the 'offline' event"),er.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ei.protocol=4;class en extends ei{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){et("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){et('probing transport "%s"',e);let t=this.createTransport(e),s=!1;ei.priorWebsocketSuccess=!1;let r=()=>{s||(et('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",r=>{if(!s){if("pong"===r.type&&"probe"===r.data)et('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),t&&(ei.priorWebsocketSuccess="websocket"===t.name,et('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{s||"closed"===this.readyState||(et("changing transport and sending upgrade packet"),h(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{et('probe transport "%s" failed',e);let s=Error("probe error");s.transport=t.name,this.emitReserved("upgradeError",s)}}}))};function i(){s||(s=!0,h(),t.close(),t=null)}let n=s=>{let r=Error("probe error: "+s);r.transport=t.name,i(),et('probe transport "%s" failed because of error: %s',e,s),this.emitReserved("upgradeError",r)};function o(){n("transport closed")}function a(){n("socket closed")}function l(e){t&&e.name!==t.name&&(et('"%s" works - aborting "%s"',e.name,t.name),i())}let h=()=>{t.removeListener("open",r),t.removeListener("error",n),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",r),t.once("error",n),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}class eo extends en{constructor(e,t={}){let s="object"==typeof e?e:t;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(e=>K[e]).filter(e=>!!e)),super(e,s)}}eo.protocol;var ea=s(4974);let el=ea("socket.io-client:url"),eh="function"==typeof ArrayBuffer,ec=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eu=Object.prototype.toString,ed="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eu.call(Blob),ep="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eu.call(File);function ef(e){return eh&&(e instanceof ArrayBuffer||ec(e))||ed&&e instanceof Blob||ep&&e instanceof File}let em=s(2428)("socket.io-parser"),eg=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ey=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(n||(n={}));class ev{constructor(e){this.replacer=e}encode(e){return(em("encoding packet %j",e),(e.type===n.EVENT||e.type===n.ACK)&&function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,r=t.length;s<r;s++)if(e(t[s]))return!0;return!1}if(ef(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===n.EVENT?n.BINARY_EVENT:n.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===n.BINARY_EVENT||e.type===n.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),em("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if(ef(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let r=Array(t.length);for(let i=0;i<t.length;i++)r[i]=e(t[i],s);return r}if("object"==typeof t&&!(t instanceof Date)){let r={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=e(t[i],s));return r}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}}(e),s=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(s),r}}function eb(e){return"[object Object]"===Object.prototype.toString.call(e)}class e_ extends _{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===n.BINARY_EVENT;s||t.type===n.BINARY_ACK?(t.type=s?n.EVENT:n.ACK,this.reconstructor=new eC(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ef(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===n[s.type])throw Error("unknown packet type "+s.type);if(s.type===n.BINARY_EVENT||s.type===n.BINARY_ACK){let r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(r,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(i)}if("/"===e.charAt(t+1)){let r=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(r,t)}else s.nsp="/";let r=e.charAt(t+1);if(""!==r&&Number(r)==r){let r=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){let r=this.tryParse(e.substr(t));if(e_.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return em("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case n.CONNECT:return eb(t);case n.DISCONNECT:return void 0===t;case n.CONNECT_ERROR:return"string"==typeof t||eb(t);case n.EVENT:case n.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===eg.indexOf(t[0]));case n.ACK:case n.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class eC{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,s;let e=(t=this.reconPack,s=this.buffers,t.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=e(t[r],s);else if("object"==typeof t)for(let r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=e(t[r],s));return t}(t.data,s),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function ex(e,t,s){return e.on(t,s),function(){e.off(t,s)}}let ew=ea("socket.io-client:socket"),eE=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class ek extends _{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[ex(e,"open",this.onopen.bind(this)),ex(e,"packet",this.onpacket.bind(this)),ex(e,"error",this.onerror.bind(this)),ex(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,r,i;if(eE.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:n.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;ew("emitting packet with ack id %d",e);let s=t.pop();this._registerAckCallback(e,s),o.id=e}let a=null===(r=null===(s=this.io.engine)||void 0===s?void 0:s.transport)||void 0===r?void 0:r.writable,l=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a?ew("discard packet as the transport is not currently writable"):l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var s;let r=null!==(s=this.flags.timeout)&&void 0!==s?s:this._opts.ackTimeout;if(void 0===r){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(ew("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));ew("event with ack id %d has timed out after %d ms",e,r),t.call(this,Error("operation has timed out"))},r),n=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};n.withError=!0,this.acks[e]=n}emitWithAck(e,...t){return new Promise((s,r)=>{let i=(e,t)=>e?r(e):s(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(s===this._queue[0])return null!==e?s.tryCount>this._opts.retries&&(ew("packet [%d] is discarded after %d tries",s.id,s.tryCount),this._queue.shift(),t&&t(e)):(ew("packet [%d] was successfully sent",s.id),this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(ew("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e){ew("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,ew("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){ew("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:n.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){ew("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case n.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case n.EVENT:case n.BINARY_EVENT:this.onevent(e);break;case n.ACK:case n.BINARY_ACK:this.onack(e);break;case n.DISCONNECT:this.ondisconnect();break;case n.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];ew("emitting event %j",t),null!=e.id&&(ew("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,s=!1;return function(...r){s||(s=!0,ew("sending ack %j",r),t.packet({type:n.ACK,id:e,data:r}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t){ew("bad ack %s",e.id);return}delete this.acks[e.id],ew("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){ew("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){ew("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(ew("performing disconnect (%s)",this.nsp),this.packet({type:n.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function eS(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}eS.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-s:e+s}return 0|Math.min(e,this.max)},eS.prototype.reset=function(){this.attempts=0},eS.prototype.setMin=function(e){this.ms=e},eS.prototype.setMax=function(e){this.max=e},eS.prototype.setJitter=function(e){this.jitter=e};let eT=ea("socket.io-client:manager");class eO extends _{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,T(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(s=t.randomizationFactor)&&void 0!==s?s:.5),this.backoff=new eS({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let r=t.parser||o;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(eT("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;eT("opening %s",this.uri),this.engine=new eo(this.uri,this.opts);let t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=ex(t,"open",function(){s.onopen(),e&&e()}),i=t=>{eT("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},n=ex(t,"error",i);if(!1!==this._timeout){let e=this._timeout;eT("connect attempt will timeout after %d",e);let s=this.setTimeoutFn(()=>{eT("connect attempt timed out after %d",e),r(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(n),this}connect(e){return this.open(e)}onopen(){eT("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(ex(e,"ping",this.onping.bind(this)),ex(e,"data",this.ondata.bind(this)),ex(e,"error",this.onerror.bind(this)),ex(e,"close",this.onclose.bind(this)),ex(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){C(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){eT("error",e),this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new ek(this,e,t),this.nsps[e]=s),s}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){eT("socket %s is still active, skipping close",e);return}this._close()}_packet(e){eT("writing packet %j",e);let t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){eT("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){eT("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;eT("closed due to %s",e),this.cleanup(),null===(s=this.engine)||void 0===s||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)eT("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();eT("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!e.skipReconnect&&(eT("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(eT("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(eT("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eP=ea("socket.io-client"),eA={};function eR(e,t){let s;"object"==typeof e&&(t=e,e=void 0);let r=function(e,t="",s){let r=e;s=s||"undefined"!=typeof location&&location,null==e&&(e=s.protocol+"//"+s.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?s.protocol+e:s.host+e),/^(https?|wss?):\/\//.test(e)||(el("protocol-less url %s",e),e=void 0!==s?s.protocol+"//"+e:"https://"+e),el("parse %s",e),r=ee(e)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(s&&s.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),i=r.source,n=r.id,o=r.path,a=eA[n]&&o in eA[n].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(eP("ignoring socket cache for %s",i),s=new eO(i,t)):(eA[n]||(eP("new io instance for %s",i),eA[n]=new eO(i,t)),s=eA[n]),r.query&&!t.query&&(t.query=r.queryKey),s.socket(r.path,t)}Object.assign(eR,{Manager:eO,Socket:ek,io:eR,connect:eR})}};