{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "1537981e2895bb42ed2f0c8e683b958a", "previewModeSigningKey": "ef425ad6da4b922525e84a3744862c2e213bb5614b5a340022bd3dcb0526bbd0", "previewModeEncryptionKey": "f470af37c30e07d232c2ed5df36255d24dcca4dbf30c658ed2cbbabea8431631"}}