"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadConfig = exports.register = exports.matchFromAbsolutePathsAsync = exports.createMatchPathAsync = exports.matchFromAbsolutePaths = exports.createMatchPath = void 0;
// register is used from register.js in root dir
var match_path_sync_1 = require("./match-path-sync");
Object.defineProperty(exports, "createMatchPath", { enumerable: true, get: function () { return match_path_sync_1.createMatchPath; } });
Object.defineProperty(exports, "matchFromAbsolutePaths", { enumerable: true, get: function () { return match_path_sync_1.matchFromAbsolutePaths; } });
var match_path_async_1 = require("./match-path-async");
Object.defineProperty(exports, "createMatchPathAsync", { enumerable: true, get: function () { return match_path_async_1.createMatchPathAsync; } });
Object.defineProperty(exports, "matchFromAbsolutePathsAsync", { enumerable: true, get: function () { return match_path_async_1.matchFromAbsolutePathsAsync; } });
var register_1 = require("./register");
Object.defineProperty(exports, "register", { enumerable: true, get: function () { return register_1.register; } });
var config_loader_1 = require("./config-loader");
Object.defineProperty(exports, "loadConfig", { enumerable: true, get: function () { return config_loader_1.loadConfig; } });
//# sourceMappingURL=index.js.map