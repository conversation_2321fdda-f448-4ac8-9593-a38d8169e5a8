/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socket.io-parser";
exports.ids = ["vendor-chunks/socket.io-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/browser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-parser/node_modules/debug/src/browser.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-env browser */ /**\n * This is the web browser implementation of `debug()`.\n */ exports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (()=>{\n    let warned = false;\n    return ()=>{\n        if (!warned) {\n            warned = true;\n            console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n        }\n    };\n})();\n/**\n * Colors.\n */ exports.colors = [\n    \"#0000CC\",\n    \"#0000FF\",\n    \"#0033CC\",\n    \"#0033FF\",\n    \"#0066CC\",\n    \"#0066FF\",\n    \"#0099CC\",\n    \"#0099FF\",\n    \"#00CC00\",\n    \"#00CC33\",\n    \"#00CC66\",\n    \"#00CC99\",\n    \"#00CCCC\",\n    \"#00CCFF\",\n    \"#3300CC\",\n    \"#3300FF\",\n    \"#3333CC\",\n    \"#3333FF\",\n    \"#3366CC\",\n    \"#3366FF\",\n    \"#3399CC\",\n    \"#3399FF\",\n    \"#33CC00\",\n    \"#33CC33\",\n    \"#33CC66\",\n    \"#33CC99\",\n    \"#33CCCC\",\n    \"#33CCFF\",\n    \"#6600CC\",\n    \"#6600FF\",\n    \"#6633CC\",\n    \"#6633FF\",\n    \"#66CC00\",\n    \"#66CC33\",\n    \"#9900CC\",\n    \"#9900FF\",\n    \"#9933CC\",\n    \"#9933FF\",\n    \"#99CC00\",\n    \"#99CC33\",\n    \"#CC0000\",\n    \"#CC0033\",\n    \"#CC0066\",\n    \"#CC0099\",\n    \"#CC00CC\",\n    \"#CC00FF\",\n    \"#CC3300\",\n    \"#CC3333\",\n    \"#CC3366\",\n    \"#CC3399\",\n    \"#CC33CC\",\n    \"#CC33FF\",\n    \"#CC6600\",\n    \"#CC6633\",\n    \"#CC9900\",\n    \"#CC9933\",\n    \"#CCCC00\",\n    \"#CCCC33\",\n    \"#FF0000\",\n    \"#FF0033\",\n    \"#FF0066\",\n    \"#FF0099\",\n    \"#FF00CC\",\n    \"#FF00FF\",\n    \"#FF3300\",\n    \"#FF3333\",\n    \"#FF3366\",\n    \"#FF3399\",\n    \"#FF33CC\",\n    \"#FF33FF\",\n    \"#FF6600\",\n    \"#FF6633\",\n    \"#FF9900\",\n    \"#FF9933\",\n    \"#FFCC00\",\n    \"#FFCC33\"\n];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */ // eslint-disable-next-line complexity\nfunction useColors() {\n    // NB: In an Electron preload script, document will be defined but not fully\n    // initialized. Since we know we're in Chrome, we'll just detect this case\n    // explicitly\n    if (false) {}\n    // Internet Explorer and Edge do not support colors.\n    if (typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n        return false;\n    }\n    let m;\n    // Is webkit? http://stackoverflow.com/a/16459606/376773\n    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n    return typeof document !== \"undefined\" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n     false && (0) || // Is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    typeof navigator !== \"undefined\" && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n    typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    args[0] = (this.useColors ? \"%c\" : \"\") + this.namespace + (this.useColors ? \" %c\" : \" \") + args[0] + (this.useColors ? \"%c \" : \" \") + \"+\" + module.exports.humanize(this.diff);\n    if (!this.useColors) {\n        return;\n    }\n    const c = \"color: \" + this.color;\n    args.splice(1, 0, c, \"color: inherit\");\n    // The final \"%c\" is somewhat tricky, because there could be other\n    // arguments passed either before or after the %c, so we need to\n    // figure out the correct index to insert the CSS into\n    let index = 0;\n    let lastC = 0;\n    args[0].replace(/%[a-zA-Z%]/g, (match)=>{\n        if (match === \"%%\") {\n            return;\n        }\n        index++;\n        if (match === \"%c\") {\n            // We only are interested in the *last* %c\n            // (the user may have provided their own)\n            lastC = index;\n        }\n    });\n    args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */ exports.log = console.debug || console.log || (()=>{});\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    try {\n        if (namespaces) {\n            exports.storage.setItem(\"debug\", namespaces);\n        } else {\n            exports.storage.removeItem(\"debug\");\n        }\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    let r;\n    try {\n        r = exports.storage.getItem(\"debug\");\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n    if (!r && typeof process !== \"undefined\" && \"env\" in process) {\n        r = process.env.DEBUG;\n    }\n    return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */ function localstorage() {\n    try {\n        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n        // The Browser also has localStorage in the global context.\n        return localStorage;\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */ formatters.j = function(v) {\n    try {\n        return JSON.stringify(v);\n    } catch (error) {\n        return \"[UnexpectedJSONParseError]: \" + error.message;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/common.js":
/*!************************************************************************!*\
  !*** ./node_modules/socket.io-parser/node_modules/debug/src/common.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */ function setup(env) {\n    createDebug.debug = createDebug;\n    createDebug.default = createDebug;\n    createDebug.coerce = coerce;\n    createDebug.disable = disable;\n    createDebug.enable = enable;\n    createDebug.enabled = enabled;\n    createDebug.humanize = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n    createDebug.destroy = destroy;\n    Object.keys(env).forEach((key)=>{\n        createDebug[key] = env[key];\n    });\n    /**\n\t* The currently active debug mode names, and names to skip.\n\t*/ createDebug.names = [];\n    createDebug.skips = [];\n    /**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/ createDebug.formatters = {};\n    /**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/ function selectColor(namespace) {\n        let hash = 0;\n        for(let i = 0; i < namespace.length; i++){\n            hash = (hash << 5) - hash + namespace.charCodeAt(i);\n            hash |= 0; // Convert to 32bit integer\n        }\n        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n    }\n    createDebug.selectColor = selectColor;\n    /**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/ function createDebug(namespace) {\n        let prevTime;\n        let enableOverride = null;\n        let namespacesCache;\n        let enabledCache;\n        function debug(...args) {\n            // Disabled?\n            if (!debug.enabled) {\n                return;\n            }\n            const self = debug;\n            // Set `diff` timestamp\n            const curr = Number(new Date());\n            const ms = curr - (prevTime || curr);\n            self.diff = ms;\n            self.prev = prevTime;\n            self.curr = curr;\n            prevTime = curr;\n            args[0] = createDebug.coerce(args[0]);\n            if (typeof args[0] !== \"string\") {\n                // Anything else let's inspect with %O\n                args.unshift(\"%O\");\n            }\n            // Apply any `formatters` transformations\n            let index = 0;\n            args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format)=>{\n                // If we encounter an escaped % then don't increase the array index\n                if (match === \"%%\") {\n                    return \"%\";\n                }\n                index++;\n                const formatter = createDebug.formatters[format];\n                if (typeof formatter === \"function\") {\n                    const val = args[index];\n                    match = formatter.call(self, val);\n                    // Now we need to remove `args[index]` since it's inlined in the `format`\n                    args.splice(index, 1);\n                    index--;\n                }\n                return match;\n            });\n            // Apply env-specific formatting (colors, etc.)\n            createDebug.formatArgs.call(self, args);\n            const logFn = self.log || createDebug.log;\n            logFn.apply(self, args);\n        }\n        debug.namespace = namespace;\n        debug.useColors = createDebug.useColors();\n        debug.color = createDebug.selectColor(namespace);\n        debug.extend = extend;\n        debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n        Object.defineProperty(debug, \"enabled\", {\n            enumerable: true,\n            configurable: false,\n            get: ()=>{\n                if (enableOverride !== null) {\n                    return enableOverride;\n                }\n                if (namespacesCache !== createDebug.namespaces) {\n                    namespacesCache = createDebug.namespaces;\n                    enabledCache = createDebug.enabled(namespace);\n                }\n                return enabledCache;\n            },\n            set: (v)=>{\n                enableOverride = v;\n            }\n        });\n        // Env-specific initialization logic for debug instances\n        if (typeof createDebug.init === \"function\") {\n            createDebug.init(debug);\n        }\n        return debug;\n    }\n    function extend(namespace, delimiter) {\n        const newDebug = createDebug(this.namespace + (typeof delimiter === \"undefined\" ? \":\" : delimiter) + namespace);\n        newDebug.log = this.log;\n        return newDebug;\n    }\n    /**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/ function enable(namespaces) {\n        createDebug.save(namespaces);\n        createDebug.namespaces = namespaces;\n        createDebug.names = [];\n        createDebug.skips = [];\n        let i;\n        const split = (typeof namespaces === \"string\" ? namespaces : \"\").split(/[\\s,]+/);\n        const len = split.length;\n        for(i = 0; i < len; i++){\n            if (!split[i]) {\n                continue;\n            }\n            namespaces = split[i].replace(/\\*/g, \".*?\");\n            if (namespaces[0] === \"-\") {\n                createDebug.skips.push(new RegExp(\"^\" + namespaces.slice(1) + \"$\"));\n            } else {\n                createDebug.names.push(new RegExp(\"^\" + namespaces + \"$\"));\n            }\n        }\n    }\n    /**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/ function disable() {\n        const namespaces = [\n            ...createDebug.names.map(toNamespace),\n            ...createDebug.skips.map(toNamespace).map((namespace)=>\"-\" + namespace)\n        ].join(\",\");\n        createDebug.enable(\"\");\n        return namespaces;\n    }\n    /**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/ function enabled(name) {\n        if (name[name.length - 1] === \"*\") {\n            return true;\n        }\n        let i;\n        let len;\n        for(i = 0, len = createDebug.skips.length; i < len; i++){\n            if (createDebug.skips[i].test(name)) {\n                return false;\n            }\n        }\n        for(i = 0, len = createDebug.names.length; i < len; i++){\n            if (createDebug.names[i].test(name)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/ function toNamespace(regexp) {\n        return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, \"*\");\n    }\n    /**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/ function coerce(val) {\n        if (val instanceof Error) {\n            return val.stack || val.message;\n        }\n        return val;\n    }\n    /**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/ function destroy() {\n        console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n    }\n    createDebug.enable(createDebug.load());\n    return createDebug;\n}\nmodule.exports = setup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLXBhcnNlci9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFDQTs7O0NBR0MsR0FFRCxTQUFTQSxNQUFNQyxHQUFHO0lBQ2pCQyxZQUFZQyxLQUFLLEdBQUdEO0lBQ3BCQSxZQUFZRSxPQUFPLEdBQUdGO0lBQ3RCQSxZQUFZRyxNQUFNLEdBQUdBO0lBQ3JCSCxZQUFZSSxPQUFPLEdBQUdBO0lBQ3RCSixZQUFZSyxNQUFNLEdBQUdBO0lBQ3JCTCxZQUFZTSxPQUFPLEdBQUdBO0lBQ3RCTixZQUFZTyxRQUFRLEdBQUdDLG1CQUFPQSxDQUFDO0lBQy9CUixZQUFZUyxPQUFPLEdBQUdBO0lBRXRCQyxPQUFPQyxJQUFJLENBQUNaLEtBQUthLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDeEJiLFdBQVcsQ0FBQ2EsSUFBSSxHQUFHZCxHQUFHLENBQUNjLElBQUk7SUFDNUI7SUFFQTs7Q0FFQSxHQUVBYixZQUFZYyxLQUFLLEdBQUcsRUFBRTtJQUN0QmQsWUFBWWUsS0FBSyxHQUFHLEVBQUU7SUFFdEI7Ozs7Q0FJQSxHQUNBZixZQUFZZ0IsVUFBVSxHQUFHLENBQUM7SUFFMUI7Ozs7O0NBS0EsR0FDQSxTQUFTQyxZQUFZQyxTQUFTO1FBQzdCLElBQUlDLE9BQU87UUFFWCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUYsVUFBVUcsTUFBTSxFQUFFRCxJQUFLO1lBQzFDRCxPQUFPLENBQUVBLFFBQVEsS0FBS0EsT0FBUUQsVUFBVUksVUFBVSxDQUFDRjtZQUNuREQsUUFBUSxHQUFHLDJCQUEyQjtRQUN2QztRQUVBLE9BQU9uQixZQUFZdUIsTUFBTSxDQUFDQyxLQUFLQyxHQUFHLENBQUNOLFFBQVFuQixZQUFZdUIsTUFBTSxDQUFDRixNQUFNLENBQUM7SUFDdEU7SUFDQXJCLFlBQVlpQixXQUFXLEdBQUdBO0lBRTFCOzs7Ozs7Q0FNQSxHQUNBLFNBQVNqQixZQUFZa0IsU0FBUztRQUM3QixJQUFJUTtRQUNKLElBQUlDLGlCQUFpQjtRQUNyQixJQUFJQztRQUNKLElBQUlDO1FBRUosU0FBUzVCLE1BQU0sR0FBRzZCLElBQUk7WUFDckIsWUFBWTtZQUNaLElBQUksQ0FBQzdCLE1BQU1LLE9BQU8sRUFBRTtnQkFDbkI7WUFDRDtZQUVBLE1BQU15QixPQUFPOUI7WUFFYix1QkFBdUI7WUFDdkIsTUFBTStCLE9BQU9DLE9BQU8sSUFBSUM7WUFDeEIsTUFBTUMsS0FBS0gsT0FBUU4sQ0FBQUEsWUFBWU0sSUFBRztZQUNsQ0QsS0FBS0ssSUFBSSxHQUFHRDtZQUNaSixLQUFLTSxJQUFJLEdBQUdYO1lBQ1pLLEtBQUtDLElBQUksR0FBR0E7WUFDWk4sV0FBV007WUFFWEYsSUFBSSxDQUFDLEVBQUUsR0FBRzlCLFlBQVlHLE1BQU0sQ0FBQzJCLElBQUksQ0FBQyxFQUFFO1lBRXBDLElBQUksT0FBT0EsSUFBSSxDQUFDLEVBQUUsS0FBSyxVQUFVO2dCQUNoQyxzQ0FBc0M7Z0JBQ3RDQSxLQUFLUSxPQUFPLENBQUM7WUFDZDtZQUVBLHlDQUF5QztZQUN6QyxJQUFJQyxRQUFRO1lBQ1pULElBQUksQ0FBQyxFQUFFLEdBQUdBLElBQUksQ0FBQyxFQUFFLENBQUNVLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQ0MsT0FBT0M7Z0JBQ2xELG1FQUFtRTtnQkFDbkUsSUFBSUQsVUFBVSxNQUFNO29CQUNuQixPQUFPO2dCQUNSO2dCQUNBRjtnQkFDQSxNQUFNSSxZQUFZM0MsWUFBWWdCLFVBQVUsQ0FBQzBCLE9BQU87Z0JBQ2hELElBQUksT0FBT0MsY0FBYyxZQUFZO29CQUNwQyxNQUFNQyxNQUFNZCxJQUFJLENBQUNTLE1BQU07b0JBQ3ZCRSxRQUFRRSxVQUFVRSxJQUFJLENBQUNkLE1BQU1hO29CQUU3Qix5RUFBeUU7b0JBQ3pFZCxLQUFLZ0IsTUFBTSxDQUFDUCxPQUFPO29CQUNuQkE7Z0JBQ0Q7Z0JBQ0EsT0FBT0U7WUFDUjtZQUVBLCtDQUErQztZQUMvQ3pDLFlBQVkrQyxVQUFVLENBQUNGLElBQUksQ0FBQ2QsTUFBTUQ7WUFFbEMsTUFBTWtCLFFBQVFqQixLQUFLa0IsR0FBRyxJQUFJakQsWUFBWWlELEdBQUc7WUFDekNELE1BQU1FLEtBQUssQ0FBQ25CLE1BQU1EO1FBQ25CO1FBRUE3QixNQUFNaUIsU0FBUyxHQUFHQTtRQUNsQmpCLE1BQU1rRCxTQUFTLEdBQUduRCxZQUFZbUQsU0FBUztRQUN2Q2xELE1BQU1tRCxLQUFLLEdBQUdwRCxZQUFZaUIsV0FBVyxDQUFDQztRQUN0Q2pCLE1BQU1vRCxNQUFNLEdBQUdBO1FBQ2ZwRCxNQUFNUSxPQUFPLEdBQUdULFlBQVlTLE9BQU8sRUFBRSw0REFBNEQ7UUFFakdDLE9BQU80QyxjQUFjLENBQUNyRCxPQUFPLFdBQVc7WUFDdkNzRCxZQUFZO1lBQ1pDLGNBQWM7WUFDZEMsS0FBSztnQkFDSixJQUFJOUIsbUJBQW1CLE1BQU07b0JBQzVCLE9BQU9BO2dCQUNSO2dCQUNBLElBQUlDLG9CQUFvQjVCLFlBQVkwRCxVQUFVLEVBQUU7b0JBQy9DOUIsa0JBQWtCNUIsWUFBWTBELFVBQVU7b0JBQ3hDN0IsZUFBZTdCLFlBQVlNLE9BQU8sQ0FBQ1k7Z0JBQ3BDO2dCQUVBLE9BQU9XO1lBQ1I7WUFDQThCLEtBQUtDLENBQUFBO2dCQUNKakMsaUJBQWlCaUM7WUFDbEI7UUFDRDtRQUVBLHdEQUF3RDtRQUN4RCxJQUFJLE9BQU81RCxZQUFZNkQsSUFBSSxLQUFLLFlBQVk7WUFDM0M3RCxZQUFZNkQsSUFBSSxDQUFDNUQ7UUFDbEI7UUFFQSxPQUFPQTtJQUNSO0lBRUEsU0FBU29ELE9BQU9uQyxTQUFTLEVBQUU0QyxTQUFTO1FBQ25DLE1BQU1DLFdBQVcvRCxZQUFZLElBQUksQ0FBQ2tCLFNBQVMsR0FBSSxRQUFPNEMsY0FBYyxjQUFjLE1BQU1BLFNBQVEsSUFBSzVDO1FBQ3JHNkMsU0FBU2QsR0FBRyxHQUFHLElBQUksQ0FBQ0EsR0FBRztRQUN2QixPQUFPYztJQUNSO0lBRUE7Ozs7OztDQU1BLEdBQ0EsU0FBUzFELE9BQU9xRCxVQUFVO1FBQ3pCMUQsWUFBWWdFLElBQUksQ0FBQ047UUFDakIxRCxZQUFZMEQsVUFBVSxHQUFHQTtRQUV6QjFELFlBQVljLEtBQUssR0FBRyxFQUFFO1FBQ3RCZCxZQUFZZSxLQUFLLEdBQUcsRUFBRTtRQUV0QixJQUFJSztRQUNKLE1BQU02QyxRQUFRLENBQUMsT0FBT1AsZUFBZSxXQUFXQSxhQUFhLEVBQUMsRUFBR08sS0FBSyxDQUFDO1FBQ3ZFLE1BQU1DLE1BQU1ELE1BQU01QyxNQUFNO1FBRXhCLElBQUtELElBQUksR0FBR0EsSUFBSThDLEtBQUs5QyxJQUFLO1lBQ3pCLElBQUksQ0FBQzZDLEtBQUssQ0FBQzdDLEVBQUUsRUFBRTtnQkFFZDtZQUNEO1lBRUFzQyxhQUFhTyxLQUFLLENBQUM3QyxFQUFFLENBQUNvQixPQUFPLENBQUMsT0FBTztZQUVyQyxJQUFJa0IsVUFBVSxDQUFDLEVBQUUsS0FBSyxLQUFLO2dCQUMxQjFELFlBQVllLEtBQUssQ0FBQ29ELElBQUksQ0FBQyxJQUFJQyxPQUFPLE1BQU1WLFdBQVdXLEtBQUssQ0FBQyxLQUFLO1lBQy9ELE9BQU87Z0JBQ05yRSxZQUFZYyxLQUFLLENBQUNxRCxJQUFJLENBQUMsSUFBSUMsT0FBTyxNQUFNVixhQUFhO1lBQ3REO1FBQ0Q7SUFDRDtJQUVBOzs7OztDQUtBLEdBQ0EsU0FBU3REO1FBQ1IsTUFBTXNELGFBQWE7ZUFDZjFELFlBQVljLEtBQUssQ0FBQ3dELEdBQUcsQ0FBQ0M7ZUFDdEJ2RSxZQUFZZSxLQUFLLENBQUN1RCxHQUFHLENBQUNDLGFBQWFELEdBQUcsQ0FBQ3BELENBQUFBLFlBQWEsTUFBTUE7U0FDN0QsQ0FBQ3NELElBQUksQ0FBQztRQUNQeEUsWUFBWUssTUFBTSxDQUFDO1FBQ25CLE9BQU9xRDtJQUNSO0lBRUE7Ozs7OztDQU1BLEdBQ0EsU0FBU3BELFFBQVFtRSxJQUFJO1FBQ3BCLElBQUlBLElBQUksQ0FBQ0EsS0FBS3BELE1BQU0sR0FBRyxFQUFFLEtBQUssS0FBSztZQUNsQyxPQUFPO1FBQ1I7UUFFQSxJQUFJRDtRQUNKLElBQUk4QztRQUVKLElBQUs5QyxJQUFJLEdBQUc4QyxNQUFNbEUsWUFBWWUsS0FBSyxDQUFDTSxNQUFNLEVBQUVELElBQUk4QyxLQUFLOUMsSUFBSztZQUN6RCxJQUFJcEIsWUFBWWUsS0FBSyxDQUFDSyxFQUFFLENBQUNzRCxJQUFJLENBQUNELE9BQU87Z0JBQ3BDLE9BQU87WUFDUjtRQUNEO1FBRUEsSUFBS3JELElBQUksR0FBRzhDLE1BQU1sRSxZQUFZYyxLQUFLLENBQUNPLE1BQU0sRUFBRUQsSUFBSThDLEtBQUs5QyxJQUFLO1lBQ3pELElBQUlwQixZQUFZYyxLQUFLLENBQUNNLEVBQUUsQ0FBQ3NELElBQUksQ0FBQ0QsT0FBTztnQkFDcEMsT0FBTztZQUNSO1FBQ0Q7UUFFQSxPQUFPO0lBQ1I7SUFFQTs7Ozs7O0NBTUEsR0FDQSxTQUFTRixZQUFZSSxNQUFNO1FBQzFCLE9BQU9BLE9BQU9DLFFBQVEsR0FDcEJDLFNBQVMsQ0FBQyxHQUFHRixPQUFPQyxRQUFRLEdBQUd2RCxNQUFNLEdBQUcsR0FDeENtQixPQUFPLENBQUMsV0FBVztJQUN0QjtJQUVBOzs7Ozs7Q0FNQSxHQUNBLFNBQVNyQyxPQUFPeUMsR0FBRztRQUNsQixJQUFJQSxlQUFla0MsT0FBTztZQUN6QixPQUFPbEMsSUFBSW1DLEtBQUssSUFBSW5DLElBQUlvQyxPQUFPO1FBQ2hDO1FBQ0EsT0FBT3BDO0lBQ1I7SUFFQTs7O0NBR0EsR0FDQSxTQUFTbkM7UUFDUndFLFFBQVFDLElBQUksQ0FBQztJQUNkO0lBRUFsRixZQUFZSyxNQUFNLENBQUNMLFlBQVltRixJQUFJO0lBRW5DLE9BQU9uRjtBQUNSO0FBRUFvRixPQUFPQyxPQUFPLEdBQUd2RiIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zb2NrZXQuaW8tcGFyc2VyL25vZGVfbW9kdWxlcy9kZWJ1Zy9zcmMvY29tbW9uLmpzP2IxYTciXSwic291cmNlc0NvbnRlbnQiOlsiXG4vKipcbiAqIFRoaXMgaXMgdGhlIGNvbW1vbiBsb2dpYyBmb3IgYm90aCB0aGUgTm9kZS5qcyBhbmQgd2ViIGJyb3dzZXJcbiAqIGltcGxlbWVudGF0aW9ucyBvZiBgZGVidWcoKWAuXG4gKi9cblxuZnVuY3Rpb24gc2V0dXAoZW52KSB7XG5cdGNyZWF0ZURlYnVnLmRlYnVnID0gY3JlYXRlRGVidWc7XG5cdGNyZWF0ZURlYnVnLmRlZmF1bHQgPSBjcmVhdGVEZWJ1Zztcblx0Y3JlYXRlRGVidWcuY29lcmNlID0gY29lcmNlO1xuXHRjcmVhdGVEZWJ1Zy5kaXNhYmxlID0gZGlzYWJsZTtcblx0Y3JlYXRlRGVidWcuZW5hYmxlID0gZW5hYmxlO1xuXHRjcmVhdGVEZWJ1Zy5lbmFibGVkID0gZW5hYmxlZDtcblx0Y3JlYXRlRGVidWcuaHVtYW5pemUgPSByZXF1aXJlKCdtcycpO1xuXHRjcmVhdGVEZWJ1Zy5kZXN0cm95ID0gZGVzdHJveTtcblxuXHRPYmplY3Qua2V5cyhlbnYpLmZvckVhY2goa2V5ID0+IHtcblx0XHRjcmVhdGVEZWJ1Z1trZXldID0gZW52W2tleV07XG5cdH0pO1xuXG5cdC8qKlxuXHQqIFRoZSBjdXJyZW50bHkgYWN0aXZlIGRlYnVnIG1vZGUgbmFtZXMsIGFuZCBuYW1lcyB0byBza2lwLlxuXHQqL1xuXG5cdGNyZWF0ZURlYnVnLm5hbWVzID0gW107XG5cdGNyZWF0ZURlYnVnLnNraXBzID0gW107XG5cblx0LyoqXG5cdCogTWFwIG9mIHNwZWNpYWwgXCIlblwiIGhhbmRsaW5nIGZ1bmN0aW9ucywgZm9yIHRoZSBkZWJ1ZyBcImZvcm1hdFwiIGFyZ3VtZW50LlxuXHQqXG5cdCogVmFsaWQga2V5IG5hbWVzIGFyZSBhIHNpbmdsZSwgbG93ZXIgb3IgdXBwZXItY2FzZSBsZXR0ZXIsIGkuZS4gXCJuXCIgYW5kIFwiTlwiLlxuXHQqL1xuXHRjcmVhdGVEZWJ1Zy5mb3JtYXR0ZXJzID0ge307XG5cblx0LyoqXG5cdCogU2VsZWN0cyBhIGNvbG9yIGZvciBhIGRlYnVnIG5hbWVzcGFjZVxuXHQqIEBwYXJhbSB7U3RyaW5nfSBuYW1lc3BhY2UgVGhlIG5hbWVzcGFjZSBzdHJpbmcgZm9yIHRoZSBkZWJ1ZyBpbnN0YW5jZSB0byBiZSBjb2xvcmVkXG5cdCogQHJldHVybiB7TnVtYmVyfFN0cmluZ30gQW4gQU5TSSBjb2xvciBjb2RlIGZvciB0aGUgZ2l2ZW4gbmFtZXNwYWNlXG5cdCogQGFwaSBwcml2YXRlXG5cdCovXG5cdGZ1bmN0aW9uIHNlbGVjdENvbG9yKG5hbWVzcGFjZSkge1xuXHRcdGxldCBoYXNoID0gMDtcblxuXHRcdGZvciAobGV0IGkgPSAwOyBpIDwgbmFtZXNwYWNlLmxlbmd0aDsgaSsrKSB7XG5cdFx0XHRoYXNoID0gKChoYXNoIDw8IDUpIC0gaGFzaCkgKyBuYW1lc3BhY2UuY2hhckNvZGVBdChpKTtcblx0XHRcdGhhc2ggfD0gMDsgLy8gQ29udmVydCB0byAzMmJpdCBpbnRlZ2VyXG5cdFx0fVxuXG5cdFx0cmV0dXJuIGNyZWF0ZURlYnVnLmNvbG9yc1tNYXRoLmFicyhoYXNoKSAlIGNyZWF0ZURlYnVnLmNvbG9ycy5sZW5ndGhdO1xuXHR9XG5cdGNyZWF0ZURlYnVnLnNlbGVjdENvbG9yID0gc2VsZWN0Q29sb3I7XG5cblx0LyoqXG5cdCogQ3JlYXRlIGEgZGVidWdnZXIgd2l0aCB0aGUgZ2l2ZW4gYG5hbWVzcGFjZWAuXG5cdCpcblx0KiBAcGFyYW0ge1N0cmluZ30gbmFtZXNwYWNlXG5cdCogQHJldHVybiB7RnVuY3Rpb259XG5cdCogQGFwaSBwdWJsaWNcblx0Ki9cblx0ZnVuY3Rpb24gY3JlYXRlRGVidWcobmFtZXNwYWNlKSB7XG5cdFx0bGV0IHByZXZUaW1lO1xuXHRcdGxldCBlbmFibGVPdmVycmlkZSA9IG51bGw7XG5cdFx0bGV0IG5hbWVzcGFjZXNDYWNoZTtcblx0XHRsZXQgZW5hYmxlZENhY2hlO1xuXG5cdFx0ZnVuY3Rpb24gZGVidWcoLi4uYXJncykge1xuXHRcdFx0Ly8gRGlzYWJsZWQ/XG5cdFx0XHRpZiAoIWRlYnVnLmVuYWJsZWQpIHtcblx0XHRcdFx0cmV0dXJuO1xuXHRcdFx0fVxuXG5cdFx0XHRjb25zdCBzZWxmID0gZGVidWc7XG5cblx0XHRcdC8vIFNldCBgZGlmZmAgdGltZXN0YW1wXG5cdFx0XHRjb25zdCBjdXJyID0gTnVtYmVyKG5ldyBEYXRlKCkpO1xuXHRcdFx0Y29uc3QgbXMgPSBjdXJyIC0gKHByZXZUaW1lIHx8IGN1cnIpO1xuXHRcdFx0c2VsZi5kaWZmID0gbXM7XG5cdFx0XHRzZWxmLnByZXYgPSBwcmV2VGltZTtcblx0XHRcdHNlbGYuY3VyciA9IGN1cnI7XG5cdFx0XHRwcmV2VGltZSA9IGN1cnI7XG5cblx0XHRcdGFyZ3NbMF0gPSBjcmVhdGVEZWJ1Zy5jb2VyY2UoYXJnc1swXSk7XG5cblx0XHRcdGlmICh0eXBlb2YgYXJnc1swXSAhPT0gJ3N0cmluZycpIHtcblx0XHRcdFx0Ly8gQW55dGhpbmcgZWxzZSBsZXQncyBpbnNwZWN0IHdpdGggJU9cblx0XHRcdFx0YXJncy51bnNoaWZ0KCclTycpO1xuXHRcdFx0fVxuXG5cdFx0XHQvLyBBcHBseSBhbnkgYGZvcm1hdHRlcnNgIHRyYW5zZm9ybWF0aW9uc1xuXHRcdFx0bGV0IGluZGV4ID0gMDtcblx0XHRcdGFyZ3NbMF0gPSBhcmdzWzBdLnJlcGxhY2UoLyUoW2EtekEtWiVdKS9nLCAobWF0Y2gsIGZvcm1hdCkgPT4ge1xuXHRcdFx0XHQvLyBJZiB3ZSBlbmNvdW50ZXIgYW4gZXNjYXBlZCAlIHRoZW4gZG9uJ3QgaW5jcmVhc2UgdGhlIGFycmF5IGluZGV4XG5cdFx0XHRcdGlmIChtYXRjaCA9PT0gJyUlJykge1xuXHRcdFx0XHRcdHJldHVybiAnJSc7XG5cdFx0XHRcdH1cblx0XHRcdFx0aW5kZXgrKztcblx0XHRcdFx0Y29uc3QgZm9ybWF0dGVyID0gY3JlYXRlRGVidWcuZm9ybWF0dGVyc1tmb3JtYXRdO1xuXHRcdFx0XHRpZiAodHlwZW9mIGZvcm1hdHRlciA9PT0gJ2Z1bmN0aW9uJykge1xuXHRcdFx0XHRcdGNvbnN0IHZhbCA9IGFyZ3NbaW5kZXhdO1xuXHRcdFx0XHRcdG1hdGNoID0gZm9ybWF0dGVyLmNhbGwoc2VsZiwgdmFsKTtcblxuXHRcdFx0XHRcdC8vIE5vdyB3ZSBuZWVkIHRvIHJlbW92ZSBgYXJnc1tpbmRleF1gIHNpbmNlIGl0J3MgaW5saW5lZCBpbiB0aGUgYGZvcm1hdGBcblx0XHRcdFx0XHRhcmdzLnNwbGljZShpbmRleCwgMSk7XG5cdFx0XHRcdFx0aW5kZXgtLTtcblx0XHRcdFx0fVxuXHRcdFx0XHRyZXR1cm4gbWF0Y2g7XG5cdFx0XHR9KTtcblxuXHRcdFx0Ly8gQXBwbHkgZW52LXNwZWNpZmljIGZvcm1hdHRpbmcgKGNvbG9ycywgZXRjLilcblx0XHRcdGNyZWF0ZURlYnVnLmZvcm1hdEFyZ3MuY2FsbChzZWxmLCBhcmdzKTtcblxuXHRcdFx0Y29uc3QgbG9nRm4gPSBzZWxmLmxvZyB8fCBjcmVhdGVEZWJ1Zy5sb2c7XG5cdFx0XHRsb2dGbi5hcHBseShzZWxmLCBhcmdzKTtcblx0XHR9XG5cblx0XHRkZWJ1Zy5uYW1lc3BhY2UgPSBuYW1lc3BhY2U7XG5cdFx0ZGVidWcudXNlQ29sb3JzID0gY3JlYXRlRGVidWcudXNlQ29sb3JzKCk7XG5cdFx0ZGVidWcuY29sb3IgPSBjcmVhdGVEZWJ1Zy5zZWxlY3RDb2xvcihuYW1lc3BhY2UpO1xuXHRcdGRlYnVnLmV4dGVuZCA9IGV4dGVuZDtcblx0XHRkZWJ1Zy5kZXN0cm95ID0gY3JlYXRlRGVidWcuZGVzdHJveTsgLy8gWFhYIFRlbXBvcmFyeS4gV2lsbCBiZSByZW1vdmVkIGluIHRoZSBuZXh0IG1ham9yIHJlbGVhc2UuXG5cblx0XHRPYmplY3QuZGVmaW5lUHJvcGVydHkoZGVidWcsICdlbmFibGVkJywge1xuXHRcdFx0ZW51bWVyYWJsZTogdHJ1ZSxcblx0XHRcdGNvbmZpZ3VyYWJsZTogZmFsc2UsXG5cdFx0XHRnZXQ6ICgpID0+IHtcblx0XHRcdFx0aWYgKGVuYWJsZU92ZXJyaWRlICE9PSBudWxsKSB7XG5cdFx0XHRcdFx0cmV0dXJuIGVuYWJsZU92ZXJyaWRlO1xuXHRcdFx0XHR9XG5cdFx0XHRcdGlmIChuYW1lc3BhY2VzQ2FjaGUgIT09IGNyZWF0ZURlYnVnLm5hbWVzcGFjZXMpIHtcblx0XHRcdFx0XHRuYW1lc3BhY2VzQ2FjaGUgPSBjcmVhdGVEZWJ1Zy5uYW1lc3BhY2VzO1xuXHRcdFx0XHRcdGVuYWJsZWRDYWNoZSA9IGNyZWF0ZURlYnVnLmVuYWJsZWQobmFtZXNwYWNlKTtcblx0XHRcdFx0fVxuXG5cdFx0XHRcdHJldHVybiBlbmFibGVkQ2FjaGU7XG5cdFx0XHR9LFxuXHRcdFx0c2V0OiB2ID0+IHtcblx0XHRcdFx0ZW5hYmxlT3ZlcnJpZGUgPSB2O1xuXHRcdFx0fVxuXHRcdH0pO1xuXG5cdFx0Ly8gRW52LXNwZWNpZmljIGluaXRpYWxpemF0aW9uIGxvZ2ljIGZvciBkZWJ1ZyBpbnN0YW5jZXNcblx0XHRpZiAodHlwZW9mIGNyZWF0ZURlYnVnLmluaXQgPT09ICdmdW5jdGlvbicpIHtcblx0XHRcdGNyZWF0ZURlYnVnLmluaXQoZGVidWcpO1xuXHRcdH1cblxuXHRcdHJldHVybiBkZWJ1Zztcblx0fVxuXG5cdGZ1bmN0aW9uIGV4dGVuZChuYW1lc3BhY2UsIGRlbGltaXRlcikge1xuXHRcdGNvbnN0IG5ld0RlYnVnID0gY3JlYXRlRGVidWcodGhpcy5uYW1lc3BhY2UgKyAodHlwZW9mIGRlbGltaXRlciA9PT0gJ3VuZGVmaW5lZCcgPyAnOicgOiBkZWxpbWl0ZXIpICsgbmFtZXNwYWNlKTtcblx0XHRuZXdEZWJ1Zy5sb2cgPSB0aGlzLmxvZztcblx0XHRyZXR1cm4gbmV3RGVidWc7XG5cdH1cblxuXHQvKipcblx0KiBFbmFibGVzIGEgZGVidWcgbW9kZSBieSBuYW1lc3BhY2VzLiBUaGlzIGNhbiBpbmNsdWRlIG1vZGVzXG5cdCogc2VwYXJhdGVkIGJ5IGEgY29sb24gYW5kIHdpbGRjYXJkcy5cblx0KlxuXHQqIEBwYXJhbSB7U3RyaW5nfSBuYW1lc3BhY2VzXG5cdCogQGFwaSBwdWJsaWNcblx0Ki9cblx0ZnVuY3Rpb24gZW5hYmxlKG5hbWVzcGFjZXMpIHtcblx0XHRjcmVhdGVEZWJ1Zy5zYXZlKG5hbWVzcGFjZXMpO1xuXHRcdGNyZWF0ZURlYnVnLm5hbWVzcGFjZXMgPSBuYW1lc3BhY2VzO1xuXG5cdFx0Y3JlYXRlRGVidWcubmFtZXMgPSBbXTtcblx0XHRjcmVhdGVEZWJ1Zy5za2lwcyA9IFtdO1xuXG5cdFx0bGV0IGk7XG5cdFx0Y29uc3Qgc3BsaXQgPSAodHlwZW9mIG5hbWVzcGFjZXMgPT09ICdzdHJpbmcnID8gbmFtZXNwYWNlcyA6ICcnKS5zcGxpdCgvW1xccyxdKy8pO1xuXHRcdGNvbnN0IGxlbiA9IHNwbGl0Lmxlbmd0aDtcblxuXHRcdGZvciAoaSA9IDA7IGkgPCBsZW47IGkrKykge1xuXHRcdFx0aWYgKCFzcGxpdFtpXSkge1xuXHRcdFx0XHQvLyBpZ25vcmUgZW1wdHkgc3RyaW5nc1xuXHRcdFx0XHRjb250aW51ZTtcblx0XHRcdH1cblxuXHRcdFx0bmFtZXNwYWNlcyA9IHNwbGl0W2ldLnJlcGxhY2UoL1xcKi9nLCAnLio/Jyk7XG5cblx0XHRcdGlmIChuYW1lc3BhY2VzWzBdID09PSAnLScpIHtcblx0XHRcdFx0Y3JlYXRlRGVidWcuc2tpcHMucHVzaChuZXcgUmVnRXhwKCdeJyArIG5hbWVzcGFjZXMuc2xpY2UoMSkgKyAnJCcpKTtcblx0XHRcdH0gZWxzZSB7XG5cdFx0XHRcdGNyZWF0ZURlYnVnLm5hbWVzLnB1c2gobmV3IFJlZ0V4cCgnXicgKyBuYW1lc3BhY2VzICsgJyQnKSk7XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cblx0LyoqXG5cdCogRGlzYWJsZSBkZWJ1ZyBvdXRwdXQuXG5cdCpcblx0KiBAcmV0dXJuIHtTdHJpbmd9IG5hbWVzcGFjZXNcblx0KiBAYXBpIHB1YmxpY1xuXHQqL1xuXHRmdW5jdGlvbiBkaXNhYmxlKCkge1xuXHRcdGNvbnN0IG5hbWVzcGFjZXMgPSBbXG5cdFx0XHQuLi5jcmVhdGVEZWJ1Zy5uYW1lcy5tYXAodG9OYW1lc3BhY2UpLFxuXHRcdFx0Li4uY3JlYXRlRGVidWcuc2tpcHMubWFwKHRvTmFtZXNwYWNlKS5tYXAobmFtZXNwYWNlID0+ICctJyArIG5hbWVzcGFjZSlcblx0XHRdLmpvaW4oJywnKTtcblx0XHRjcmVhdGVEZWJ1Zy5lbmFibGUoJycpO1xuXHRcdHJldHVybiBuYW1lc3BhY2VzO1xuXHR9XG5cblx0LyoqXG5cdCogUmV0dXJucyB0cnVlIGlmIHRoZSBnaXZlbiBtb2RlIG5hbWUgaXMgZW5hYmxlZCwgZmFsc2Ugb3RoZXJ3aXNlLlxuXHQqXG5cdCogQHBhcmFtIHtTdHJpbmd9IG5hbWVcblx0KiBAcmV0dXJuIHtCb29sZWFufVxuXHQqIEBhcGkgcHVibGljXG5cdCovXG5cdGZ1bmN0aW9uIGVuYWJsZWQobmFtZSkge1xuXHRcdGlmIChuYW1lW25hbWUubGVuZ3RoIC0gMV0gPT09ICcqJykge1xuXHRcdFx0cmV0dXJuIHRydWU7XG5cdFx0fVxuXG5cdFx0bGV0IGk7XG5cdFx0bGV0IGxlbjtcblxuXHRcdGZvciAoaSA9IDAsIGxlbiA9IGNyZWF0ZURlYnVnLnNraXBzLmxlbmd0aDsgaSA8IGxlbjsgaSsrKSB7XG5cdFx0XHRpZiAoY3JlYXRlRGVidWcuc2tpcHNbaV0udGVzdChuYW1lKSkge1xuXHRcdFx0XHRyZXR1cm4gZmFsc2U7XG5cdFx0XHR9XG5cdFx0fVxuXG5cdFx0Zm9yIChpID0gMCwgbGVuID0gY3JlYXRlRGVidWcubmFtZXMubGVuZ3RoOyBpIDwgbGVuOyBpKyspIHtcblx0XHRcdGlmIChjcmVhdGVEZWJ1Zy5uYW1lc1tpXS50ZXN0KG5hbWUpKSB7XG5cdFx0XHRcdHJldHVybiB0cnVlO1xuXHRcdFx0fVxuXHRcdH1cblxuXHRcdHJldHVybiBmYWxzZTtcblx0fVxuXG5cdC8qKlxuXHQqIENvbnZlcnQgcmVnZXhwIHRvIG5hbWVzcGFjZVxuXHQqXG5cdCogQHBhcmFtIHtSZWdFeHB9IHJlZ3hlcFxuXHQqIEByZXR1cm4ge1N0cmluZ30gbmFtZXNwYWNlXG5cdCogQGFwaSBwcml2YXRlXG5cdCovXG5cdGZ1bmN0aW9uIHRvTmFtZXNwYWNlKHJlZ2V4cCkge1xuXHRcdHJldHVybiByZWdleHAudG9TdHJpbmcoKVxuXHRcdFx0LnN1YnN0cmluZygyLCByZWdleHAudG9TdHJpbmcoKS5sZW5ndGggLSAyKVxuXHRcdFx0LnJlcGxhY2UoL1xcLlxcKlxcPyQvLCAnKicpO1xuXHR9XG5cblx0LyoqXG5cdCogQ29lcmNlIGB2YWxgLlxuXHQqXG5cdCogQHBhcmFtIHtNaXhlZH0gdmFsXG5cdCogQHJldHVybiB7TWl4ZWR9XG5cdCogQGFwaSBwcml2YXRlXG5cdCovXG5cdGZ1bmN0aW9uIGNvZXJjZSh2YWwpIHtcblx0XHRpZiAodmFsIGluc3RhbmNlb2YgRXJyb3IpIHtcblx0XHRcdHJldHVybiB2YWwuc3RhY2sgfHwgdmFsLm1lc3NhZ2U7XG5cdFx0fVxuXHRcdHJldHVybiB2YWw7XG5cdH1cblxuXHQvKipcblx0KiBYWFggRE8gTk9UIFVTRS4gVGhpcyBpcyBhIHRlbXBvcmFyeSBzdHViIGZ1bmN0aW9uLlxuXHQqIFhYWCBJdCBXSUxMIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgcmVsZWFzZS5cblx0Ki9cblx0ZnVuY3Rpb24gZGVzdHJveSgpIHtcblx0XHRjb25zb2xlLndhcm4oJ0luc3RhbmNlIG1ldGhvZCBgZGVidWcuZGVzdHJveSgpYCBpcyBkZXByZWNhdGVkIGFuZCBubyBsb25nZXIgZG9lcyBhbnl0aGluZy4gSXQgd2lsbCBiZSByZW1vdmVkIGluIHRoZSBuZXh0IG1ham9yIHZlcnNpb24gb2YgYGRlYnVnYC4nKTtcblx0fVxuXG5cdGNyZWF0ZURlYnVnLmVuYWJsZShjcmVhdGVEZWJ1Zy5sb2FkKCkpO1xuXG5cdHJldHVybiBjcmVhdGVEZWJ1Zztcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBzZXR1cDtcbiJdLCJuYW1lcyI6WyJzZXR1cCIsImVudiIsImNyZWF0ZURlYnVnIiwiZGVidWciLCJkZWZhdWx0IiwiY29lcmNlIiwiZGlzYWJsZSIsImVuYWJsZSIsImVuYWJsZWQiLCJodW1hbml6ZSIsInJlcXVpcmUiLCJkZXN0cm95IiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJrZXkiLCJuYW1lcyIsInNraXBzIiwiZm9ybWF0dGVycyIsInNlbGVjdENvbG9yIiwibmFtZXNwYWNlIiwiaGFzaCIsImkiLCJsZW5ndGgiLCJjaGFyQ29kZUF0IiwiY29sb3JzIiwiTWF0aCIsImFicyIsInByZXZUaW1lIiwiZW5hYmxlT3ZlcnJpZGUiLCJuYW1lc3BhY2VzQ2FjaGUiLCJlbmFibGVkQ2FjaGUiLCJhcmdzIiwic2VsZiIsImN1cnIiLCJOdW1iZXIiLCJEYXRlIiwibXMiLCJkaWZmIiwicHJldiIsInVuc2hpZnQiLCJpbmRleCIsInJlcGxhY2UiLCJtYXRjaCIsImZvcm1hdCIsImZvcm1hdHRlciIsInZhbCIsImNhbGwiLCJzcGxpY2UiLCJmb3JtYXRBcmdzIiwibG9nRm4iLCJsb2ciLCJhcHBseSIsInVzZUNvbG9ycyIsImNvbG9yIiwiZXh0ZW5kIiwiZGVmaW5lUHJvcGVydHkiLCJlbnVtZXJhYmxlIiwiY29uZmlndXJhYmxlIiwiZ2V0IiwibmFtZXNwYWNlcyIsInNldCIsInYiLCJpbml0IiwiZGVsaW1pdGVyIiwibmV3RGVidWciLCJzYXZlIiwic3BsaXQiLCJsZW4iLCJwdXNoIiwiUmVnRXhwIiwic2xpY2UiLCJtYXAiLCJ0b05hbWVzcGFjZSIsImpvaW4iLCJuYW1lIiwidGVzdCIsInJlZ2V4cCIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwiRXJyb3IiLCJzdGFjayIsIm1lc3NhZ2UiLCJjb25zb2xlIiwid2FybiIsImxvYWQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/socket.io-parser/node_modules/debug/src/index.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */ if (typeof process === \"undefined\" || process.type === \"renderer\" || false === true || process.__nwjs) {\n    module.exports = __webpack_require__(/*! ./browser.js */ \"(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/browser.js\");\n} else {\n    module.exports = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLXBhcnNlci9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUVELElBQUksT0FBT0EsWUFBWSxlQUFlQSxRQUFRQyxJQUFJLEtBQUssY0FBY0QsS0FBZSxLQUFLLFFBQVFBLFFBQVFHLE1BQU0sRUFBRTtJQUNoSEMsbUlBQXlCO0FBQzFCLE9BQU87SUFDTkEsNkhBQXlCO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1wYXJzZXIvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9pbmRleC5qcz8yMTMzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGV0ZWN0IEVsZWN0cm9uIHJlbmRlcmVyIC8gbndqcyBwcm9jZXNzLCB3aGljaCBpcyBub2RlLCBidXQgd2Ugc2hvdWxkXG4gKiB0cmVhdCBhcyBhIGJyb3dzZXIuXG4gKi9cblxuaWYgKHR5cGVvZiBwcm9jZXNzID09PSAndW5kZWZpbmVkJyB8fCBwcm9jZXNzLnR5cGUgPT09ICdyZW5kZXJlcicgfHwgcHJvY2Vzcy5icm93c2VyID09PSB0cnVlIHx8IHByb2Nlc3MuX19ud2pzKSB7XG5cdG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9icm93c2VyLmpzJyk7XG59IGVsc2Uge1xuXHRtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbm9kZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJ0eXBlIiwiYnJvd3NlciIsIl9fbndqcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/node.js":
/*!**********************************************************************!*\
  !*** ./node_modules/socket.io-parser/node_modules/debug/src/node.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */ const tty = __webpack_require__(/*! tty */ \"tty\");\nconst util = __webpack_require__(/*! util */ \"util\");\n/**\n * This is the Node.js implementation of `debug()`.\n */ exports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(()=>{}, \"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n/**\n * Colors.\n */ exports.colors = [\n    6,\n    2,\n    3,\n    4,\n    5,\n    1\n];\ntry {\n    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const supportsColor = __webpack_require__(/*! supports-color */ \"(ssr)/./node_modules/supports-color/index.js\");\n    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n        exports.colors = [\n            20,\n            21,\n            26,\n            27,\n            32,\n            33,\n            38,\n            39,\n            40,\n            41,\n            42,\n            43,\n            44,\n            45,\n            56,\n            57,\n            62,\n            63,\n            68,\n            69,\n            74,\n            75,\n            76,\n            77,\n            78,\n            79,\n            80,\n            81,\n            92,\n            93,\n            98,\n            99,\n            112,\n            113,\n            128,\n            129,\n            134,\n            135,\n            148,\n            149,\n            160,\n            161,\n            162,\n            163,\n            164,\n            165,\n            166,\n            167,\n            168,\n            169,\n            170,\n            171,\n            172,\n            173,\n            178,\n            179,\n            184,\n            185,\n            196,\n            197,\n            198,\n            199,\n            200,\n            201,\n            202,\n            203,\n            204,\n            205,\n            206,\n            207,\n            208,\n            209,\n            214,\n            215,\n            220,\n            221\n        ];\n    }\n} catch (error) {\n// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */ exports.inspectOpts = Object.keys(process.env).filter((key)=>{\n    return /^debug_/i.test(key);\n}).reduce((obj, key)=>{\n    // Camel-case\n    const prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, (_, k)=>{\n        return k.toUpperCase();\n    });\n    // Coerce string value into JS value\n    let val = process.env[key];\n    if (/^(yes|on|true|enabled)$/i.test(val)) {\n        val = true;\n    } else if (/^(no|off|false|disabled)$/i.test(val)) {\n        val = false;\n    } else if (val === \"null\") {\n        val = null;\n    } else {\n        val = Number(val);\n    }\n    obj[prop] = val;\n    return obj;\n}, {});\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */ function useColors() {\n    return \"colors\" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);\n}\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    const { namespace: name, useColors } = this;\n    if (useColors) {\n        const c = this.color;\n        const colorCode = \"\\x1b[3\" + (c < 8 ? c : \"8;5;\" + c);\n        const prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n        args[0] = prefix + args[0].split(\"\\n\").join(\"\\n\" + prefix);\n        args.push(colorCode + \"m+\" + module.exports.humanize(this.diff) + \"\\x1b[0m\");\n    } else {\n        args[0] = getDate() + name + \" \" + args[0];\n    }\n}\nfunction getDate() {\n    if (exports.inspectOpts.hideDate) {\n        return \"\";\n    }\n    return new Date().toISOString() + \" \";\n}\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */ function log(...args) {\n    return process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + \"\\n\");\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    if (namespaces) {\n        process.env.DEBUG = namespaces;\n    } else {\n        // If you set a process.env field to null or undefined, it gets cast to the\n        // string 'null' or 'undefined'. Just delete instead.\n        delete process.env.DEBUG;\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    return process.env.DEBUG;\n}\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */ function init(debug) {\n    debug.inspectOpts = {};\n    const keys = Object.keys(exports.inspectOpts);\n    for(let i = 0; i < keys.length; i++){\n        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */ formatters.o = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts).split(\"\\n\").map((str)=>str.trim()).join(\" \");\n};\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */ formatters.O = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js":
/*!*****************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/binary.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deconstructPacket: () => (/* binding */ deconstructPacket),\n/* harmony export */   reconstructPacket: () => (/* binding */ reconstructPacket)\n/* harmony export */ });\n/* harmony import */ var _is_binary_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\");\n\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */ function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return {\n        packet: pack,\n        buffers: buffers\n    };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data) return data;\n    if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_0__.isBinary)(data)) {\n        const placeholder = {\n            _placeholder: true,\n            num: buffers.length\n        };\n        buffers.push(data);\n        return placeholder;\n    } else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for(let i = 0; i < data.length; i++){\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    } else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for(const key in data){\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */ function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data) return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" && data.num >= 0 && data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        } else {\n            throw new Error(\"illegal attachments\");\n        }\n    } else if (Array.isArray(data)) {\n        for(let i = 0; i < data.length; i++){\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    } else if (typeof data === \"object\") {\n        for(const key in data){\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder),\n/* harmony export */   Encoder: () => (/* binding */ Encoder),\n/* harmony export */   PacketType: () => (/* binding */ PacketType),\n/* harmony export */   protocol: () => (/* binding */ protocol)\n/* harmony export */ });\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js\");\n/* harmony import */ var _is_binary_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-parser/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-parser\"); // debug()\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */ const RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\"\n];\n/**\n * Protocol version.\n *\n * @public\n */ const protocol = 5;\nvar PacketType;\n(function(PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */ class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */ constructor(replacer){\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */ encode(obj) {\n        debug(\"encoding packet %j\", obj);\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_2__.hasBinary)(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT ? PacketType.BINARY_EVENT : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id\n                });\n            }\n        }\n        return [\n            this.encodeAsString(obj)\n        ];\n    }\n    /**\n     * Encode packet as string.\n     */ encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT || obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        debug(\"encoded %j as %s\", obj, str);\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */ encodeAsBinary(obj) {\n        const deconstruction = (0,_binary_js__WEBPACK_IMPORTED_MODULE_1__.deconstructPacket)(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */ class Decoder extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_0__.Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */ constructor(reviver){\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */ add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            } else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        } else if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_2__.isBinary)(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            } else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        } else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */ decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0))\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT || p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while(str.charAt(++i) !== \"-\" && i != str.length){}\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while(++i){\n                const c = str.charAt(i);\n                if (\",\" === c) break;\n                if (i === str.length) break;\n            }\n            p.nsp = str.substring(start, i);\n        } else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while(++i){\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length) break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            } else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        debug(\"decoded %s as %j\", str, p);\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        } catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch(type){\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && (typeof payload[0] === \"number\" || typeof payload[0] === \"string\" && RESERVED_EVENTS.indexOf(payload[0]) === -1);\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */ destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */ class BinaryReconstructor {\n    constructor(packet){\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */ takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = (0,_binary_js__WEBPACK_IMPORTED_MODULE_1__.reconstructPacket)(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */ finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js":
/*!********************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/is-binary.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasBinary: () => (/* binding */ hasBinary),\n/* harmony export */   isBinary: () => (/* binding */ isBinary)\n/* harmony export */ });\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj)=>{\n    return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeFile = typeof File === \"function\" || typeof File !== \"undefined\" && toString.call(File) === \"[object FileConstructor]\";\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */ function isBinary(obj) {\n    return withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)) || withNativeBlob && obj instanceof Blob || withNativeFile && obj instanceof File;\n}\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for(let i = 0, l = obj.length; i < l; i++){\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON && typeof obj.toJSON === \"function\" && arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for(const key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLXBhcnNlci9idWlsZC9lc20tZGVidWcvaXMtYmluYXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsTUFBTUEsd0JBQXdCLE9BQU9DLGdCQUFnQjtBQUNyRCxNQUFNQyxTQUFTLENBQUNDO0lBQ1osT0FBTyxPQUFPRixZQUFZQyxNQUFNLEtBQUssYUFDL0JELFlBQVlDLE1BQU0sQ0FBQ0MsT0FDbkJBLElBQUlDLE1BQU0sWUFBWUg7QUFDaEM7QUFDQSxNQUFNSSxXQUFXQyxPQUFPQyxTQUFTLENBQUNGLFFBQVE7QUFDMUMsTUFBTUcsaUJBQWlCLE9BQU9DLFNBQVMsY0FDbEMsT0FBT0EsU0FBUyxlQUNiSixTQUFTSyxJQUFJLENBQUNELFVBQVU7QUFDaEMsTUFBTUUsaUJBQWlCLE9BQU9DLFNBQVMsY0FDbEMsT0FBT0EsU0FBUyxlQUNiUCxTQUFTSyxJQUFJLENBQUNFLFVBQVU7QUFDaEM7Ozs7Q0FJQyxHQUNNLFNBQVNDLFNBQVNWLEdBQUc7SUFDeEIsT0FBUSx5QkFBMkJBLENBQUFBLGVBQWVGLGVBQWVDLE9BQU9DLElBQUcsS0FDdEVLLGtCQUFrQkwsZUFBZU0sUUFDakNFLGtCQUFrQlIsZUFBZVM7QUFDMUM7QUFDTyxTQUFTRSxVQUFVWCxHQUFHLEVBQUVZLE1BQU07SUFDakMsSUFBSSxDQUFDWixPQUFPLE9BQU9BLFFBQVEsVUFBVTtRQUNqQyxPQUFPO0lBQ1g7SUFDQSxJQUFJYSxNQUFNQyxPQUFPLENBQUNkLE1BQU07UUFDcEIsSUFBSyxJQUFJZSxJQUFJLEdBQUdDLElBQUloQixJQUFJaUIsTUFBTSxFQUFFRixJQUFJQyxHQUFHRCxJQUFLO1lBQ3hDLElBQUlKLFVBQVVYLEdBQUcsQ0FBQ2UsRUFBRSxHQUFHO2dCQUNuQixPQUFPO1lBQ1g7UUFDSjtRQUNBLE9BQU87SUFDWDtJQUNBLElBQUlMLFNBQVNWLE1BQU07UUFDZixPQUFPO0lBQ1g7SUFDQSxJQUFJQSxJQUFJWSxNQUFNLElBQ1YsT0FBT1osSUFBSVksTUFBTSxLQUFLLGNBQ3RCTSxVQUFVRCxNQUFNLEtBQUssR0FBRztRQUN4QixPQUFPTixVQUFVWCxJQUFJWSxNQUFNLElBQUk7SUFDbkM7SUFDQSxJQUFLLE1BQU1PLE9BQU9uQixJQUFLO1FBQ25CLElBQUlHLE9BQU9DLFNBQVMsQ0FBQ2dCLGNBQWMsQ0FBQ2IsSUFBSSxDQUFDUCxLQUFLbUIsUUFBUVIsVUFBVVgsR0FBRyxDQUFDbUIsSUFBSSxHQUFHO1lBQ3ZFLE9BQU87UUFDWDtJQUNKO0lBQ0EsT0FBTztBQUNYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1wYXJzZXIvYnVpbGQvZXNtLWRlYnVnL2lzLWJpbmFyeS5qcz8xNzMxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHdpdGhOYXRpdmVBcnJheUJ1ZmZlciA9IHR5cGVvZiBBcnJheUJ1ZmZlciA9PT0gXCJmdW5jdGlvblwiO1xuY29uc3QgaXNWaWV3ID0gKG9iaikgPT4ge1xuICAgIHJldHVybiB0eXBlb2YgQXJyYXlCdWZmZXIuaXNWaWV3ID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBBcnJheUJ1ZmZlci5pc1ZpZXcob2JqKVxuICAgICAgICA6IG9iai5idWZmZXIgaW5zdGFuY2VvZiBBcnJheUJ1ZmZlcjtcbn07XG5jb25zdCB0b1N0cmluZyA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmc7XG5jb25zdCB3aXRoTmF0aXZlQmxvYiA9IHR5cGVvZiBCbG9iID09PSBcImZ1bmN0aW9uXCIgfHxcbiAgICAodHlwZW9mIEJsb2IgIT09IFwidW5kZWZpbmVkXCIgJiZcbiAgICAgICAgdG9TdHJpbmcuY2FsbChCbG9iKSA9PT0gXCJbb2JqZWN0IEJsb2JDb25zdHJ1Y3Rvcl1cIik7XG5jb25zdCB3aXRoTmF0aXZlRmlsZSA9IHR5cGVvZiBGaWxlID09PSBcImZ1bmN0aW9uXCIgfHxcbiAgICAodHlwZW9mIEZpbGUgIT09IFwidW5kZWZpbmVkXCIgJiZcbiAgICAgICAgdG9TdHJpbmcuY2FsbChGaWxlKSA9PT0gXCJbb2JqZWN0IEZpbGVDb25zdHJ1Y3Rvcl1cIik7XG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiBvYmogaXMgYSBCdWZmZXIsIGFuIEFycmF5QnVmZmVyLCBhIEJsb2Igb3IgYSBGaWxlLlxuICpcbiAqIEBwcml2YXRlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0JpbmFyeShvYmopIHtcbiAgICByZXR1cm4gKCh3aXRoTmF0aXZlQXJyYXlCdWZmZXIgJiYgKG9iaiBpbnN0YW5jZW9mIEFycmF5QnVmZmVyIHx8IGlzVmlldyhvYmopKSkgfHxcbiAgICAgICAgKHdpdGhOYXRpdmVCbG9iICYmIG9iaiBpbnN0YW5jZW9mIEJsb2IpIHx8XG4gICAgICAgICh3aXRoTmF0aXZlRmlsZSAmJiBvYmogaW5zdGFuY2VvZiBGaWxlKSk7XG59XG5leHBvcnQgZnVuY3Rpb24gaGFzQmluYXJ5KG9iaiwgdG9KU09OKSB7XG4gICAgaWYgKCFvYmogfHwgdHlwZW9mIG9iaiAhPT0gXCJvYmplY3RcIikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChBcnJheS5pc0FycmF5KG9iaikpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDAsIGwgPSBvYmoubGVuZ3RoOyBpIDwgbDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAoaGFzQmluYXJ5KG9ialtpXSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChpc0JpbmFyeShvYmopKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAob2JqLnRvSlNPTiAmJlxuICAgICAgICB0eXBlb2Ygb2JqLnRvSlNPTiA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgICAgIGFyZ3VtZW50cy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgcmV0dXJuIGhhc0JpbmFyeShvYmoudG9KU09OKCksIHRydWUpO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IGtleSBpbiBvYmopIHtcbiAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvYmosIGtleSkgJiYgaGFzQmluYXJ5KG9ialtrZXldKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuIl0sIm5hbWVzIjpbIndpdGhOYXRpdmVBcnJheUJ1ZmZlciIsIkFycmF5QnVmZmVyIiwiaXNWaWV3Iiwib2JqIiwiYnVmZmVyIiwidG9TdHJpbmciLCJPYmplY3QiLCJwcm90b3R5cGUiLCJ3aXRoTmF0aXZlQmxvYiIsIkJsb2IiLCJjYWxsIiwid2l0aE5hdGl2ZUZpbGUiLCJGaWxlIiwiaXNCaW5hcnkiLCJoYXNCaW5hcnkiLCJ0b0pTT04iLCJBcnJheSIsImlzQXJyYXkiLCJpIiwibCIsImxlbmd0aCIsImFyZ3VtZW50cyIsImtleSIsImhhc093blByb3BlcnR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\n");

/***/ })

};
;