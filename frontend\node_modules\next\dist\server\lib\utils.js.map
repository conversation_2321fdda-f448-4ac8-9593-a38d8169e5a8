{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "names": ["printAndExit", "getDebugPort", "getNodeOptionsWithoutInspect", "getPort", "RESTART_EXIT_CODE", "checkNodeDebugType", "getMaxOldSpaceSize", "message", "code", "console", "log", "error", "process", "exit", "debugPortStr", "execArgv", "find", "localArg", "startsWith", "split", "env", "NODE_OPTIONS", "match", "parseInt", "NODE_INSPECT_RE", "replace", "args", "parsed", "PORT", "Number", "isNaN", "nodeDebugType", "undefined", "some", "maxOldSpaceSize"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAEgBA,YAAY;eAAZA;;IAUHC,YAAY;eAAZA;;IAcGC,4BAA4B;eAA5BA;;IAIAC,OAAO;eAAPA;;IAaHC,iBAAiB;eAAjBA;;IAEGC,kBAAkB;eAAlBA;;IAoBAC,kBAAkB;eAAlBA;;;AA/DT,SAASN,aAAaO,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEAK,QAAQC,IAAI,CAACL;AACf;AAEO,MAAMP,eAAe;QAExBW,wBAOAA,iCAAAA,kCAAAA;IARF,MAAME,eACJF,EAAAA,yBAAAA,QAAQG,QAAQ,CACbC,IAAI,CACH,CAACC,WACCA,SAASC,UAAU,CAAC,gBACpBD,SAASC,UAAU,CAAC,sCAJ1BN,uBAMIO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACpBP,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,mCAAAA,0BAA0BU,KAAK,sBAA/BV,kCAAAA,sCAAAA,2BAAkC,sDAAlCA,+BAAqE,CAAC,EAAE;IAC1E,OAAOE,eAAeS,SAAST,cAAc,MAAM;AACrD;AAEA,MAAMU,kBAAkB;AACjB,SAAStB;IACd,OAAO,AAACU,CAAAA,QAAQQ,GAAG,CAACC,YAAY,IAAI,EAAC,EAAGI,OAAO,CAACD,iBAAiB;AACnE;AAEO,SAASrB,QAAQuB,IAA0B;IAChD,IAAI,OAAOA,IAAI,CAAC,SAAS,KAAK,UAAU;QACtC,OAAOA,IAAI,CAAC,SAAS;IACvB;IAEA,MAAMC,SAASf,QAAQQ,GAAG,CAACQ,IAAI,IAAIL,SAASX,QAAQQ,GAAG,CAACQ,IAAI,EAAE;IAC9D,IAAI,OAAOD,WAAW,YAAY,CAACE,OAAOC,KAAK,CAACH,SAAS;QACvD,OAAOA;IACT;IAEA,OAAO;AACT;AAEO,MAAMvB,oBAAoB;AAE1B,SAASC;QAKZO,iCAAAA,2BAOAA,kCAAAA;IAXF,IAAImB,gBAAgBC;IAEpB,IACEpB,QAAQG,QAAQ,CAACkB,IAAI,CAAC,CAAChB,WAAaA,SAASC,UAAU,CAAC,mBACxDN,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,kCAAAA,0BAA0BU,KAAK,qBAA/BV,qCAAAA,2BAAkC,2BAClC;QACAmB,gBAAgB;IAClB;IAEA,IACEnB,QAAQG,QAAQ,CAACkB,IAAI,CAAC,CAAChB,WAAaA,SAASC,UAAU,CAAC,uBACxDN,6BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,mCAAAA,2BAA0BU,KAAK,qBAA/BV,sCAAAA,4BAAkC,+BAClC;QACAmB,gBAAgB;IAClB;IAEA,OAAOA;AACT;AAEO,SAASzB;QACUM,iCAAAA;IAAxB,MAAMsB,mBAAkBtB,4BAAAA,QAAQQ,GAAG,CAACC,YAAY,sBAAxBT,kCAAAA,0BAA0BU,KAAK,CACrD,kDADsBV,+BAErB,CAAC,EAAE;IAEN,OAAOsB,kBAAkBX,SAASW,iBAAiB,MAAMF;AAC3D"}