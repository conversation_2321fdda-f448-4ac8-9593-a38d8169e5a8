/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cwa%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cwa%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cwa%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cwa%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cwa%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cwa%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Capp%5Cglobals.css&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Capp%5Cglobals.css&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Ccomponents%5CQRScanner.tsx&server=true!":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Ccomponents%5CQRScanner.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/QRScanner.tsx */ \"(ssr)/./components/QRScanner.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1Byb2plY3QlNUN3YSU1Q2Zyb250ZW5kJTVDY29tcG9uZW50cyU1Q1FSU2Nhbm5lci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLz9lYjNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdFxcXFx3YVxcXFxmcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXFFSU2Nhbm5lci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Ccomponents%5CQRScanner.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CProject%5Cwa%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/QRScanner.tsx":
/*!**********************************!*\
  !*** ./components/QRScanner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRScanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/QrCodeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ExclamationTriangleIcon,PhoneIcon,QrCodeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction QRScanner() {\n    const [botStatus, setBotStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: \"disconnected\"\n    });\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Connect to Socket.IO server\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:3000\" || 0);\n        setSocket(newSocket);\n        newSocket.on(\"connect\", ()=>{\n            console.log(\"Connected to server\");\n            setIsConnecting(false);\n        });\n        newSocket.on(\"disconnect\", ()=>{\n            console.log(\"Disconnected from server\");\n            setIsConnecting(true);\n        });\n        newSocket.on(\"bot-status\", (data)=>{\n            setBotStatus(data);\n        });\n        newSocket.on(\"qr-code\", (data)=>{\n            setBotStatus((prev)=>({\n                    ...prev,\n                    qrCode: data.qrCode,\n                    status: data.status\n                }));\n        });\n        // Request initial status\n        newSocket.emit(\"request-qr\");\n        return ()=>{\n            newSocket.close();\n        };\n    }, []);\n    const getStatusInfo = ()=>{\n        switch(botStatus.status){\n            case \"disconnected\":\n                return {\n                    icon: _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: \"Bot Terputus\",\n                    description: \"WhatsApp bot sedang tidak terhubung\",\n                    color: \"text-red-500\",\n                    bgColor: \"bg-red-50\",\n                    borderColor: \"border-red-200\"\n                };\n            case \"qr\":\n                return {\n                    icon: _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    text: \"Scan QR Code\",\n                    description: \"Scan QR code dengan WhatsApp di ponsel Anda\",\n                    color: \"text-blue-500\",\n                    bgColor: \"bg-blue-50\",\n                    borderColor: \"border-blue-200\"\n                };\n            case \"connecting\":\n                return {\n                    icon: _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    text: \"Menghubungkan...\",\n                    description: \"Sedang menghubungkan ke WhatsApp\",\n                    color: \"text-yellow-500\",\n                    bgColor: \"bg-yellow-50\",\n                    borderColor: \"border-yellow-200\"\n                };\n            case \"ready\":\n                return {\n                    icon: _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    text: \"Bot Siap! \\uD83C\\uDF89\",\n                    description: \"WhatsApp bot sudah terhubung dan siap digunakan\",\n                    color: \"text-green-500\",\n                    bgColor: \"bg-green-50\",\n                    borderColor: \"border-green-200\"\n                };\n            default:\n                return {\n                    icon: _barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: \"Status Tidak Diketahui\",\n                    description: \"Status bot tidak dapat ditentukan\",\n                    color: \"text-gray-500\",\n                    bgColor: \"bg-gray-50\",\n                    borderColor: \"border-gray-200\"\n                };\n        }\n    };\n    const statusInfo = getStatusInfo();\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.6\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                delay: 0.2,\n                                type: \"spring\",\n                                stiffness: 200\n                            },\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-whatsapp-500 rounded-2xl mb-4 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-800 mb-2\",\n                            children: \"WhatsApp Bot AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Interface modern untuk mengelola bot WhatsApp Anda\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    layout: true,\n                    className: `card ${statusInfo.bgColor} ${statusInfo.borderColor} border-2 mb-6`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-xl ${statusInfo.bgColor}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                        className: `w-6 h-6 ${statusInfo.color} ${botStatus.status === \"connecting\" ? \"animate-spin\" : \"\"}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: `font-semibold ${statusInfo.color}`,\n                                            children: statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: statusInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Status Koneksi:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `status-indicator ${isConnecting ? \"status-connecting\" : \"status-online\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: isConnecting ? \"text-yellow-600\" : \"text-green-600\",\n                                            children: isConnecting ? \"Menghubungkan...\" : \"Terhubung\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: botStatus.status === \"qr\" && botStatus.qrCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"card text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                        children: \"Scan QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Buka WhatsApp di ponsel → Menu (⋮) → WhatsApp Web → Scan QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    animate: {\n                                        boxShadow: [\n                                            \"0 0 0 0 rgba(37, 211, 102, 0.4)\",\n                                            \"0 0 0 20px rgba(37, 211, 102, 0)\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    },\n                                    className: \"rounded-2xl overflow-hidden bg-white p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: botStatus.qrCode,\n                                        alt: \"QR Code\",\n                                        className: \"w-64 h-64 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-xs text-gray-500\",\n                                children: \"QR Code akan diperbarui secara otomatis\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, \"qr-code\", true, {\n                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                botStatus.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"card bg-green-50 border-2 border-green-200 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-green-700 mb-2\",\n                                    children: \"Bot Siap Digunakan! \\uD83C\\uDF89\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600 mb-4\",\n                                    children: \"WhatsApp bot Anda sudah terhubung dan siap melayani pesan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        botStatus.activeUsers !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 text-sm text-green-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        botStatus.activeUsers,\n                                        \" pengguna aktif\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: ()=>socket?.emit(\"request-qr\"),\n                            className: \"btn-secondary flex-1 inline-flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Refresh Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        botStatus.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                            href: \"/dashboard\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"btn-primary flex-1 inline-flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ExclamationTriangleIcon_PhoneIcon_QrCodeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Lihat Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-sm text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 WhatsApp Bot AI. Dibuat dengan ❤️\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\components\\\\QRScanner.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/QRScanner.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bc38c79f92a5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aGF0c2FwcC1ib3QtZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/Yjk3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJjMzhjNzlmOTJhNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"WhatsApp Bot AI - QR Scanner\",\n    description: \"Professional WhatsApp Bot AI dengan interface modern untuk scan QR code\",\n    keywords: [\n        \"whatsapp\",\n        \"bot\",\n        \"ai\",\n        \"qr\",\n        \"scanner\"\n    ],\n    authors: [\n        {\n            name: \"WhatsApp Bot AI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#25D366\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_QRScanner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/QRScanner */ \"(rsc)/./components/QRScanner.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRScanner__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\Project\\\\wa\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0M7QUFFaEMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZEQUFTQTs7Ozs7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aGF0c2FwcC1ib3QtZnJvbnRlbmQvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUVJTY2FubmVyIGZyb20gJ0AvY29tcG9uZW50cy9RUlNjYW5uZXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gPFFSU2Nhbm5lciAvPjtcbn1cbiJdLCJuYW1lcyI6WyJRUlNjYW5uZXIiLCJIb21lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/QRScanner.tsx":
/*!**********************************!*\
  !*** ./components/QRScanner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\wa\frontend\components\QRScanner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/engine.io-client","vendor-chunks/ws","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProject%5Cwa%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cwa%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();