{"version": 3, "sources": ["../../src/server/send-payload.ts"], "names": ["sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "type", "generateEtags", "poweredByHeader", "revalidate", "isResSent", "formatRevalidate", "payload", "isDynamic", "toUnchunkedString", "generateETag", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "contentType", "RSC_CONTENT_TYPE_HEADER", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,gBAAgB;eAAhBA;;IAwBMC,gBAAgB;eAAhBA;;;uBA9BI;sBACG;8DACX;4BACe;kCACO;;;;;;AAEjC,SAASD,iBACdE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIE,IAAAA,cAAK,EAACJ,IAAIK,OAAO,EAAE;QAAEH;IAAK,IAAI;QAChCD,IAAIK,UAAU,GAAG;QACjBL,IAAIM,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeR,iBAAiB,EACrCC,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,UAAU,EASX;IACC,IAAIC,IAAAA,gBAAS,EAACZ,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,IAAI,OAAOS,eAAe,aAAa;QACrCX,IAAIE,SAAS,CAAC,iBAAiBW,IAAAA,4BAAgB,EAACF;IAClD;IAEA,MAAMG,UAAUP,OAAOQ,SAAS,GAAG,OAAOR,OAAOS,iBAAiB;IAElE,IAAIF,YAAY,MAAM;QACpB,MAAMb,OAAOQ,gBAAgBQ,IAAAA,kBAAY,EAACH,WAAWI;QACrD,IAAIrB,iBAAiBE,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAImB,SAAS,CAAC,iBAAiB;QAClCnB,IAAIE,SAAS,CACX,gBACAK,OAAOa,WAAW,GACdb,OAAOa,WAAW,GAClBZ,SAAS,QACTa,yCAAuB,GACvBb,SAAS,SACT,qBACA;IAER;IAEA,IAAIM,SAAS;QACXd,IAAIE,SAAS,CAAC,kBAAkBoB,OAAOC,UAAU,CAACT;IACpD;IAEA,IAAIf,IAAIyB,MAAM,KAAK,QAAQ;QACzBxB,IAAIM,GAAG,CAAC;QACR;IACF;IAEA,IAAIQ,YAAY,MAAM;QACpBd,IAAIM,GAAG,CAACQ;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMP,OAAOkB,kBAAkB,CAACzB;AAClC"}