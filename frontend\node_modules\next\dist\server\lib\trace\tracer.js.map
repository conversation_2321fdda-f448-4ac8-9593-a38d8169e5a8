{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["getTracer", "SpanStatusCode", "SpanKind", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "carrier", "fn", "getter", "activeContext", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": ";;;;;;;;;;;;;;;;IAqYSA,SAAS;eAATA;;IAAWC,cAAc;eAAdA;;IAAgBC,QAAQ;eAARA;;;2BApYK;AAWzC,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEV,cAAc,EAAEC,QAAQ,EAAEU,YAAY,EAAE,GAC3ET;AAEF,MAAMU,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMtB,eAAeuB,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOvB,MAAMX,SAAS,CAAC,WAAW;IACpC;IAEOmC,aAAyB;QAC9B,OAAO1B;IACT;IAEO2B,qBAAuC;QAC5C,OAAOzB,MAAM0B,OAAO,CAAC5B,2BAAAA,QAAS6B,MAAM;IACtC;IAEOC,sBACLC,OAAU,EACVC,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMC,gBAAgBlC,QAAQ6B,MAAM;QACpC,IAAI3B,MAAMiC,cAAc,CAACD,gBAAgB;YACvC,qDAAqD;YACrD,OAAOF;QACT;QACA,MAAMI,gBAAgBnC,YAAYoC,OAAO,CAACH,eAAeH,SAASE;QAClE,OAAOjC,QAAQsC,IAAI,CAACF,eAAeJ;IACrC;IAsBO9B,MAAS,GAAGqC,IAAgB,EAAE;YAwCxBrC;QAvCX,MAAM,CAACsC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJP,EAAE,EACFW,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACET,IAAIS;YACJE,SAAS,CAAC;QACZ,IACA;YACEX,IAAIU;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACG,mCAAwB,CAACC,QAAQ,CAACL,SAClC7C,QAAQC,GAAG,CAACkD,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOf;QACT;QAEA,MAAMgB,WAAWL,QAAQK,QAAQ,IAAIR;QAErC,mHAAmH;QACnH,IAAIS,cAAc,IAAI,CAACd,cAAc,CACnCQ,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACvB,kBAAkB;QAEhD,IAAIwB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAc9C;YACdgD,aAAa;QACf,OAAO,KAAIjD,wBAAAA,MAAMiC,cAAc,CAACc,iCAArB/C,sBAAmCkD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS9B;QAEfoB,QAAQW,UAAU,GAAG;YACnB,kBAAkBN;YAClB,kBAAkBR;YAClB,GAAGG,QAAQW,UAAU;QACvB;QAEA,OAAOtD,QAAQsC,IAAI,CAACW,YAAYM,QAAQ,CAACnC,eAAeiC,SAAS,IAC/D,IAAI,CAAC5B,iBAAiB,GAAG+B,eAAe,CACtCR,UACAL,SACA,CAACnC;gBACC,MAAMiD,YAAY;oBAChBvC,wBAAwBwC,MAAM,CAACL;gBACjC;gBACA,IAAIF,YAAY;oBACdjC,wBAAwByC,GAAG,CACzBN,QACA,IAAIlC,IACFyC,OAAOC,OAAO,CAAClB,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAItB,GAAG8B,MAAM,GAAG,GAAG;wBACjB,OAAO9B,GAAGxB,MAAM,CAACT,MAAgBQ,mBAAmBC,MAAMT;oBAC5D;oBAEA,MAAMgE,SAAS/B,GAAGxB;oBAElB,IAAIJ,UAAU2D,SAAS;wBACrBA,OACGzD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAAClB,MAAQQ,mBAAmBC,MAAMT,MAEnCiE,OAAO,CAACP;oBACb,OAAO;wBACLjD,KAAKS,GAAG;wBACRwC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAOhE,KAAU;oBACjBQ,mBAAmBC,MAAMT;oBACzB0D;oBACA,MAAM1D;gBACR;YACF;IAGN;IAaOkE,KAAK,GAAG1B,IAAgB,EAAE;QAC/B,MAAM2B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMxB,SAASX,GAAG,GACvBO,KAAKuB,MAAM,KAAK,IAAIvB,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACK,mCAAwB,CAACC,QAAQ,CAACsB,SACnCxE,QAAQC,GAAG,CAACkD,iBAAiB,KAAK,KAClC;YACA,OAAOd;QACT;QAEA,OAAO;YACL,IAAIoC,aAAazB;YACjB,IAAI,OAAOyB,eAAe,cAAc,OAAOpC,OAAO,YAAY;gBAChEoC,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOxC,UAAU,GAAGgD,IAAI,CAAC1E,QAAQ6B,MAAM,IAAI2C;gBAChE,OAAON,OAAOhE,KAAK,CAACiE,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUxE,GAAQ;wBACvC6E,wBAAAA,KAAO7E;wBACP,OAAO0E,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOtC,GAAGqC,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOhE,KAAK,CAACiE,MAAMC,YAAY,IAAMpC,GAAGqC,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGtC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMU,cAAc,IAAI,CAACd,cAAc,CACrCQ,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACvB,kBAAkB;QAEhD,OAAO,IAAI,CAACF,iBAAiB,GAAGoD,SAAS,CAACrC,MAAMG,SAASM;IAC3D;IAEQd,eAAee,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBhD,MAAM4E,OAAO,CAAC9E,QAAQ6B,MAAM,IAAIqB,cAChC6B;QAEJ,OAAO9B;IACT;IAEO+B,wBAAwB;QAC7B,MAAM3B,SAASrD,QAAQ6B,MAAM,GAAGoD,QAAQ,CAAC7D;QACzC,OAAOF,wBAAwBgE,GAAG,CAAC7B;IACrC;AACF;AAEA,MAAM9D,YAAY,AAAC,CAAA;IACjB,MAAM2E,SAAS,IAAI1C;IAEnB,OAAO,IAAM0C;AACf,CAAA"}