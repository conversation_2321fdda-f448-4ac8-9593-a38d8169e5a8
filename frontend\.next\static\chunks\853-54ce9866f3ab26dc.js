"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[853],{622:function(t,e,i){var s=i(2265),r=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),o=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function l(t,e,i){var s,l={},h=null,u=null;for(s in void 0!==i&&(h=""+i),void 0!==e.key&&(h=""+e.key),void 0!==e.ref&&(u=e.ref),e)n.call(e,s)&&!a.hasOwnProperty(s)&&(l[s]=e[s]);if(t&&t.defaultProps)for(s in e=t.defaultProps)void 0===l[s]&&(l[s]=e[s]);return{$$typeof:r,type:t,key:h,ref:u,props:l,_owner:o.current}}e.jsx=l,e.jsxs=l},7437:function(t,e,i){t.exports=i(622)},1706:function(t,e,i){var s=i(2265);let r=s.forwardRef(function({title:t,titleId:e,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":e},i),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))});e.Z=r},9186:function(t,e,i){var s=i(2265);let r=s.forwardRef(function({title:t,titleId:e,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":e},i),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});e.Z=r},887:function(t,e,i){var s=i(2265);let r=s.forwardRef(function({title:t,titleId:e,...i},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":e},i),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))});e.Z=r},781:function(t,e,i){i.d(e,{p:function(){return s}});let s=(0,i(2265).createContext)({})},8243:function(t,e,i){i.d(e,{O:function(){return s}});let s=(0,i(2265).createContext)(null)},2363:function(t,e,i){i.d(e,{Pn:function(){return a},Wi:function(){return o},frameData:function(){return l},S6:function(){return h}});var s=i(6977);class r{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let n=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:h}=function(t,e){let i=!1,s=!0,o={delta:0,timestamp:0,isProcessing:!1},a=n.reduce((t,e)=>(t[e]=function(t){let e=new r,i=new r,s=0,n=!1,o=!1,a=new WeakSet,l={schedule:(t,r=!1,o=!1)=>{let l=o&&n,h=l?e:i;return r&&a.add(t),h.add(t)&&l&&n&&(s=e.order.length),t},cancel:t=>{i.remove(t),a.delete(t)},process:r=>{if(n){o=!0;return}if(n=!0,[e,i]=[i,e],i.clear(),s=e.order.length)for(let i=0;i<s;i++){let s=e.order[i];s(r),a.has(s)&&(l.schedule(s),t())}n=!1,o&&(o=!1,l.process(r))}};return l}(()=>i=!0),t),{}),l=t=>a[t].process(o),h=()=>{let r=performance.now();i=!1,o.delta=s?1e3/60:Math.max(Math.min(r-o.timestamp,40),1),o.timestamp=r,o.isProcessing=!0,n.forEach(l),o.isProcessing=!1,i&&e&&(s=!1,t(h))},u=()=>{i=!0,s=!0,o.isProcessing||t(h)};return{schedule:n.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,r=!1)=>(i||u(),s.schedule(t,e,r)),t},{}),cancel:t=>n.forEach(e=>a[e].cancel(t)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.Z,!0)},5251:function(t,e,i){let s;i.d(e,{E:function(){return rH}});var r,n,o=i(2265);let a=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),l=(0,o.createContext)({});var h=i(8243),u=i(538);let c=(0,o.createContext)({strict:!1}),d=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function f(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function m(t){return"string"==typeof t||Array.isArray(t)}function g(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],v=["initial",...y];function b(t){return g(t.animate)||v.some(e=>m(t[e]))}function x(t){return!!(b(t)||t.variants)}function w(t){return Array.isArray(t)?t.join(" "):t}let P={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},T={};for(let t in P)T[t]={isEnabled:e=>P[t].some(t=>!!e[t])};var A=i(6613),k=i(781);let E=(0,o.createContext)({}),S=Symbol.for("motionComponentSymbol"),_=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function C(t){if("string"!=typeof t||t.includes("-"));else if(_.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let R={},V=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],L=new Set(V);function D(t,{layout:e,layoutId:i}){return L.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!R[t]||"opacity"===t)}let O=t=>!!(t&&t.getVelocity),M={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=V.length,j=t=>e=>"string"==typeof e&&e.startsWith(t),F=j("--"),N=j("var(--"),U=(t,e)=>e&&"number"==typeof t?e.transform(t):t,I=(t,e,i)=>Math.min(Math.max(i,t),e),W={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={...W,transform:t=>I(0,1,t)},$={...W,default:1},H=t=>Math.round(1e5*t)/1e5,z=/(-)?([\d]*\.?[\d])+/g,Y=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Z=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function X(t){return"string"==typeof t}let K=t=>({test:e=>X(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),G=K("deg"),J=K("%"),Q=K("px"),tt=K("vh"),te=K("vw"),ti={...J,parse:t=>J.parse(t)/100,transform:t=>J.transform(100*t)},ts={...W,transform:Math.round},tr={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:G,rotateX:G,rotateY:G,rotateZ:G,scale:$,scaleX:$,scaleY:$,scaleZ:$,skew:G,skewX:G,skewY:G,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:q,originX:ti,originY:ti,originZ:Q,zIndex:ts,fillOpacity:q,strokeOpacity:q,numOctaves:ts};function tn(t,e,i,s){let{style:r,vars:n,transform:o,transformOrigin:a}=t,l=!1,h=!1,u=!0;for(let t in e){let i=e[t];if(F(t)){n[t]=i;continue}let s=tr[t],c=U(i,s);if(L.has(t)){if(l=!0,o[t]=c,!u)continue;i!==(s.default||0)&&(u=!1)}else t.startsWith("origin")?(h=!0,a[t]=c):r[t]=c}if(!e.transform&&(l||s?r.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:i=!0},s,r){let n="";for(let e=0;e<B;e++){let i=V[e];if(void 0!==t[i]){let e=M[i]||i;n+=`${e}(${t[i]}) `}}return e&&!t.z&&(n+="translateZ(0)"),n=n.trim(),r?n=r(t,s?"":n):i&&s&&(n="none"),n}(t.transform,i,u,s):r.transform&&(r.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin=`${t} ${e} ${i}`}}let to=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ta(t,e,i){for(let s in e)O(e[s])||D(s,i)||(t[s]=e[s])}let tl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function th(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tl.has(t)}let tu=t=>!th(t);try{(r=require("@emotion/is-prop-valid").default)&&(tu=t=>t.startsWith("on")?!th(t):r(t))}catch(t){}function tc(t,e,i){return"string"==typeof t?t:Q.transform(e+i*t)}let td={offset:"stroke-dashoffset",array:"stroke-dasharray"},tp={offset:"strokeDashoffset",array:"strokeDasharray"};function tf(t,{attrX:e,attrY:i,attrScale:s,originX:r,originY:n,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...h},u,c,d){if(tn(t,h,u,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:f,dimensions:m}=t;p.transform&&(m&&(f.transform=p.transform),delete p.transform),m&&(void 0!==r||void 0!==n||f.transform)&&(f.transformOrigin=function(t,e,i){let s=tc(e,t.x,t.width),r=tc(i,t.y,t.height);return`${s} ${r}`}(m,void 0!==r?r:.5,void 0!==n?n:.5)),void 0!==e&&(p.x=e),void 0!==i&&(p.y=i),void 0!==s&&(p.scale=s),void 0!==o&&function(t,e,i=1,s=0,r=!0){t.pathLength=1;let n=r?td:tp;t[n.offset]=Q.transform(-s);let o=Q.transform(e),a=Q.transform(i);t[n.array]=`${o} ${a}`}(p,o,a,l,!1)}let tm=()=>({...to(),attrs:{}}),tg=t=>"string"==typeof t&&"svg"===t.toLowerCase();function ty(t,{style:e,vars:i},s,r){for(let n in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(n,i[n])}let tv=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tb(t,e,i,s){for(let i in ty(t,e,void 0,s),e.attrs)t.setAttribute(tv.has(i)?i:d(i),e.attrs[i])}function tx(t,e){let{style:i}=t,s={};for(let r in i)(O(i[r])||e.style&&O(e.style[r])||D(r,t))&&(s[r]=i[r]);return s}function tw(t,e){let i=tx(t,e);for(let s in t)(O(t[s])||O(e[s]))&&(i[-1!==V.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]);return i}function tP(t,e,i,s={},r={}){return"function"==typeof e&&(e=e(void 0!==i?i:t.custom,s,r)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==i?i:t.custom,s,r)),e}var tT=i(961);let tA=t=>Array.isArray(t),tk=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tE=t=>tA(t)?t[t.length-1]||0:t;function tS(t){let e=O(t)?t.get():t;return tk(e)?e.toValue():e}let t_=t=>(e,i)=>{let s=(0,o.useContext)(l),r=(0,o.useContext)(h.O),n=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:i},s,r,n){let o={latestValues:function(t,e,i,s){let r={},n=s(t,{});for(let t in n)r[t]=tS(n[t]);let{initial:o,animate:a}=t,l=b(t),h=x(t);e&&h&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let u=!!i&&!1===i.initial,c=(u=u||!1===o)?a:o;return c&&"boolean"!=typeof c&&!g(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let i=tP(t,e);if(!i)return;let{transitionEnd:s,transition:n,...o}=i;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let t in s)r[t]=s[t]}),r}(s,r,n,t),renderState:e()};return i&&(o.mount=t=>i(s,t,o)),o})(t,e,s,r);return i?n():(0,tT.h)(n)};var tC=i(2363);let tR={useVisualState:t_({scrapeMotionValuesFromProps:tw,createRenderState:tm,onMount:(t,e,{renderState:i,latestValues:s})=>{tC.Wi.read(()=>{try{i.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){i.dimensions={x:0,y:0,width:0,height:0}}}),tC.Wi.render(()=>{tf(i,s,{enableHardwareAcceleration:!1},tg(e.tagName),t.transformTemplate),tb(e,i)})}})},tV={useVisualState:t_({scrapeMotionValuesFromProps:tx,createRenderState:to})};function tL(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let tD=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tO(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tM=t=>e=>tD(e)&&t(e,tO(e));function tB(t,e,i,s){return tL(t,e,tM(i),s)}let tj=(t,e)=>i=>e(t(i)),tF=(...t)=>t.reduce(tj);function tN(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tU=tN("dragHorizontal"),tI=tN("dragVertical");function tW(t){let e=!1;if("y"===t)e=tI();else if("x"===t)e=tU();else{let t=tU(),i=tI();t&&i?e=()=>{t(),i()}:(t&&t(),i&&i())}return e}function tq(){let t=tW(!0);return!t||(t(),!1)}class t${constructor(t){this.isMounted=!1,this.node=t}update(){}}function tH(t,e){let i="onHover"+(e?"Start":"End");return tB(t.current,"pointer"+(e?"enter":"leave"),(s,r)=>{if("touch"===s.pointerType||tq())return;let n=t.getProps();t.animationState&&n.whileHover&&t.animationState.setActive("whileHover",e),n[i]&&tC.Wi.update(()=>n[i](s,r))},{passive:!t.getProps()[i]})}class tz extends t${mount(){this.unmount=tF(tH(this.node,!0),tH(this.node,!1))}unmount(){}}class tY extends t${constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tF(tL(this.node.current,"focus",()=>this.onFocus()),tL(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tZ=(t,e)=>!!e&&(t===e||tZ(t,e.parentElement));var tX=i(6977);function tK(t,e){if(!e)return;let i=new PointerEvent("pointer"+t);e(i,tO(i))}class tG extends t${constructor(){super(...arguments),this.removeStartListeners=tX.Z,this.removeEndListeners=tX.Z,this.removeAccessibleListeners=tX.Z,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),s=tB(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:s,globalTapTarget:r}=this.node.getProps();tC.Wi.update(()=>{r||tZ(this.node.current,t.target)?i&&i(t,e):s&&s(t,e)})},{passive:!(i.onTap||i.onPointerUp)}),r=tB(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=tF(s,r),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tL(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tL(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&tK("up",(t,e)=>{let{onTap:i}=this.node.getProps();i&&tC.Wi.update(()=>i(t,e))})}),tK("down",(t,e)=>{this.startPress(t,e)}))}),e=tL(this.node.current,"blur",()=>{this.isPressing&&tK("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tF(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:i,whileTap:s}=this.node.getProps();s&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&tC.Wi.update(()=>i(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tq()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&tC.Wi.update(()=>i(t,e))}mount(){let t=this.node.getProps(),e=tB(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=tL(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tF(e,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let tJ=new WeakMap,tQ=new WeakMap,t0=t=>{let e=tJ.get(t.target);e&&e(t)},t1=t=>{t.forEach(t0)},t2={some:0,all:1};class t3 extends t${constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:t2[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;tQ.has(i)||tQ.set(i,{});let s=tQ.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(t1,{root:t,...e})),s[r]}(e);return tJ.set(t,i),s.observe(t),()=>{tJ.delete(t),s.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=e?i:s;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}function t5(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function t6(t,e,i){let s=t.getProps();return tP(s,e,void 0!==i?i:s.custom,function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,i)=>e[i]=t.getVelocity()),e}(t))}var t4=i(6567);let t8=t=>1e3*t,t9=t=>t/1e3,t7={current:!1},et=t=>Array.isArray(t)&&"number"==typeof t[0],ee=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,ei={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ee([0,.65,.55,1]),circOut:ee([.55,0,1,.45]),backIn:ee([.31,.01,.66,-.59]),backOut:ee([.33,1.53,.69,.99])},es=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function er(t,e,i,s){if(t===e&&i===s)return tX.Z;let r=e=>(function(t,e,i,s,r){let n,o;let a=0;do(n=es(o=e+(i-e)/2,s,r)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:es(r(t),e,s)}let en=er(.42,0,1,1),eo=er(0,0,.58,1),ea=er(.42,0,.58,1),el=t=>Array.isArray(t)&&"number"!=typeof t[0],eh=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,eu=t=>e=>1-t(1-e),ec=t=>1-Math.sin(Math.acos(t)),ed=eu(ec),ep=eh(ec),ef=er(.33,1.53,.69,.99),em=eu(ef),eg=eh(em),ey={linear:tX.Z,easeIn:en,easeInOut:ea,easeOut:eo,circIn:ec,circInOut:ep,circOut:ed,backIn:em,backInOut:eg,backOut:ef,anticipate:t=>(t*=2)<1?.5*em(t):.5*(2-Math.pow(2,-10*(t-1)))},ev=t=>{if(Array.isArray(t)){(0,t4.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,r]=t;return er(e,i,s,r)}return"string"==typeof t?((0,t4.k)(void 0!==ey[t],`Invalid easing type '${t}'`),ey[t]):t},eb=(t,e)=>i=>!!(X(i)&&Z.test(i)&&i.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(i,e)),ex=(t,e,i)=>s=>{if(!X(s))return s;let[r,n,o,a]=s.match(z);return{[t]:parseFloat(r),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ew=t=>I(0,255,t),eP={...W,transform:t=>Math.round(ew(t))},eT={test:eb("rgb","red"),parse:ex("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+eP.transform(t)+", "+eP.transform(e)+", "+eP.transform(i)+", "+H(q.transform(s))+")"},eA={test:eb("#"),parse:function(t){let e="",i="",s="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:eT.transform},ek={test:eb("hsl","hue"),parse:ex("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+J.transform(H(e))+", "+J.transform(H(i))+", "+H(q.transform(s))+")"},eE={test:t=>eT.test(t)||eA.test(t)||ek.test(t),parse:t=>eT.test(t)?eT.parse(t):ek.test(t)?ek.parse(t):eA.parse(t),transform:t=>X(t)?t:t.hasOwnProperty("red")?eT.transform(t):ek.transform(t)},eS=(t,e,i)=>-i*t+i*e+t;function e_(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}let eC=(t,e,i)=>{let s=t*t;return Math.sqrt(Math.max(0,i*(e*e-s)+s))},eR=[eA,eT,ek],eV=t=>eR.find(e=>e.test(t));function eL(t){let e=eV(t);(0,t4.k)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let i=e.parse(t);return e===ek&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let r=0,n=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;r=e_(a,s,t+1/3),n=e_(a,s,t),o=e_(a,s,t-1/3)}else r=n=o=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*o),alpha:s}}(i)),i}let eD=(t,e)=>{let i=eL(t),s=eL(e),r={...i};return t=>(r.red=eC(i.red,s.red,t),r.green=eC(i.green,s.green,t),r.blue=eC(i.blue,s.blue,t),r.alpha=eS(i.alpha,s.alpha,t),eT.transform(r))},eO={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tX.Z},eM={regex:Y,countKey:"Colors",token:"${c}",parse:eE.parse},eB={regex:z,countKey:"Numbers",token:"${n}",parse:W.parse};function ej(t,{regex:e,countKey:i,token:s,parse:r}){let n=t.tokenised.match(e);n&&(t["num"+i]=n.length,t.tokenised=t.tokenised.replace(e,s),t.values.push(...n.map(r)))}function eF(t){let e=t.toString(),i={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return i.value.includes("var(--")&&ej(i,eO),ej(i,eM),ej(i,eB),i}function eN(t){return eF(t).values}function eU(t){let{values:e,numColors:i,numVars:s,tokenised:r}=eF(t),n=e.length;return t=>{let e=r;for(let r=0;r<n;r++)e=r<s?e.replace(eO.token,t[r]):r<s+i?e.replace(eM.token,eE.transform(t[r])):e.replace(eB.token,H(t[r]));return e}}let eI=t=>"number"==typeof t?0:t,eW={test:function(t){var e,i;return isNaN(t)&&X(t)&&((null===(e=t.match(z))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(Y))||void 0===i?void 0:i.length)||0)>0},parse:eN,createTransformer:eU,getAnimatableNone:function(t){let e=eN(t);return eU(t)(e.map(eI))}},eq=(t,e)=>i=>`${i>0?e:t}`;function e$(t,e){return"number"==typeof t?i=>eS(t,e,i):eE.test(t)?eD(t,e):t.startsWith("var(")?eq(t,e):eY(t,e)}let eH=(t,e)=>{let i=[...t],s=i.length,r=t.map((t,i)=>e$(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=r[e](t);return i}},ez=(t,e)=>{let i={...t,...e},s={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(s[r]=e$(t[r],e[r]));return t=>{for(let e in s)i[e]=s[e](t);return i}},eY=(t,e)=>{let i=eW.createTransformer(e),s=eF(t),r=eF(e);return s.numVars===r.numVars&&s.numColors===r.numColors&&s.numNumbers>=r.numNumbers?tF(eH(s.values,r.values),i):((0,t4.K)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eq(t,e))},eZ=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},eX=(t,e)=>i=>eS(t,e,i);function eK(t,e,{clamp:i=!0,ease:s,mixer:r}={}){let n=t.length;if((0,t4.k)(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],r=i||function(t){if("number"==typeof t);else if("string"==typeof t)return eE.test(t)?eD:eY;else if(Array.isArray(t))return eH;else if("object"==typeof t)return ez;return eX}(t[0]),n=t.length-1;for(let i=0;i<n;i++){let n=r(t[i],t[i+1]);e&&(n=tF(Array.isArray(e)?e[i]||tX.Z:e,n)),s.push(n)}return s}(e,s,r),a=o.length,l=e=>{let i=0;if(a>1)for(;i<t.length-2&&!(e<t[i+1]);i++);let s=eZ(t[i],t[i+1],e);return o[i](s)};return i?e=>l(I(t[0],t[n-1],e)):l}function eG({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){let r=el(s)?s.map(ev):ev(s),n={done:!1,value:e[0]},o=eK((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let r=eZ(0,e,s);t.push(eS(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||ea).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(n.value=o(e),n.done=e>=t,n)}}function eJ(t,e,i){var s,r;let n=Math.max(e-5,0);return s=i-t(n),(r=e-n)?1e3/r*s:0}function eQ(t,e){return t*Math.sqrt(1-e*e)}let e0=["duration","bounce"],e1=["stiffness","damping","mass"];function e2(t,e){return e.some(e=>void 0!==t[e])}function e3({keyframes:t,restDelta:e,restSpeed:i,...s}){let r;let n=t[0],o=t[t.length-1],a={done:!1,value:n},{stiffness:l,damping:h,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e2(t,e1)&&e2(t,e0)){let i=function({duration:t=800,bounce:e=.25,velocity:i=0,mass:s=1}){let r,n;(0,t4.K)(t<=t8(10),"Spring duration must be 10 seconds or less");let o=1-e;o=I(.05,1,o),t=I(.01,10,t9(t)),o<1?(r=e=>{let s=e*o,r=s*t;return .001-(s-i)/eQ(e,o)*Math.exp(-r)},n=e=>{let s=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=eQ(Math.pow(e,2),o);return(s*i+i-n)*Math.exp(-s)*(-r(e)+.001>0?-1:1)/a}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(r,n,5/t);if(t=t8(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:1}).isResolvedFromDuration=!0}return e}({...s,velocity:-t9(s.velocity||0)}),f=d||0,m=h/(2*Math.sqrt(l*u)),g=o-n,y=t9(Math.sqrt(l/u)),v=5>Math.abs(g);if(i||(i=v?.01:2),e||(e=v?.005:.5),m<1){let t=eQ(y,m);r=e=>o-Math.exp(-m*y*e)*((f+m*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===m)r=t=>o-Math.exp(-y*t)*(g+(f+y*g)*t);else{let t=y*Math.sqrt(m*m-1);r=e=>{let i=Math.exp(-m*y*e),s=Math.min(t*e,300);return o-i*((f+m*y*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}return{calculatedDuration:p&&c||null,next:t=>{let s=r(t);if(p)a.done=t>=c;else{let n=f;0!==t&&(n=m<1?eJ(r,t,s):0);let l=Math.abs(n)<=i,h=Math.abs(o-s)<=e;a.done=l&&h}return a.value=a.done?o:s,a}}}function e5({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let c,d;let p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let x=t=>-y*Math.exp(-t/s),w=t=>b+x(t),P=t=>{let e=x(t),i=w(t);f.done=Math.abs(e)<=h,f.value=f.done?b:i},T=t=>{m(f.value)&&(c=t,d=e3({keyframes:[f.value,g(f.value)],velocity:eJ(w,t,f.value),damping:r,stiffness:n,restDelta:h,restSpeed:u}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>c)?d.next(t-c):(e||P(t),f)}}}let e6=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tC.Wi.update(e,!0),stop:()=>(0,tC.Pn)(e),now:()=>tC.frameData.isProcessing?tC.frameData.timestamp:performance.now()}};function e4(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let e8={decay:e5,inertia:e5,tween:eG,keyframes:eG,spring:e3};function e9({autoplay:t=!0,delay:e=0,driver:i=e6,keyframes:s,type:r="keyframes",repeat:n=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:h,onComplete:u,onUpdate:c,...d}){let p,f,m,g,y,v=1,b=!1,x=()=>{f=new Promise(t=>{p=t})};x();let w=e8[r]||eG;w!==eG&&"number"!=typeof s[0]&&(g=eK([0,100],s,{clamp:!1}),s=[0,100]);let P=w({...d,keyframes:s});"mirror"===a&&(y=w({...d,keyframes:[...s].reverse(),velocity:-(d.velocity||0)}));let T="idle",A=null,k=null,E=null;null===P.calculatedDuration&&n&&(P.calculatedDuration=e4(P));let{calculatedDuration:S}=P,_=1/0,C=1/0;null!==S&&(C=(_=S+o)*(n+1)-o);let R=0,V=t=>{if(null===k)return;v>0&&(k=Math.min(k,t)),v<0&&(k=Math.min(t-C/v,k));let i=(R=null!==A?A:Math.round(t-k)*v)-e*(v>=0?1:-1),r=v>=0?i<0:i>C;R=Math.max(i,0),"finished"===T&&null===A&&(R=C);let l=R,h=P;if(n){let t=Math.min(R,C)/_,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,n+1))%2&&("reverse"===a?(i=1-i,o&&(i-=o/_)):"mirror"===a&&(h=y)),l=I(0,1,i)*_}let u=r?{done:!1,value:s[0]}:h.next(l);g&&(u.value=g(u.value));let{done:d}=u;r||null===S||(d=v>=0?R>=C:R<=0);let p=null===A&&("finished"===T||"running"===T&&d);return c&&c(u.value),p&&O(),u},L=()=>{m&&m.stop(),m=void 0},D=()=>{T="idle",L(),p(),x(),k=E=null},O=()=>{T="finished",u&&u(),L(),p()},M=()=>{if(b)return;m||(m=i(V));let t=m.now();l&&l(),null!==A?k=t-A:k&&"finished"!==T||(k=t),"finished"===T&&x(),E=k,A=null,T="running",m.start()};t&&M();let B={then:(t,e)=>f.then(t,e),get time(){return t9(R)},set time(newTime){R=newTime=t8(newTime),null===A&&m&&0!==v?k=m.now()-newTime/v:A=newTime},get duration(){return t9(null===P.calculatedDuration?e4(P):P.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,B.time=t9(R)},get state(){return T},play:M,pause:()=>{T="paused",A=R},stop:()=>{b=!0,"idle"!==T&&(T="idle",h&&h(),D())},cancel:()=>{null!==E&&V(E),D()},complete:()=>{T="finished"},sample:t=>(k=0,V(t))};return B}let e7=(n=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===s&&(s=n()),s)),it=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ie=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&ei[e]||et(e)||Array.isArray(e)&&e.every(t))}(e.ease),ii={type:"spring",stiffness:500,damping:25,restSpeed:10},is=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ir={type:"keyframes",duration:.8},io={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ia=(t,{keyframes:e})=>e.length>2?ir:L.has(t)?t.startsWith("scale")?is(e[1]):ii:io,il=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eW.test(e)||"0"===e)&&!e.startsWith("url(")),ih=new Set(["brightness","contrast","saturate","opacity"]);function iu(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(z)||[];if(!s)return t;let r=i.replace(s,""),n=ih.has(e)?1:0;return s!==i&&(n*=100),e+"("+n+r+")"}let ic=/([a-z-]*)\(.*?\)/g,id={...eW,getAnimatableNone:t=>{let e=t.match(ic);return e?e.map(iu).join(" "):t}},ip={...tr,color:eE,backgroundColor:eE,outlineColor:eE,fill:eE,stroke:eE,borderColor:eE,borderTopColor:eE,borderRightColor:eE,borderBottomColor:eE,borderLeftColor:eE,filter:id,WebkitFilter:id},im=t=>ip[t];function ig(t,e){let i=im(t);return i!==id&&(i=eW),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let iy=t=>/^0[^.\s]+$/.test(t);function iv(t,e){return t[e]||t.default||t}let ib={skipAnimations:!1},ix=(t,e,i,s={})=>r=>{let n=iv(s,t)||{},o=n.delay||s.delay||0,{elapsed:a=0}=s;a-=t8(o);let l=function(t,e,i,s){let r,n;let o=il(e,i);r=Array.isArray(i)?[...i]:[null,i];let a=void 0!==s.from?s.from:t.get(),l=[];for(let t=0;t<r.length;t++){var h;null===r[t]&&(r[t]=0===t?a:r[t-1]),("number"==typeof(h=r[t])?0===h:null!==h?"none"===h||"0"===h||iy(h):void 0)&&l.push(t),"string"==typeof r[t]&&"none"!==r[t]&&"0"!==r[t]&&(n=r[t])}if(o&&l.length&&n)for(let t=0;t<l.length;t++)r[l[t]]=ig(e,n);return r}(e,t,i,n),h=l[0],u=l[l.length-1],c=il(t,h),d=il(t,u);(0,t4.K)(c===d,`You are trying to animate ${t} from "${h}" to "${u}". ${h} is not an animatable value - to enable this animation set ${h} to a value animatable to ${u} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...n,delay:-a,onUpdate:t=>{e.set(t),n.onUpdate&&n.onUpdate(t)},onComplete:()=>{r(),n.onComplete&&n.onComplete()}};if(!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(n)&&(p={...p,...ia(t,p)}),p.duration&&(p.duration=t8(p.duration)),p.repeatDelay&&(p.repeatDelay=t8(p.repeatDelay)),!c||!d||t7.current||!1===n.type||ib.skipAnimations)return function({keyframes:t,delay:e,onUpdate:i,onComplete:s}){let r=()=>(i&&i(t[t.length-1]),s&&s(),{time:0,speed:1,duration:0,play:tX.Z,pause:tX.Z,stop:tX.Z,then:t=>(t(),Promise.resolve()),cancel:tX.Z,complete:tX.Z});return e?e9({keyframes:[0,1],duration:0,delay:e,onComplete:r}):r()}(t7.current?{...p,delay:0}:p);if(!s.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let i=function(t,e,{onUpdate:i,onComplete:s,...r}){let n,o;if(!(e7()&&it.has(e)&&!r.repeatDelay&&"mirror"!==r.repeatType&&0!==r.damping&&"inertia"!==r.type))return!1;let a=!1,l=!1,h=()=>{o=new Promise(t=>{n=t})};h();let{keyframes:u,duration:c=300,ease:d,times:p}=r;if(ie(e,r)){let t=e9({...r,repeat:0,delay:0}),e={done:!1,value:u[0]},i=[],s=0;for(;!e.done&&s<2e4;)e=t.sample(s),i.push(e.value),s+=10;p=void 0,u=i,c=s-10,d="linear"}let f=function(t,e,i,{delay:s=0,duration:r,repeat:n=0,repeatType:o="loop",ease:a,times:l}={}){let h={[e]:i};l&&(h.offset=l);let u=function t(e){if(e)return et(e)?ee(e):Array.isArray(e)?e.map(t):ei[e]}(a);return Array.isArray(u)&&(h.easing=u),t.animate(h,{delay:s,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,u,{...r,duration:c,ease:d,times:p}),m=()=>{l=!1,f.cancel()},g=()=>{l=!0,tC.Wi.update(m),n(),h()};return f.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:i="loop"}){let s=e&&"loop"!==i&&e%2==1?0:t.length-1;return t[s]}(u,r)),s&&s(),g())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(f.timeline=t,f.onfinish=null,tX.Z),get time(){return t9(f.currentTime||0)},set time(newTime){f.currentTime=t8(newTime)},get speed(){return f.playbackRate},set speed(newSpeed){f.playbackRate=newSpeed},get duration(){return t9(c)},play:()=>{a||(f.play(),(0,tC.Pn)(m))},pause:()=>f.pause(),stop:()=>{if(a=!0,"idle"===f.playState)return;let{currentTime:e}=f;if(e){let i=e9({...r,autoplay:!1});t.setWithVelocity(i.sample(e-10).value,i.sample(e).value,10)}g()},complete:()=>{l||f.finish()},cancel:g}}(e,t,p);if(i)return i}return e9(p)};function iw(t){return!!(O(t)&&t.add)}let iP=t=>/^\-?\d*\.?\d+$/.test(t);function iT(t,e){-1===t.indexOf(e)&&t.push(e)}function iA(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class ik{constructor(){this.subscriptions=[]}add(t){return iT(this.subscriptions,t),()=>iA(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](t,e,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let iE=t=>!isNaN(parseFloat(t)),iS={current:void 0};class i_{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:i,timestamp:s}=tC.frameData;this.lastUpdated!==s&&(this.timeDelta=i,this.lastUpdated=s,tC.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tC.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=iE(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ik);let i=this.events[t].add(e);return"change"===t?()=>{i(),tC.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=t,this.timeDelta=i}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return iS.current&&iS.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function iC(t,e){return new i_(t,e)}let iR=t=>e=>e.test(t),iV=[W,Q,J,G,te,tt,{test:t=>"auto"===t,parse:t=>t}],iL=t=>iV.find(iR(t)),iD=[...iV,eE,eW],iO=t=>iD.find(iR(t));function iM(t,e,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");s&&(n=s);let h=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let s=t.getValue(e),r=a[e];if(!s||void 0===r||u&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(u,e))continue;let o={delay:i,elapsed:0,...iv(n||{},e)};if(window.HandoffAppearAnimations){let i=t.getProps()[p];if(i){let t=window.HandoffAppearAnimations(i,e,s,tC.Wi);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let c=!o.isHandoff&&!function(t,e){let i=t.get();if(!Array.isArray(e))return i!==e;for(let t=0;t<e.length;t++)if(e[t]!==i)return!0}(s,r);if("spring"===o.type&&(s.getVelocity()||o.velocity)&&(c=!1),s.animation&&(c=!1),c)continue;s.start(ix(e,s,r,t.shouldReduceMotion&&L.has(e)?{type:!1}:o));let d=s.animation;iw(l)&&(l.add(e),d.then(()=>l.remove(e))),h.push(d)}return o&&Promise.all(h).then(()=>{o&&function(t,e){let i=t6(t,e),{transitionEnd:s={},transition:r={},...n}=i?t.makeTargetAnimatable(i,!1):{};for(let e in n={...n,...s}){let i=tE(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,iC(i))}}(t,o)}),h}function iB(t,e,i={}){let s=t6(t,e,i.custom),{transition:r=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(iM(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,r=1,n){let o=[],a=(t.variantChildren.size-1)*s,l=1===r?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ij).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(iB(t,e,{...n,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([n(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[n,o]:[o,n];return t().then(()=>e())}}function ij(t,e){return t.sortNodePosition(e)}let iF=[...y].reverse(),iN=y.length;function iU(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class iI extends t${constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>iB(t,e,i)));else if("string"==typeof e)s=iB(t,e,i);else{let r="function"==typeof e?t6(t,e,i.custom):e;s=Promise.all(iM(t,r,i))}return s.then(()=>t.notify("AnimationComplete",e))})(t,e,i))),i={animate:iU(!0),whileInView:iU(),whileHover:iU(),whileTap:iU(),whileDrag:iU(),whileFocus:iU(),exit:iU()},s=!0,r=(e,i)=>{let s=t6(t,i);if(s){let{transition:t,transitionEnd:i,...r}=s;e={...e,...r,...i}}return e};function n(n,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},h=[],u=new Set,c={},d=1/0;for(let e=0;e<iN;e++){var p;let f=iF[e],y=i[f],v=void 0!==a[f]?a[f]:l[f],b=m(v),x=f===o?y.isActive:null;!1===x&&(d=e);let w=v===l[f]&&v!==a[f]&&b;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...c},!y.isActive&&null===x||!v&&!y.prevProp||g(v)||"boolean"==typeof v)continue;let P=(p=y.prevProp,("string"==typeof v?v!==p:!!Array.isArray(v)&&!t5(v,p))||f===o&&y.isActive&&!w&&b||e>d&&b),T=!1,A=Array.isArray(v)?v:[v],k=A.reduce(r,{});!1===x&&(k={});let{prevResolvedValues:E={}}=y,S={...E,...k},_=t=>{P=!0,u.has(t)&&(T=!0,u.delete(t)),y.needsAnimating[t]=!0};for(let t in S){let e=k[t],i=E[t];if(!c.hasOwnProperty(t))(tA(e)&&tA(i)?t5(e,i):e===i)?void 0!==e&&u.has(t)?_(t):y.protectedKeys[t]=!0:void 0!==e?_(t):u.add(t)}y.prevProp=v,y.prevResolvedValues=k,y.isActive&&(c={...c,...k}),s&&t.blockInitialAnimation&&(P=!1),P&&(!w||T)&&h.push(...A.map(t=>({animation:t,options:{type:f,...n}})))}if(u.size){let e={};u.forEach(i=>{let s=t.getBaseTarget(i);void 0!==s&&(e[i]=s)}),h.push({animation:e})}let f=!!h.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(f=!1),s=!1,f?e(h):Promise.resolve()}return{animateChanges:n,setActive:function(e,s,r){var o;if(i[e].isActive===s)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,s)}),i[e].isActive=s;let a=n(r,e);for(let t in i)i[t].protectedKeys={};return a},setAnimateFunction:function(i){e=i(t)},getState:()=>i}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),g(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let iW=0;class iq extends t${constructor(){super(...arguments),this.id=iW++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:i}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===s)return;let r=this.node.animationState.setActive("exit",!t,{custom:null!=i?i:this.node.getProps().custom});e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let i$=(t,e)=>Math.abs(t-e);class iH{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=iZ(this.lastMoveEventInfo,this.history),s=null!==this.startEvent,r=(t=i.offset,e={x:0,y:0},Math.sqrt(i$(t.x,e.x)**2+i$(t.y,e.y)**2)>=3);if(!s&&!r)return;let{point:n}=i,{timestamp:o}=tC.frameData;this.history.push({...n,timestamp:o});let{onStart:a,onMove:l}=this.handlers;s||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iz(e,this.transformPagePoint),tC.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iZ("pointercancel"===t.type?this.lastMoveEventInfo:iz(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),s&&s(t,n)},!tD(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let n=iz(tO(t),this.transformPagePoint),{point:o}=n,{timestamp:a}=tC.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iZ(n,this.history)),this.removeListeners=tF(tB(this.contextWindow,"pointermove",this.handlePointerMove),tB(this.contextWindow,"pointerup",this.handlePointerUp),tB(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,tC.Pn)(this.updatePoint)}}function iz(t,e){return e?{point:e(t.point)}:t}function iY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iZ({point:t},e){return{point:t,delta:iY(t,iX(e)),offset:iY(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,r=iX(t);for(;i>=0&&(s=t[i],!(r.timestamp-s.timestamp>t8(.1)));)i--;if(!s)return{x:0,y:0};let n=t9(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let o={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function iX(t){return t[t.length-1]}function iK(t){return t.max-t.min}function iG(t,e=0,i=.01){return Math.abs(t-e)<=i}function iJ(t,e,i,s=.5){t.origin=s,t.originPoint=eS(e.min,e.max,t.origin),t.scale=iK(i)/iK(e),(iG(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eS(i.min,i.max,t.origin)-t.originPoint,(iG(t.translate)||isNaN(t.translate))&&(t.translate=0)}function iQ(t,e,i,s){iJ(t.x,e.x,i.x,s?s.originX:void 0),iJ(t.y,e.y,i.y,s?s.originY:void 0)}function i0(t,e,i){t.min=i.min+e.min,t.max=t.min+iK(e)}function i1(t,e,i){t.min=e.min-i.min,t.max=t.min+iK(e)}function i2(t,e,i){i1(t.x,e.x,i.x),i1(t.y,e.y,i.y)}function i3(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i5(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function i6(t,e,i){return{min:i4(t,e),max:i4(t,i)}}function i4(t,e){return"number"==typeof t?t:t[e]||0}let i8=()=>({translate:0,scale:1,origin:0,originPoint:0}),i9=()=>({x:i8(),y:i8()}),i7=()=>({min:0,max:0}),st=()=>({x:i7(),y:i7()});function se(t){return[t("x"),t("y")]}function si({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function ss(t){return void 0===t||1===t}function sr({scale:t,scaleX:e,scaleY:i}){return!ss(t)||!ss(e)||!ss(i)}function sn(t){return sr(t)||so(t)||t.z||t.rotate||t.rotateX||t.rotateY}function so(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function sa(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function sl(t,e=0,i=1,s,r){t.min=sa(t.min,e,i,s,r),t.max=sa(t.max,e,i,s,r)}function sh(t,{x:e,y:i}){sl(t.x,e.translate,e.scale,e.originPoint),sl(t.y,i.translate,i.scale,i.originPoint)}function su(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function sc(t,e){t.min=t.min+e,t.max=t.max+e}function sd(t,e,[i,s,r]){let n=void 0!==e[r]?e[r]:.5,o=eS(t.min,t.max,n);sl(t,e[i],e[s],o,e.scale)}let sp=["x","scaleX","originX"],sf=["y","scaleY","originY"];function sm(t,e){sd(t.x,e,sp),sd(t.y,e,sf)}function sg(t,e){return si(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let sy=({current:t})=>t?t.ownerDocument.defaultView:null,sv=new WeakMap;class sb{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=st(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iH(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tO(t,"page").point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tW(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),se(t=>{let e=this.getAxisMotionValue(t).get()||0;if(J.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];if(s){let t=iK(s);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),r&&tC.Wi.update(()=>r(t,e),!1,!0);let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>se(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:sy(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&tC.Wi.update(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!sx(t,s,this.currentDirection))return;let r=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eS(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eS(i,t,s.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),r.set(n)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,r=this.constraints;e&&f(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:r}){return{x:i3(t.x,i,r),y:i3(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i6(t,"left","right"),y:i6(t,"top","bottom")}}(i),r!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&se(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!f(e))return!1;let s=e.current;(0,t4.k)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(t,e,i){let s=sg(t,i),{scroll:r}=e;return r&&(sc(s.x,r.offset.x),sc(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),o={x:i5((t=r.layout.layoutBox).x,n.x),y:i5(t.y,n.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=si(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(se(o=>{if(!sx(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return i.start(ix(t,i,0,e))}stopAnimation(){se(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){se(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){se(e=>{let{drag:i}=this.getProps();if(!sx(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[e];r.set(t[e]-eS(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!f(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};se(t=>{let e=this.getAxisMotionValue(t);if(e){let i=e.get();s[t]=function(t,e){let i=.5,s=iK(t),r=iK(e);return r>s?i=eZ(e.min,e.max-s,t.min):s>r&&(i=eZ(t.min,t.max-r,e.min)),I(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),se(e=>{if(!sx(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:n}=this.constraints[e];i.set(eS(r,n,s[e]))})}addListeners(){if(!this.visualElement.current)return;sv.set(this.visualElement,this);let t=tB(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();f(t)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),e();let r=tL(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(se(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),s(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:o}}}function sx(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sw extends t${constructor(t){super(t),this.removeGroupControls=tX.Z,this.removeListeners=tX.Z,this.controls=new sb(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tX.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let sP=t=>(e,i)=>{t&&tC.Wi.update(()=>t(e,i))};class sT extends t${constructor(){super(...arguments),this.removePointerDownListener=tX.Z}onPointerDown(t){this.session=new iH(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:sy(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:sP(t),onStart:sP(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&tC.Wi.update(()=>s(t,e))}}}mount(){this.removePointerDownListener=tB(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let sA={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sk(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sE={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let i=sk(t,e.target.x),s=sk(t,e.target.y);return`${i}% ${s}%`}};class sS extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;Object.assign(R,sC),r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),sA.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,n=i.projection;return n&&(n.isPresent=r,s||t.layoutDependency!==e||void 0===e?n.willUpdate():this.safeToRemove(),t.isPresent===r||(r?n.promote():n.relegate()||tC.Wi.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function s_(t){let[e,i]=function(){let t=(0,o.useContext)(h.O);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:i,register:s}=t,r=(0,o.useId)();return(0,o.useEffect)(()=>s(r),[]),!e&&i?[!1,()=>i&&i(r)]:[!0]}(),s=(0,o.useContext)(k.p);return o.createElement(sS,{...t,layoutGroup:s,switchLayoutGroup:(0,o.useContext)(E),isPresent:e,safeToRemove:i})}let sC={borderRadius:{...sE,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sE,borderTopRightRadius:sE,borderBottomLeftRadius:sE,borderBottomRightRadius:sE,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=eW.parse(t);if(s.length>5)return t;let r=eW.createTransformer(t),n="number"!=typeof s[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+n]/=o,s[1+n]/=a;let l=eS(o,a,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}},sR=["TopLeft","TopRight","BottomLeft","BottomRight"],sV=sR.length,sL=t=>"string"==typeof t?parseFloat(t):t,sD=t=>"number"==typeof t||Q.test(t);function sO(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sM=sj(0,.5,ed),sB=sj(.5,.95,tX.Z);function sj(t,e,i){return s=>s<t?0:s>e?1:i(eZ(t,e,s))}function sF(t,e){t.min=e.min,t.max=e.max}function sN(t,e){sF(t.x,e.x),sF(t.y,e.y)}function sU(t,e,i,s,r){return t-=e,t=s+1/i*(t-s),void 0!==r&&(t=s+1/r*(t-s)),t}function sI(t,e,[i,s,r],n,o){!function(t,e=0,i=1,s=.5,r,n=t,o=t){if(J.test(e)&&(e=parseFloat(e),e=eS(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eS(n.min,n.max,s);t===n&&(a-=e),t.min=sU(t.min,e,i,a,r),t.max=sU(t.max,e,i,a,r)}(t,e[i],e[s],e[r],e.scale,n,o)}let sW=["x","scaleX","originX"],sq=["y","scaleY","originY"];function s$(t,e,i,s){sI(t.x,e,sW,i?i.x:void 0,s?s.x:void 0),sI(t.y,e,sq,i?i.y:void 0,s?s.y:void 0)}function sH(t){return 0===t.translate&&1===t.scale}function sz(t){return sH(t.x)&&sH(t.y)}function sY(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function sZ(t){return iK(t.x)/iK(t.y)}class sX{constructor(){this.members=[]}add(t){iT(this.members,t),t.scheduleRender()}remove(t){if(iA(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function sK(t,e,i){let s="",r=t.x.translate/e.x,n=t.y.translate/e.y;if((r||n)&&(s=`translate3d(${r}px, ${n}px, 0) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{rotate:t,rotateX:e,rotateY:r}=i;t&&(s+=`rotate(${t}deg) `),e&&(s+=`rotateX(${e}deg) `),r&&(s+=`rotateY(${r}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(s+=`scale(${o}, ${a})`),s||"none"}let sG=(t,e)=>t.depth-e.depth;class sJ{constructor(){this.children=[],this.isDirty=!1}add(t){iT(this.children,t),this.isDirty=!0}remove(t){iA(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sG),this.isDirty=!1,this.children.forEach(t)}}let sQ=["","X","Y","Z"],s0={visibility:"hidden"},s1=0,s2={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function s3({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=s1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,s2.totalNodes=s2.resolvedTargetDeltas=s2.recalculatedProjection=0,this.nodes.forEach(s4),this.nodes.forEach(rs),this.nodes.forEach(rr),this.nodes.forEach(s8),window.MotionDebug&&window.MotionDebug.record(s2)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sJ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ik),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=performance.now(),s=({timestamp:r})=>{let n=r-i;n>=e&&((0,tC.Pn)(s),t(n-e))};return tC.Wi.read(s,!0),()=>(0,tC.Pn)(s)}(s,250),sA.hasAnimatedSinceResize&&(sA.hasAnimatedSinceResize=!1,this.nodes.forEach(ri))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&n&&(s||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||ru,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!sY(this.targetLayout,s)||i,h=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...iv(r,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ri(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,tC.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rn),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s7);return}this.isUpdating||this.nodes.forEach(rt),this.isUpdating=!1,this.nodes.forEach(re),this.nodes.forEach(s5),this.nodes.forEach(s6),this.clearAllSnapshots();let t=performance.now();tC.frameData.delta=I(0,1e3/60,t-tC.frameData.timestamp),tC.frameData.timestamp=t,tC.frameData.isProcessing=!0,tC.S6.update.process(tC.frameData),tC.S6.preRender.process(tC.frameData),tC.S6.render.process(tC.frameData),tC.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(s9),this.sharedNodes.forEach(ro)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tC.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tC.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=st(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:s(this.instance),offset:i(this.instance)})}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!sz(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;t&&(e||sn(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),rp((e=s).x),rp(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return st();let e=t.measureViewportBox(),{scroll:i}=this.root;return i&&(sc(e.x,i.offset.x),sc(e.y,i.offset.y)),e}removeElementScroll(t){let e=st();sN(e,t);for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;if(s!==this.root&&r&&n.layoutScroll){if(r.isRoot){sN(e,t);let{scroll:i}=this.root;i&&(sc(e.x,-i.offset.x),sc(e.y,-i.offset.y))}sc(e.x,r.offset.x),sc(e.y,r.offset.y)}}return e}applyTransform(t,e=!1){let i=st();sN(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&sm(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),sn(s.latestValues)&&sm(i,s.latestValues)}return sn(this.latestValues)&&sm(i,this.latestValues),i}removeTransform(t){let e=st();sN(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!sn(i.latestValues))continue;sr(i.latestValues)&&i.updateSnapshot();let s=st();sN(s,i.measurePageBox()),s$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return sn(this.latestValues)&&s$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tC.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,r;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==n;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tC.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=st(),this.relativeTargetOrigin=st(),i2(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=st(),this.targetWithTransforms=st()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,r=this.relativeParent.target,i0(i.x,s.x,r.x),i0(i.y,s.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sN(this.target,this.layout.layoutBox),sh(this.target,this.targetDelta)):sN(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=st(),this.relativeTargetOrigin=st(),i2(this.relativeTargetOrigin,this.target,t.target),sN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}s2.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||sr(this.parent.latestValues)||so(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===tC.frameData.timestamp&&(s=!1),s)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;sN(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let r,n;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(r=i[a]).projectionDelta;let o=r.instance;(!o||!o.style||"contents"!==o.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&sm(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,sh(t,n)),s&&sn(r.latestValues)&&sm(t,r.latestValues))}e.x=su(e.x),e.y=su(e.y)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=i9(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=i9(),this.projectionDeltaWithTransform=i9());let h=this.projectionTransform;iQ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=sK(this.projectionDelta,this.treeScale),(this.projectionTransform!==h||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),s2.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},o=i9();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=st(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(rh));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(ra(o.x,t.x,s),ra(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,f;i2(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,rl(p.x,f.x,a.x,s),rl(p.y,f.y,a.y,s),i&&(h=this.relativeTarget,d=i,h.x.min===d.x.min&&h.x.max===d.x.max&&h.y.min===d.y.min&&h.y.max===d.y.max)&&(this.isProjectionDirty=!1),i||(i=st()),sN(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,s,r,n){r?(t.opacity=eS(0,void 0!==i.opacity?i.opacity:1,sM(s)),t.opacityExit=eS(void 0!==e.opacity?e.opacity:1,0,sB(s))):n&&(t.opacity=eS(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let r=0;r<sV;r++){let n=`border${sR[r]}Radius`,o=sO(e,n),a=sO(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sD(o)===sD(a)?(t[n]=Math.max(eS(sL(o),sL(a),s),0),(J.test(a)||J.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=eS(e.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,tC.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tC.Wi.update(()=>{sA.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=O(t)?t:iC(t);return s.start(ix("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&rf(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||st();let e=iK(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iK(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sN(e,i),sm(e,r),iQ(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sX),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.rotate||i.rotateX||i.rotateY||i.rotateZ)&&(e=!0),!e)return;let s={};for(let e=0;e<sQ.length;e++){let r="rotate"+sQ[e];i[r]&&(s[r]=i[r],t.setStaticValue(r,0))}for(let e in t.render(),s)t.setStaticValue(e,s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return s0;let s={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=tS(null==t?void 0:t.pointerEvents)||"",s.transform=r?r(this.latestValues,""):"none",s;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tS(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!sn(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=n.animationValues||n.latestValues;this.applyTransformsToTarget(),s.transform=sK(this.projectionDeltaWithTransform,this.treeScale,o),r&&(s.transform=r(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,n.animationValues?s.opacity=n===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=n===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,R){if(void 0===o[t])continue;let{correct:e,applyTo:i}=R[t],r="none"===s.transform?o[t]:e(o[t],n);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=r}else s[t]=r}return this.options.layoutId&&(s.pointerEvents=n===this?tS(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(s7),this.root.sharedNodes.clear()}}}function s5(t){t.updateLayout()}function s6(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:r}=t.options,n=i.source!==t.layout.source;"size"===r?se(t=>{let s=n?i.measuredBox[t]:i.layoutBox[t],r=iK(s);s.min=e[t].min,s.max=s.min+r}):rf(r,i.layoutBox,e)&&se(s=>{let r=n?i.measuredBox[s]:i.layoutBox[s],o=iK(e[s]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=i9();iQ(o,e,i.layoutBox);let a=i9();n?iQ(a,t.applyTransform(s,!0),i.measuredBox):iQ(a,e,i.layoutBox);let l=!sz(o),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let o=st();i2(o,i.layoutBox,r.layoutBox);let a=st();i2(a,e,n.layoutBox),sY(o,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s4(t){s2.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s8(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s9(t){t.clearSnapshot()}function s7(t){t.clearMeasurements()}function rt(t){t.isLayoutDirty=!1}function re(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ri(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rs(t){t.resolveTargetDelta()}function rr(t){t.calcProjection()}function rn(t){t.resetRotation()}function ro(t){t.removeLeadSnapshot()}function ra(t,e,i){t.translate=eS(e.translate,0,i),t.scale=eS(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rl(t,e,i,s){t.min=eS(e.min,i.min,s),t.max=eS(e.max,i.max,s)}function rh(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ru={duration:.45,ease:[.4,0,.1,1]},rc=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),rd=rc("applewebkit/")&&!rc("chrome/")?Math.round:tX.Z;function rp(t){t.min=rd(t.min),t.max=rd(t.max)}function rf(t,e,i){return"position"===t||"preserve-aspect"===t&&!iG(sZ(e),sZ(i),.2)}let rm=s3({attachResizeListener:(t,e)=>tL(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rg={current:void 0},ry=s3({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rg.current){let t=new rm({});t.mount(window),t.setOptions({layoutScroll:!0}),rg.current=t}return rg.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),rv=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function rb(t,e,i=1){(0,t4.k)(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[s,r]=function(t){let e=rv.exec(t);if(!e)return[,];let[,i,s]=e;return[i,s]}(t);if(!s)return;let n=window.getComputedStyle(e).getPropertyValue(s);if(n){let t=n.trim();return iP(t)?parseFloat(t):t}return N(r)?rb(r,e,i+1):r}let rx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),rw=t=>rx.has(t),rP=t=>Object.keys(t).some(rw),rT=t=>t===W||t===Q,rA=(t,e)=>parseFloat(t.split(", ")[e]),rk=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let r=s.match(/^matrix3d\((.+)\)$/);if(r)return rA(r[1],e);{let e=s.match(/^matrix\((.+)\)$/);return e?rA(e[1],t):0}},rE=new Set(["x","y","z"]),rS=V.filter(t=>!rE.has(t)),r_={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:rk(4,13),y:rk(5,14)};r_.translateX=r_.x,r_.translateY=r_.y;let rC=(t,e,i)=>{let s=e.measureViewportBox(),r=e.current,n=getComputedStyle(r),{display:o}=n,a={};"none"===o&&e.setStaticValue("display",t.display||"block"),i.forEach(t=>{a[t]=r_[t](s,n)}),e.render();let l=e.measureViewportBox();return i.forEach(i=>{let s=e.getValue(i);s&&s.jump(a[i]),t[i]=r_[i](l,n)}),t},rR=(t,e,i={},s={})=>{e={...e},s={...s};let r=Object.keys(e).filter(rw),n=[],o=!1,a=[];if(r.forEach(r=>{let l;let h=t.getValue(r);if(!t.hasValue(r))return;let u=i[r],c=iL(u),d=e[r];if(tA(d)){let t=d.length,e=null===d[0]?1:0;c=iL(u=d[e]);for(let i=e;i<t&&null!==d[i];i++)l?(0,t4.k)(iL(d[i])===l,"All keyframes must be of the same type"):(l=iL(d[i]),(0,t4.k)(l===c||rT(c)&&rT(l),"Keyframes must be of the same dimension as the current value"))}else l=iL(d);if(c!==l){if(rT(c)&&rT(l)){let t=h.get();"string"==typeof t&&h.set(parseFloat(t)),"string"==typeof d?e[r]=parseFloat(d):Array.isArray(d)&&l===Q&&(e[r]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===u||0===d)?0===u?h.set(l.transform(u)):e[r]=c.transform(d):(o||(n=function(t){let e=[];return rS.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(i.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(r),s[r]=void 0!==s[r]?s[r]:e[r],h.jump(d))}}),!a.length)return{target:e,transitionEnd:s};{let i=a.indexOf("height")>=0?window.pageYOffset:null,r=rC(e,t,a);return n.length&&n.forEach(([e,i])=>{t.getValue(e).set(i)}),t.render(),A.j&&null!==i&&window.scrollTo({top:i}),{target:r,transitionEnd:s}}},rV=(t,e,i,s)=>{var r,n;let o=function(t,{...e},i){let s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:i};for(let r in i&&(i={...i}),t.values.forEach(t=>{let e=t.get();if(!N(e))return;let i=rb(e,s);i&&t.set(i)}),e){let t=e[r];if(!N(t))continue;let n=rb(t,s);n&&(e[r]=n,i||(i={}),void 0===i[r]&&(i[r]=t))}return{target:e,transitionEnd:i}}(t,e,s);return e=o.target,s=o.transitionEnd,r=e,n=s,rP(r)?rR(t,r,i,n):{target:r,transitionEnd:n}},rL={current:null},rD={current:!1},rO=new WeakMap,rM=Object.keys(T),rB=rM.length,rj=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],rF=v.length;class rN{constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,visualState:r},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tC.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=n,this.isControllingVariants=b(e),this.isVariantNode=x(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...h}=this.scrapeMotionValuesFromProps(e,{});for(let t in h){let e=h[t];void 0!==o[t]&&O(e)&&(e.set(o[t],!1),iw(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,rO.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),rD.current||function(){if(rD.current=!0,A.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rL.current=t.matches;t.addListener(e),e()}else rL.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rL.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in rO.delete(this.current),this.projection&&this.projection.unmount(),(0,tC.Pn)(this.notifyUpdate),(0,tC.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let i=L.has(t),s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tC.Wi.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{s(),r()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},i,s,r){let n,o;for(let t=0;t<rB;t++){let i=rM[t],{isEnabled:s,Feature:r,ProjectionNode:a,MeasureLayout:l}=T[i];a&&(n=a),s(e)&&(!this.features[i]&&r&&(this.features[i]=new r(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&n){this.projection=new n(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:i,drag:s,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:i,alwaysMeasureLayout:!!s||o&&f(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof i?i:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):st()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rj.length;e++){let i=rj[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){let{willChange:s}=e;for(let r in e){let n=e[r],o=i[r];if(O(n))t.addValue(r,n),iw(s)&&s.add(r);else if(O(o))t.addValue(r,iC(n,{owner:t})),iw(s)&&s.remove(r);else if(o!==n){if(t.hasValue(r)){let e=t.getValue(r);e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,iC(void 0!==e?e:n,{owner:t}))}}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<rF;t++){let i=v[t],s=this.props[i];(m(s)||!1===s)&&(e[i]=s)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=iC(e,{owner:this}),this.addValue(t,i)),i}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:i}=this.props,s="string"==typeof i||"object"==typeof i?null===(e=tP(this.props,i))||void 0===e?void 0:e[t]:void 0;if(i&&void 0!==s)return s;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||O(r)?void 0!==this.initialValues[t]&&void 0===s?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new ik),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rU extends rN{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...i},{transformValues:s},r){let n=function(t,e,i){let s={};for(let r in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(r,e);if(void 0!==t)s[r]=t;else{let t=i.getValue(r);t&&(s[r]=t.get())}}return s}(i,t||{},this);if(s&&(e&&(e=s(e)),i&&(i=s(i)),n&&(n=s(n))),r){!function(t,e,i){var s,r;let n=Object.keys(e).filter(e=>!t.hasValue(e)),o=n.length;if(o)for(let a=0;a<o;a++){let o=n[a],l=e[o],h=null;Array.isArray(l)&&(h=l[0]),null===h&&(h=null!==(r=null!==(s=i[o])&&void 0!==s?s:t.readValue(o))&&void 0!==r?r:e[o]),null!=h&&("string"==typeof h&&(iP(h)||iy(h))?h=parseFloat(h):!iO(h)&&eW.test(l)&&(h=ig(o,l)),t.addValue(o,iC(h,{owner:t})),void 0===i[o]&&(i[o]=h),null!==h&&t.setBaseTarget(o,h))}}(this,i,n);let t=rV(this,i,n,e);e=t.transitionEnd,i=t.target}return{transition:t,transitionEnd:e,...i}}}class rI extends rU{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(L.has(e)){let t=im(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(F(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return sg(t,e)}build(t,e,i,s){tn(t,e,i,s.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tx(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;O(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,i,s){ty(t,e,i,s)}}class rW extends rU{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(L.has(e)){let t=im(e);return t&&t.default||0}return e=tv.has(e)?e:d(e),t.getAttribute(e)}measureInstanceViewportBox(){return st()}scrapeMotionValuesFromProps(t,e){return tw(t,e)}build(t,e,i,s){tf(t,e,i,this.isSVGTag,s.transformTemplate)}renderInstance(t,e,i,s){tb(t,e,i,s)}mount(t){this.isSVGTag=tg(t.tagName),super.mount(t)}}let rq=(t,e)=>C(t)?new rW(e,{enableHardwareAcceleration:!1}):new rI(e,{enableHardwareAcceleration:!0}),r$={animation:{Feature:iI},exit:{Feature:iq},inView:{Feature:t3},tap:{Feature:tG},focus:{Feature:tY},hover:{Feature:tz},pan:{Feature:sT},drag:{Feature:sw,ProjectionNode:ry,MeasureLayout:s_},layout:{ProjectionNode:ry,MeasureLayout:s_}},rH=function(t){function e(e,i={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:r}){t&&function(t){for(let e in t)T[e]={...T[e],...t[e]}}(t);let n=(0,o.forwardRef)(function(n,d){var g;let y;let v={...(0,o.useContext)(a),...n,layoutId:function({layoutId:t}){let e=(0,o.useContext)(k.p).id;return e&&void 0!==t?e+"-"+t:t}(n)},{isStatic:x}=v,P=function(t){let{initial:e,animate:i}=function(t,e){if(b(t)){let{initial:e,animate:i}=t;return{initial:!1===e||m(e)?e:void 0,animate:m(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:e,animate:i}),[w(e),w(i)])}(n),T=s(n,x);if(!x&&A.j){P.visualElement=function(t,e,i,s){let{visualElement:r}=(0,o.useContext)(l),n=(0,o.useContext)(c),d=(0,o.useContext)(h.O),f=(0,o.useContext)(a).reducedMotion,m=(0,o.useRef)();s=s||n.renderer,!m.current&&s&&(m.current=s(t,{visualState:e,parent:r,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:f}));let g=m.current;(0,o.useInsertionEffect)(()=>{g&&g.update(i,d)});let y=(0,o.useRef)(!!(i[p]&&!window.HandoffComplete));return(0,u.L)(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),(0,o.useEffect)(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(r,T,v,e);let i=(0,o.useContext)(E),s=(0,o.useContext)(c).strict;P.visualElement&&(y=P.visualElement.loadFeatures(v,s,t,i))}return o.createElement(l.Provider,{value:P},y&&P.visualElement?o.createElement(y,{visualElement:P.visualElement,...v}):null,i(r,n,(g=P.visualElement,(0,o.useCallback)(t=>{t&&T.mount&&T.mount(t),g&&(t?g.mount(t):g.unmount()),d&&("function"==typeof d?d(t):f(d)&&(d.current=t))},[g])),T,x,P.visualElement))});return n[S]=r,n}(t(e,i))}if("undefined"==typeof Proxy)return e;let i=new Map;return new Proxy(e,{get:(t,s)=>(i.has(s)||i.set(s,e(s)),i.get(s))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},i,s){return{...C(t)?tR:tV,preloadedFeatures:i,useRender:function(t=!1){return(e,i,s,{latestValues:r},n)=>{let a=(C(e)?function(t,e,i,s){let r=(0,o.useMemo)(()=>{let i=tm();return tf(i,e,{enableHardwareAcceleration:!1},tg(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ta(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e,i){let s={},r=function(t,e,i){let s=t.style||{},r={};return ta(r,s,t),Object.assign(r,function({transformTemplate:t},e,i){return(0,o.useMemo)(()=>{let s=to();return tn(s,e,{enableHardwareAcceleration:!i},t),Object.assign({},s.vars,s.style)},[e])}(t,e,i)),t.transformValues?t.transformValues(r):r}(t,e,i);return t.drag&&!1!==t.dragListener&&(s.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=r,s})(i,r,n,e),l={...function(t,e,i){let s={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(tu(r)||!0===i&&th(r)||!e&&!th(r)||t.draggable&&r.startsWith("onDrag"))&&(s[r]=t[r]);return s}(i,"string"==typeof e,t),...a,ref:s},{children:h}=i,u=(0,o.useMemo)(()=>O(h)?h.get():h,[h]);return(0,o.createElement)(e,{...l,children:u})}}(e),createVisualElement:s,Component:t}})(t,e,r$,rq))},6567:function(t,e,i){i.d(e,{K:function(){return r},k:function(){return n}});var s=i(6977);let r=s.Z,n=s.Z},6613:function(t,e,i){i.d(e,{j:function(){return s}});let s="undefined"!=typeof document},6977:function(t,e,i){i.d(e,{Z:function(){return s}});let s=t=>t},961:function(t,e,i){i.d(e,{h:function(){return r}});var s=i(2265);function r(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},538:function(t,e,i){i.d(e,{L:function(){return r}});var s=i(2265);let r=i(6613).j?s.useLayoutEffect:s.useEffect},4337:function(t,e,i){let s,r;i.d(e,{io:function(){return tE}});var n,o,a={};i.r(a),i.d(a,{Decoder:function(){return tv},Encoder:function(){return tg},PacketType:function(){return o},protocol:function(){return tm}});let l=Object.create(null);l.open="0",l.close="1",l.ping="2",l.pong="3",l.message="4",l.upgrade="5",l.noop="6";let h=Object.create(null);Object.keys(l).forEach(t=>{h[l[t]]=t});let u={type:"error",data:"parser error"},c="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),d="function"==typeof ArrayBuffer,p=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,f=({type:t,data:e},i,s)=>c&&e instanceof Blob?i?s(e):m(e,s):d&&(e instanceof ArrayBuffer||p(e))?i?s(e):m(new Blob([e]),s):s(l[t]+(e||"")),m=(t,e)=>{let i=new FileReader;return i.onload=function(){e("b"+(i.result.split(",")[1]||""))},i.readAsDataURL(t)};function g(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",v="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<y.length;t++)v[y.charCodeAt(t)]=t;let b=t=>{let e=.75*t.length,i=t.length,s,r=0,n,o,a,l;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let h=new ArrayBuffer(e),u=new Uint8Array(h);for(s=0;s<i;s+=4)n=v[t.charCodeAt(s)],o=v[t.charCodeAt(s+1)],a=v[t.charCodeAt(s+2)],l=v[t.charCodeAt(s+3)],u[r++]=n<<2|o>>4,u[r++]=(15&o)<<4|a>>2,u[r++]=(3&a)<<6|63&l;return h},x="function"==typeof ArrayBuffer,w=(t,e)=>{if("string"!=typeof t)return{type:"message",data:T(t,e)};let i=t.charAt(0);return"b"===i?{type:"message",data:P(t.substring(1),e)}:h[i]?t.length>1?{type:h[i],data:t.substring(1)}:{type:h[i]}:u},P=(t,e)=>x?T(b(t),e):{base64:!0,data:t},T=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,A=(t,e)=>{let i=t.length,s=Array(i),r=0;t.forEach((t,n)=>{f(t,!1,t=>{s[n]=t,++r===i&&e(s.join("\x1e"))})})},k=(t,e)=>{let i=t.split("\x1e"),s=[];for(let t=0;t<i.length;t++){let r=w(i[t],e);if(s.push(r),"error"===r.type)break}return s};function E(t){return t.reduce((t,e)=>t+e.length,0)}function S(t,e){if(t[0].length===e)return t.shift();let i=new Uint8Array(e),s=0;for(let r=0;r<e;r++)i[r]=t[0][s++],s===t[0].length&&(t.shift(),s=0);return t.length&&s<t[0].length&&(t[0]=t[0].slice(s)),i}function _(t){if(t)return function(t){for(var e in _.prototype)t[e]=_.prototype[e];return t}(t)}_.prototype.on=_.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},_.prototype.once=function(t,e){function i(){this.off(t,i),e.apply(this,arguments)}return i.fn=e,this.on(t,i),this},_.prototype.off=_.prototype.removeListener=_.prototype.removeAllListeners=_.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i,s=this._callbacks["$"+t];if(!s)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var r=0;r<s.length;r++)if((i=s[r])===e||i.fn===e){s.splice(r,1);break}return 0===s.length&&delete this._callbacks["$"+t],this},_.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),i=this._callbacks["$"+t],s=1;s<arguments.length;s++)e[s-1]=arguments[s];if(i){i=i.slice(0);for(var s=0,r=i.length;s<r;++s)i[s].apply(this,e)}return this},_.prototype.emitReserved=_.prototype.emit,_.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},_.prototype.hasListeners=function(t){return!!this.listeners(t).length};let C="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),R="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function V(t,...e){return e.reduce((e,i)=>(t.hasOwnProperty(i)&&(e[i]=t[i]),e),{})}let L=R.setTimeout,D=R.clearTimeout;function O(t,e){e.useNativeTimers?(t.setTimeoutFn=L.bind(R),t.clearTimeoutFn=D.bind(R)):(t.setTimeoutFn=R.setTimeout.bind(R),t.clearTimeoutFn=R.clearTimeout.bind(R))}function M(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class B extends Error{constructor(t,e,i){super(t),this.description=e,this.context=i,this.type="TransportError"}}class j extends _{constructor(t){super(),this.writable=!1,O(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,i){return super.emitReserved("error",new B(t,e,i)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=w(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let i in t)t.hasOwnProperty(i)&&(e.length&&(e+="&"),e+=encodeURIComponent(i)+"="+encodeURIComponent(t[i]));return e}(t);return e.length?"?"+e:""}}class F extends j{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){k(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,A(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=M()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let N=!1;try{N="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let U=N;function I(){}class W extends F{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,i=location.port;i||(i=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||i!==t.port}}doWrite(t,e){let i=this.request({method:"POST",data:t});i.on("success",e),i.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class q extends _{constructor(t,e,i){super(),this.createRequest=t,O(this,i),this._opts=i,this._method=i.method||"GET",this._uri=e,this._data=void 0!==i.data?i.data:null,this._create()}_create(){var t;let e=V(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let i=this._xhr=this.createRequest(e);try{i.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in i.setDisableHeaderCheck&&i.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&i.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{i.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{i.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this._opts.cookieJar)||void 0===t||t.addCookies(i),"withCredentials"in i&&(i.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(i.timeout=this._opts.requestTimeout),i.onreadystatechange=()=>{var t;3===i.readyState&&(null===(t=this._opts.cookieJar)||void 0===t||t.parseCookies(i.getResponseHeader("set-cookie"))),4===i.readyState&&(200===i.status||1223===i.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof i.status?i.status:0)},0))},i.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=q.requestsCount++,q.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=I,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete q.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(q.requestsCount=0,q.requests={},"undefined"!=typeof document){if("function"==typeof attachEvent)attachEvent("onunload",$);else if("function"==typeof addEventListener){let t="onpagehide"in R?"pagehide":"unload";addEventListener(t,$,!1)}}function $(){for(let t in q.requests)q.requests.hasOwnProperty(t)&&q.requests[t].abort()}let H=function(){let t=Y({xdomain:!1});return t&&null!==t.responseType}();class z extends W{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=H&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new q(Y,this.uri(),t)}}function Y(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||U))return new XMLHttpRequest}catch(t){}if(!e)try{return new R[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let Z="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class X extends j{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,i=Z?{}:V(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(i.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,i)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let i=t[e],s=e===t.length-1;f(i,this.supportsBinary,t=>{try{this.doWrite(i,t)}catch(t){}s&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=M()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let K=R.WebSocket||R.MozWebSocket;class G extends X{createSocket(t,e,i){return Z?new K(t,e,i):e?new K(t,e):new K(t)}doWrite(t,e){this.ws.send(e)}}class J extends j{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){r||(r=new TextDecoder);let i=[],s=0,n=-1,o=!1;return new TransformStream({transform(a,l){for(i.push(a);;){if(0===s){if(1>E(i))break;let t=S(i,1);o=(128&t[0])==128,s=(n=127&t[0])<126?3:126===n?1:2}else if(1===s){if(2>E(i))break;let t=S(i,2);n=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),s=3}else if(2===s){if(8>E(i))break;let t=S(i,8),e=new DataView(t.buffer,t.byteOffset,t.length),r=e.getUint32(0);if(r>2097151){l.enqueue(u);break}n=4294967296*r+e.getUint32(4),s=3}else{if(E(i)<n)break;let t=S(i,n);l.enqueue(w(o?t:r.decode(t),e)),s=0}if(0===n||n>t){l.enqueue(u);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),i=t.readable.pipeThrough(e).getReader(),n=new TransformStream({transform(t,e){var i;i=i=>{let s;let r=i.length;if(r<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,r);else if(r<65536){s=new Uint8Array(3);let t=new DataView(s.buffer);t.setUint8(0,126),t.setUint16(1,r)}else{s=new Uint8Array(9);let t=new DataView(s.buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(r))}t.data&&"string"!=typeof t.data&&(s[0]|=128),e.enqueue(s),e.enqueue(i)},c&&t.data instanceof Blob?t.data.arrayBuffer().then(g).then(i):d&&(t.data instanceof ArrayBuffer||p(t.data))?i(g(t.data)):f(t,!1,t=>{s||(s=new TextEncoder),i(s.encode(t))})}});n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();let o=()=>{i.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let i=t[e],s=e===t.length-1;this._writer.write(i).then(()=>{s&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null===(t=this._transport)||void 0===t||t.close()}}let Q={websocket:G,webtransport:J,polling:z},tt=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,te=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ti(t){if(t.length>8e3)throw"URI too long";let e=t,i=t.indexOf("["),s=t.indexOf("]");-1!=i&&-1!=s&&(t=t.substring(0,i)+t.substring(i,s).replace(/:/g,";")+t.substring(s,t.length));let r=tt.exec(t||""),n={},o=14;for(;o--;)n[te[o]]=r[o]||"";return -1!=i&&-1!=s&&(n.source=e,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(t,e){let i=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&i.splice(0,1),"/"==e.slice(-1)&&i.splice(i.length-1,1),i}(0,n.path),n.queryKey=function(t,e){let i={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,s){e&&(i[e]=s)}),i}(0,n.query),n}let ts="function"==typeof addEventListener&&"function"==typeof removeEventListener,tr=[];ts&&addEventListener("offline",()=>{tr.forEach(t=>t())},!1);class tn extends _{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let i=ti(t);e.hostname=i.host,e.secure="https"===i.protocol||"wss"===i.protocol,e.port=i.port,i.query&&(e.query=i.query)}else e.host&&(e.hostname=ti(e.host).host);O(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},i=t.split("&");for(let t=0,s=i.length;t<s;t++){let s=i[t].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}(this.opts.query)),ts&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},tr.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let i=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](i)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let t=this.opts.rememberUpgrade&&tn.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",tn.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let i=this.writeBuffer[e].data;if(i&&(t+="string"==typeof i?function(t){let e=0,i=0;for(let s=0,r=t.length;s<r;s++)(e=t.charCodeAt(s))<128?i+=1:e<2048?i+=2:e<55296||e>=57344?i+=3:(s++,i+=4);return i}(i):Math.ceil(1.33*(i.byteLength||i.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,C(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,i){return this._sendPacket("message",t,e,i),this}send(t,e,i){return this._sendPacket("message",t,e,i),this}_sendPacket(t,e,i,s){if("function"==typeof e&&(s=e,e=void 0),"function"==typeof i&&(s=i,i=null),"closing"===this.readyState||"closed"===this.readyState)return;(i=i||{}).compress=!1!==i.compress;let r={type:t,data:e,options:i};this.emitReserved("packetCreate",r),this.writeBuffer.push(r),s&&this.once("flush",s),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},i=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?i():t()}):this.upgrading?i():t()),this}_onError(t){if(tn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ts&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=tr.indexOf(this._offlineEventListener);-1!==t&&tr.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}tn.protocol=4;class to extends tn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),i=!1;tn.priorWebsocketSuccess=!1;let s=()=>{i||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!i){if("pong"===t.type&&"probe"===t.data)this.upgrading=!0,this.emitReserved("upgrading",e),e&&(tn.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{i||"closed"===this.readyState||(h(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())}));else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}}))};function r(){i||(i=!0,h(),e.close(),e=null)}let n=t=>{let i=Error("probe error: "+t);i.transport=e.name,r(),this.emitReserved("upgradeError",i)};function o(){n("transport closed")}function a(){n("socket closed")}function l(t){e&&t.name!==e.name&&r()}let h=()=>{e.removeListener("open",s),e.removeListener("error",n),e.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};e.once("open",s),e.once("error",n),e.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{i||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let i=0;i<t.length;i++)~this.transports.indexOf(t[i])&&e.push(t[i]);return e}}class ta extends to{constructor(t,e={}){let i="object"==typeof t?t:e;(!i.transports||i.transports&&"string"==typeof i.transports[0])&&(i.transports=(i.transports||["polling","websocket","webtransport"]).map(t=>Q[t]).filter(t=>!!t)),super(t,i)}}ta.protocol;let tl="function"==typeof ArrayBuffer,th=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,tu=Object.prototype.toString,tc="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===tu.call(Blob),td="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===tu.call(File);function tp(t){return tl&&(t instanceof ArrayBuffer||th(t))||tc&&t instanceof Blob||td&&t instanceof File}let tf=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],tm=5;(n=o||(o={}))[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK";class tg{constructor(t){this.replacer=t}encode(t){return(t.type===o.EVENT||t.type===o.ACK)&&function t(e,i){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let i=0,s=e.length;i<s;i++)if(t(e[i]))return!0;return!1}if(tp(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t(e[i]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===o.EVENT?o.BINARY_EVENT:o.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===o.BINARY_EVENT||t.type===o.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],i=t.data;return t.data=function t(e,i){if(!e)return e;if(tp(e)){let t={_placeholder:!0,num:i.length};return i.push(e),t}if(Array.isArray(e)){let s=Array(e.length);for(let r=0;r<e.length;r++)s[r]=t(e[r],i);return s}if("object"==typeof e&&!(e instanceof Date)){let s={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(s[r]=t(e[r],i));return s}return e}(i,e),t.attachments=e.length,{packet:t,buffers:e}}(t),i=this.encodeAsString(e.packet),s=e.buffers;return s.unshift(i),s}}function ty(t){return"[object Object]"===Object.prototype.toString.call(t)}class tv extends _{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let i=(e=this.decodeString(t)).type===o.BINARY_EVENT;i||e.type===o.BINARY_ACK?(e.type=i?o.EVENT:o.ACK,this.reconstructor=new tb(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(tp(t)||t.base64){if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+t)}decodeString(t){let e=0,i={type:Number(t.charAt(0))};if(void 0===o[i.type])throw Error("unknown packet type "+i.type);if(i.type===o.BINARY_EVENT||i.type===o.BINARY_ACK){let s=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let r=t.substring(s,e);if(r!=Number(r)||"-"!==t.charAt(e))throw Error("Illegal attachments");i.attachments=Number(r)}if("/"===t.charAt(e+1)){let s=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);i.nsp=t.substring(s,e)}else i.nsp="/";let s=t.charAt(e+1);if(""!==s&&Number(s)==s){let s=e+1;for(;++e;){let i=t.charAt(e);if(null==i||Number(i)!=i){--e;break}if(e===t.length)break}i.id=Number(t.substring(s,e+1))}if(t.charAt(++e)){let s=this.tryParse(t.substr(e));if(tv.isPayloadValid(i.type,s))i.data=s;else throw Error("invalid payload")}return i}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case o.CONNECT:return ty(e);case o.DISCONNECT:return void 0===e;case o.CONNECT_ERROR:return"string"==typeof e||ty(e);case o.EVENT:case o.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===tf.indexOf(e[0]));case o.ACK:case o.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class tb{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,i;let t=(e=this.reconPack,i=this.buffers,e.data=function t(e,i){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<i.length)return i[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let s=0;s<e.length;s++)e[s]=t(e[s],i);else if("object"==typeof e)for(let s in e)Object.prototype.hasOwnProperty.call(e,s)&&(e[s]=t(e[s],i));return e}(e.data,i),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tx(t,e,i){return t.on(e,i),function(){t.off(e,i)}}let tw=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class tP extends _{constructor(t,e,i){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,i&&i.auth&&(this.auth=i.auth),this._opts=Object.assign({},i),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tx(t,"open",this.onopen.bind(this)),tx(t,"packet",this.onpacket.bind(this)),tx(t,"error",this.onerror.bind(this)),tx(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var i,s,r;if(tw.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let n={type:o.EVENT,data:e};if(n.options={},n.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,i=e.pop();this._registerAckCallback(t,i),n.id=t}let a=null===(s=null===(i=this.io.engine)||void 0===i?void 0:i.transport)||void 0===s?void 0:s.writable,l=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!a||(l?(this.notifyOutgoingListeners(n),this.packet(n)):this.sendBuffer.push(n)),this.flags={},this}_registerAckCallback(t,e){var i;let s=null!==(i=this.flags.timeout)&&void 0!==i?i:this._opts.ackTimeout;if(void 0===s){this.acks[t]=e;return}let r=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},s),n=(...t)=>{this.io.clearTimeoutFn(r),e.apply(this,t)};n.withError=!0,this.acks[t]=n}emitWithAck(t,...e){return new Promise((i,s)=>{let r=(t,e)=>t?s(t):i(e);r.withError=!0,e.push(r),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let i={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...s)=>{if(i===this._queue[0])return null!==t?i.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...s)),i.pending=!1,this._drainQueue()}),this._queue.push(i),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:o.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(!(t.nsp!==this.nsp))switch(t.type){case o.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case o.EVENT:case o.BINARY_EVENT:this.onevent(t);break;case o.ACK:case o.BINARY_ACK:this.onack(t);break;case o.DISCONNECT:this.ondisconnect();break;case o.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,i=!1;return function(...s){i||(i=!0,e.packet({type:o.ACK,id:t,data:s}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:o.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let i=0;i<e.length;i++)if(t===e[i]){e.splice(i,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let i=0;i<e.length;i++)if(t===e[i]){e.splice(i,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tT(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tT.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),i=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-i:t+i}return 0|Math.min(t,this.max)},tT.prototype.reset=function(){this.attempts=0},tT.prototype.setMin=function(t){this.ms=t},tT.prototype.setMax=function(t){this.max=t},tT.prototype.setJitter=function(t){this.jitter=t};class tA extends _{constructor(t,e){var i;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,O(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(i=e.randomizationFactor)&&void 0!==i?i:.5),this.backoff=new tT({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let s=e.parser||a;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null===(e=this.backoff)||void 0===e||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null===(e=this.backoff)||void 0===e||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null===(e=this.backoff)||void 0===e||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new ta(this.uri,this.opts);let e=this.engine,i=this;this._readyState="opening",this.skipReconnect=!1;let s=tx(e,"open",function(){i.onopen(),t&&t()}),r=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},n=tx(e,"error",r);if(!1!==this._timeout){let t=this._timeout,i=this.setTimeoutFn(()=>{s(),r(Error("timeout")),e.close()},t);this.opts.autoUnref&&i.unref(),this.subs.push(()=>{this.clearTimeoutFn(i)})}return this.subs.push(s),this.subs.push(n),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tx(t,"ping",this.onping.bind(this)),tx(t,"data",this.ondata.bind(this)),tx(t,"error",this.onerror.bind(this)),tx(t,"close",this.onclose.bind(this)),tx(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){C(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let i=this.nsps[t];return i?this._autoConnect&&!i.active&&i.connect():(i=new tP(this,t,e),this.nsps[t]=i),i}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let i=0;i<e.length;i++)this.engine.write(e[i],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var i;this.cleanup(),null===(i=this.engine)||void 0===i||i.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let i=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&i.unref(),this.subs.push(()=>{this.clearTimeoutFn(i)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let tk={};function tE(t,e){let i;"object"==typeof t&&(e=t,t=void 0);let s=function(t,e="",i){let s=t;i=i||"undefined"!=typeof location&&location,null==t&&(t=i.protocol+"//"+i.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?i.protocol+t:i.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==i?i.protocol+"//"+t:"https://"+t),s=ti(t)),!s.port&&(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";let r=-1!==s.host.indexOf(":")?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+r+":"+s.port+e,s.href=s.protocol+"://"+r+(i&&i.port===s.port?"":":"+s.port),s}(t,(e=e||{}).path||"/socket.io"),r=s.source,n=s.id,o=s.path,a=tk[n]&&o in tk[n].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?i=new tA(r,e):(tk[n]||(tk[n]=new tA(r,e)),i=tk[n]),s.query&&!e.query&&(e.query=s.queryKey),i.socket(s.path,e)}Object.assign(tE,{Manager:tA,Socket:tP,io:tE,connect:tE})}}]);