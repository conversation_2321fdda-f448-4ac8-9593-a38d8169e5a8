/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socket.io-client";
exports.ids = ["vendor-chunks/socket.io-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/browser.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-env browser */ /**\n * This is the web browser implementation of `debug()`.\n */ exports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (()=>{\n    let warned = false;\n    return ()=>{\n        if (!warned) {\n            warned = true;\n            console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n        }\n    };\n})();\n/**\n * Colors.\n */ exports.colors = [\n    \"#0000CC\",\n    \"#0000FF\",\n    \"#0033CC\",\n    \"#0033FF\",\n    \"#0066CC\",\n    \"#0066FF\",\n    \"#0099CC\",\n    \"#0099FF\",\n    \"#00CC00\",\n    \"#00CC33\",\n    \"#00CC66\",\n    \"#00CC99\",\n    \"#00CCCC\",\n    \"#00CCFF\",\n    \"#3300CC\",\n    \"#3300FF\",\n    \"#3333CC\",\n    \"#3333FF\",\n    \"#3366CC\",\n    \"#3366FF\",\n    \"#3399CC\",\n    \"#3399FF\",\n    \"#33CC00\",\n    \"#33CC33\",\n    \"#33CC66\",\n    \"#33CC99\",\n    \"#33CCCC\",\n    \"#33CCFF\",\n    \"#6600CC\",\n    \"#6600FF\",\n    \"#6633CC\",\n    \"#6633FF\",\n    \"#66CC00\",\n    \"#66CC33\",\n    \"#9900CC\",\n    \"#9900FF\",\n    \"#9933CC\",\n    \"#9933FF\",\n    \"#99CC00\",\n    \"#99CC33\",\n    \"#CC0000\",\n    \"#CC0033\",\n    \"#CC0066\",\n    \"#CC0099\",\n    \"#CC00CC\",\n    \"#CC00FF\",\n    \"#CC3300\",\n    \"#CC3333\",\n    \"#CC3366\",\n    \"#CC3399\",\n    \"#CC33CC\",\n    \"#CC33FF\",\n    \"#CC6600\",\n    \"#CC6633\",\n    \"#CC9900\",\n    \"#CC9933\",\n    \"#CCCC00\",\n    \"#CCCC33\",\n    \"#FF0000\",\n    \"#FF0033\",\n    \"#FF0066\",\n    \"#FF0099\",\n    \"#FF00CC\",\n    \"#FF00FF\",\n    \"#FF3300\",\n    \"#FF3333\",\n    \"#FF3366\",\n    \"#FF3399\",\n    \"#FF33CC\",\n    \"#FF33FF\",\n    \"#FF6600\",\n    \"#FF6633\",\n    \"#FF9900\",\n    \"#FF9933\",\n    \"#FFCC00\",\n    \"#FFCC33\"\n];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */ // eslint-disable-next-line complexity\nfunction useColors() {\n    // NB: In an Electron preload script, document will be defined but not fully\n    // initialized. Since we know we're in Chrome, we'll just detect this case\n    // explicitly\n    if (false) {}\n    // Internet Explorer and Edge do not support colors.\n    if (typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n        return false;\n    }\n    let m;\n    // Is webkit? http://stackoverflow.com/a/16459606/376773\n    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n    return typeof document !== \"undefined\" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n     false && (0) || // Is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    typeof navigator !== \"undefined\" && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n    typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    args[0] = (this.useColors ? \"%c\" : \"\") + this.namespace + (this.useColors ? \" %c\" : \" \") + args[0] + (this.useColors ? \"%c \" : \" \") + \"+\" + module.exports.humanize(this.diff);\n    if (!this.useColors) {\n        return;\n    }\n    const c = \"color: \" + this.color;\n    args.splice(1, 0, c, \"color: inherit\");\n    // The final \"%c\" is somewhat tricky, because there could be other\n    // arguments passed either before or after the %c, so we need to\n    // figure out the correct index to insert the CSS into\n    let index = 0;\n    let lastC = 0;\n    args[0].replace(/%[a-zA-Z%]/g, (match)=>{\n        if (match === \"%%\") {\n            return;\n        }\n        index++;\n        if (match === \"%c\") {\n            // We only are interested in the *last* %c\n            // (the user may have provided their own)\n            lastC = index;\n        }\n    });\n    args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */ exports.log = console.debug || console.log || (()=>{});\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    try {\n        if (namespaces) {\n            exports.storage.setItem(\"debug\", namespaces);\n        } else {\n            exports.storage.removeItem(\"debug\");\n        }\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    let r;\n    try {\n        r = exports.storage.getItem(\"debug\");\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n    if (!r && typeof process !== \"undefined\" && \"env\" in process) {\n        r = process.env.DEBUG;\n    }\n    return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */ function localstorage() {\n    try {\n        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n        // The Browser also has localStorage in the global context.\n        return localStorage;\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */ formatters.j = function(v) {\n    try {\n        return JSON.stringify(v);\n    } catch (error) {\n        return \"[UnexpectedJSONParseError]: \" + error.message;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2Jyb3dzZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsc0JBQXNCLEdBRXRCOztDQUVDLEdBRURBLGtCQUFrQixHQUFHQztBQUNyQkQsWUFBWSxHQUFHRTtBQUNmRixZQUFZLEdBQUdHO0FBQ2ZILGlCQUFpQixHQUFHSTtBQUNwQkosZUFBZSxHQUFHTTtBQUNsQk4sZUFBZSxHQUFHLENBQUM7SUFDbEIsSUFBSVEsU0FBUztJQUViLE9BQU87UUFDTixJQUFJLENBQUNBLFFBQVE7WUFDWkEsU0FBUztZQUNUQyxRQUFRQyxJQUFJLENBQUM7UUFDZDtJQUNEO0FBQ0Q7QUFFQTs7Q0FFQyxHQUVEVixjQUFjLEdBQUc7SUFDaEI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDQTtBQUVEOzs7Ozs7Q0FNQyxHQUVELHNDQUFzQztBQUN0QyxTQUFTSTtJQUNSLDRFQUE0RTtJQUM1RSwwRUFBMEU7SUFDMUUsYUFBYTtJQUNiLElBQUksS0FBZ0gsRUFBRSxFQUVySDtJQUVELG9EQUFvRDtJQUNwRCxJQUFJLE9BQU9ZLGNBQWMsZUFBZUEsVUFBVUMsU0FBUyxJQUFJRCxVQUFVQyxTQUFTLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLDBCQUEwQjtRQUNoSSxPQUFPO0lBQ1I7SUFFQSxJQUFJQztJQUVKLHdEQUF3RDtJQUN4RCw0RkFBNEY7SUFDNUYsT0FBTyxPQUFRQyxhQUFhLGVBQWVBLFNBQVNDLGVBQWUsSUFBSUQsU0FBU0MsZUFBZSxDQUFDQyxLQUFLLElBQUlGLFNBQVNDLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDQyxnQkFBZ0IsSUFDdkosdURBQXVEO0lBQ3RELE1BQStDLElBQUtaLENBQUFBLENBQTJFLEtBQ2hJLHFCQUFxQjtJQUNyQiw4RUFBOEU7SUFDN0UsT0FBT0ksY0FBYyxlQUFlQSxVQUFVQyxTQUFTLElBQUtHLENBQUFBLElBQUlKLFVBQVVDLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsaUJBQWdCLEtBQU1TLFNBQVNSLENBQUMsQ0FBQyxFQUFFLEVBQUUsT0FBTyxNQUNySixtRUFBbUU7SUFDbEUsT0FBT0osY0FBYyxlQUFlQSxVQUFVQyxTQUFTLElBQUlELFVBQVVDLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxLQUFLLENBQUM7QUFDdEc7QUFFQTs7OztDQUlDLEdBRUQsU0FBU2xCLFdBQVc0QixJQUFJO0lBQ3ZCQSxJQUFJLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSSxDQUFDekIsU0FBUyxHQUFHLE9BQU8sRUFBQyxJQUNuQyxJQUFJLENBQUMwQixTQUFTLEdBQ2IsS0FBSSxDQUFDMUIsU0FBUyxHQUFHLFFBQVEsR0FBRSxJQUM1QnlCLElBQUksQ0FBQyxFQUFFLEdBQ04sS0FBSSxDQUFDekIsU0FBUyxHQUFHLFFBQVEsR0FBRSxJQUM1QixNQUFNMkIsT0FBTy9CLE9BQU8sQ0FBQ2dDLFFBQVEsQ0FBQyxJQUFJLENBQUNDLElBQUk7SUFFeEMsSUFBSSxDQUFDLElBQUksQ0FBQzdCLFNBQVMsRUFBRTtRQUNwQjtJQUNEO0lBRUEsTUFBTThCLElBQUksWUFBWSxJQUFJLENBQUNDLEtBQUs7SUFDaENOLEtBQUtPLE1BQU0sQ0FBQyxHQUFHLEdBQUdGLEdBQUc7SUFFckIsa0VBQWtFO0lBQ2xFLGdFQUFnRTtJQUNoRSxzREFBc0Q7SUFDdEQsSUFBSUcsUUFBUTtJQUNaLElBQUlDLFFBQVE7SUFDWlQsSUFBSSxDQUFDLEVBQUUsQ0FBQ1UsT0FBTyxDQUFDLGVBQWVwQixDQUFBQTtRQUM5QixJQUFJQSxVQUFVLE1BQU07WUFDbkI7UUFDRDtRQUNBa0I7UUFDQSxJQUFJbEIsVUFBVSxNQUFNO1lBQ25CLDBDQUEwQztZQUMxQyx5Q0FBeUM7WUFDekNtQixRQUFRRDtRQUNUO0lBQ0Q7SUFFQVIsS0FBS08sTUFBTSxDQUFDRSxPQUFPLEdBQUdKO0FBQ3ZCO0FBRUE7Ozs7Ozs7Q0FPQyxHQUNEbEMsV0FBVyxHQUFHUyxRQUFRZ0MsS0FBSyxJQUFJaEMsUUFBUStCLEdBQUcsSUFBSyxNQUFPO0FBRXREOzs7OztDQUtDLEdBQ0QsU0FBU3RDLEtBQUt3QyxVQUFVO0lBQ3ZCLElBQUk7UUFDSCxJQUFJQSxZQUFZO1lBQ2YxQyxRQUFRSyxPQUFPLENBQUNzQyxPQUFPLENBQUMsU0FBU0Q7UUFDbEMsT0FBTztZQUNOMUMsUUFBUUssT0FBTyxDQUFDdUMsVUFBVSxDQUFDO1FBQzVCO0lBQ0QsRUFBRSxPQUFPQyxPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztBQUNEO0FBRUE7Ozs7O0NBS0MsR0FDRCxTQUFTMUM7SUFDUixJQUFJMkM7SUFDSixJQUFJO1FBQ0hBLElBQUk5QyxRQUFRSyxPQUFPLENBQUMwQyxPQUFPLENBQUM7SUFDN0IsRUFBRSxPQUFPRixPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztJQUVBLHNFQUFzRTtJQUN0RSxJQUFJLENBQUNDLEtBQUssT0FBT2pDLFlBQVksZUFBZSxTQUFTQSxTQUFTO1FBQzdEaUMsSUFBSWpDLFFBQVFtQyxHQUFHLENBQUNDLEtBQUs7SUFDdEI7SUFFQSxPQUFPSDtBQUNSO0FBRUE7Ozs7Ozs7OztDQVNDLEdBRUQsU0FBU3hDO0lBQ1IsSUFBSTtRQUNILHVHQUF1RztRQUN2RywyREFBMkQ7UUFDM0QsT0FBTzRDO0lBQ1IsRUFBRSxPQUFPTCxPQUFPO0lBQ2YsVUFBVTtJQUNWLDBDQUEwQztJQUMzQztBQUNEO0FBRUFkLE9BQU8vQixPQUFPLEdBQUdtRCxtQkFBT0EsQ0FBQywwRkFBWW5EO0FBRXJDLE1BQU0sRUFBQ29ELFVBQVUsRUFBQyxHQUFHckIsT0FBTy9CLE9BQU87QUFFbkM7O0NBRUMsR0FFRG9ELFdBQVdDLENBQUMsR0FBRyxTQUFVQyxDQUFDO0lBQ3pCLElBQUk7UUFDSCxPQUFPQyxLQUFLQyxTQUFTLENBQUNGO0lBQ3ZCLEVBQUUsT0FBT1QsT0FBTztRQUNmLE9BQU8saUNBQWlDQSxNQUFNWSxPQUFPO0lBQ3REO0FBQ0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aGF0c2FwcC1ib3QtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2Jyb3dzZXIuanM/YzA2MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZW52IGJyb3dzZXIgKi9cblxuLyoqXG4gKiBUaGlzIGlzIHRoZSB3ZWIgYnJvd3NlciBpbXBsZW1lbnRhdGlvbiBvZiBgZGVidWcoKWAuXG4gKi9cblxuZXhwb3J0cy5mb3JtYXRBcmdzID0gZm9ybWF0QXJncztcbmV4cG9ydHMuc2F2ZSA9IHNhdmU7XG5leHBvcnRzLmxvYWQgPSBsb2FkO1xuZXhwb3J0cy51c2VDb2xvcnMgPSB1c2VDb2xvcnM7XG5leHBvcnRzLnN0b3JhZ2UgPSBsb2NhbHN0b3JhZ2UoKTtcbmV4cG9ydHMuZGVzdHJveSA9ICgoKSA9PiB7XG5cdGxldCB3YXJuZWQgPSBmYWxzZTtcblxuXHRyZXR1cm4gKCkgPT4ge1xuXHRcdGlmICghd2FybmVkKSB7XG5cdFx0XHR3YXJuZWQgPSB0cnVlO1xuXHRcdFx0Y29uc29sZS53YXJuKCdJbnN0YW5jZSBtZXRob2QgYGRlYnVnLmRlc3Ryb3koKWAgaXMgZGVwcmVjYXRlZCBhbmQgbm8gbG9uZ2VyIGRvZXMgYW55dGhpbmcuIEl0IHdpbGwgYmUgcmVtb3ZlZCBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uIG9mIGBkZWJ1Z2AuJyk7XG5cdFx0fVxuXHR9O1xufSkoKTtcblxuLyoqXG4gKiBDb2xvcnMuXG4gKi9cblxuZXhwb3J0cy5jb2xvcnMgPSBbXG5cdCcjMDAwMENDJyxcblx0JyMwMDAwRkYnLFxuXHQnIzAwMzNDQycsXG5cdCcjMDAzM0ZGJyxcblx0JyMwMDY2Q0MnLFxuXHQnIzAwNjZGRicsXG5cdCcjMDA5OUNDJyxcblx0JyMwMDk5RkYnLFxuXHQnIzAwQ0MwMCcsXG5cdCcjMDBDQzMzJyxcblx0JyMwMENDNjYnLFxuXHQnIzAwQ0M5OScsXG5cdCcjMDBDQ0NDJyxcblx0JyMwMENDRkYnLFxuXHQnIzMzMDBDQycsXG5cdCcjMzMwMEZGJyxcblx0JyMzMzMzQ0MnLFxuXHQnIzMzMzNGRicsXG5cdCcjMzM2NkNDJyxcblx0JyMzMzY2RkYnLFxuXHQnIzMzOTlDQycsXG5cdCcjMzM5OUZGJyxcblx0JyMzM0NDMDAnLFxuXHQnIzMzQ0MzMycsXG5cdCcjMzNDQzY2Jyxcblx0JyMzM0NDOTknLFxuXHQnIzMzQ0NDQycsXG5cdCcjMzNDQ0ZGJyxcblx0JyM2NjAwQ0MnLFxuXHQnIzY2MDBGRicsXG5cdCcjNjYzM0NDJyxcblx0JyM2NjMzRkYnLFxuXHQnIzY2Q0MwMCcsXG5cdCcjNjZDQzMzJyxcblx0JyM5OTAwQ0MnLFxuXHQnIzk5MDBGRicsXG5cdCcjOTkzM0NDJyxcblx0JyM5OTMzRkYnLFxuXHQnIzk5Q0MwMCcsXG5cdCcjOTlDQzMzJyxcblx0JyNDQzAwMDAnLFxuXHQnI0NDMDAzMycsXG5cdCcjQ0MwMDY2Jyxcblx0JyNDQzAwOTknLFxuXHQnI0NDMDBDQycsXG5cdCcjQ0MwMEZGJyxcblx0JyNDQzMzMDAnLFxuXHQnI0NDMzMzMycsXG5cdCcjQ0MzMzY2Jyxcblx0JyNDQzMzOTknLFxuXHQnI0NDMzNDQycsXG5cdCcjQ0MzM0ZGJyxcblx0JyNDQzY2MDAnLFxuXHQnI0NDNjYzMycsXG5cdCcjQ0M5OTAwJyxcblx0JyNDQzk5MzMnLFxuXHQnI0NDQ0MwMCcsXG5cdCcjQ0NDQzMzJyxcblx0JyNGRjAwMDAnLFxuXHQnI0ZGMDAzMycsXG5cdCcjRkYwMDY2Jyxcblx0JyNGRjAwOTknLFxuXHQnI0ZGMDBDQycsXG5cdCcjRkYwMEZGJyxcblx0JyNGRjMzMDAnLFxuXHQnI0ZGMzMzMycsXG5cdCcjRkYzMzY2Jyxcblx0JyNGRjMzOTknLFxuXHQnI0ZGMzNDQycsXG5cdCcjRkYzM0ZGJyxcblx0JyNGRjY2MDAnLFxuXHQnI0ZGNjYzMycsXG5cdCcjRkY5OTAwJyxcblx0JyNGRjk5MzMnLFxuXHQnI0ZGQ0MwMCcsXG5cdCcjRkZDQzMzJ1xuXTtcblxuLyoqXG4gKiBDdXJyZW50bHkgb25seSBXZWJLaXQtYmFzZWQgV2ViIEluc3BlY3RvcnMsIEZpcmVmb3ggPj0gdjMxLFxuICogYW5kIHRoZSBGaXJlYnVnIGV4dGVuc2lvbiAoYW55IEZpcmVmb3ggdmVyc2lvbikgYXJlIGtub3duXG4gKiB0byBzdXBwb3J0IFwiJWNcIiBDU1MgY3VzdG9taXphdGlvbnMuXG4gKlxuICogVE9ETzogYWRkIGEgYGxvY2FsU3RvcmFnZWAgdmFyaWFibGUgdG8gZXhwbGljaXRseSBlbmFibGUvZGlzYWJsZSBjb2xvcnNcbiAqL1xuXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgY29tcGxleGl0eVxuZnVuY3Rpb24gdXNlQ29sb3JzKCkge1xuXHQvLyBOQjogSW4gYW4gRWxlY3Ryb24gcHJlbG9hZCBzY3JpcHQsIGRvY3VtZW50IHdpbGwgYmUgZGVmaW5lZCBidXQgbm90IGZ1bGx5XG5cdC8vIGluaXRpYWxpemVkLiBTaW5jZSB3ZSBrbm93IHdlJ3JlIGluIENocm9tZSwgd2UnbGwganVzdCBkZXRlY3QgdGhpcyBjYXNlXG5cdC8vIGV4cGxpY2l0bHlcblx0aWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5wcm9jZXNzICYmICh3aW5kb3cucHJvY2Vzcy50eXBlID09PSAncmVuZGVyZXInIHx8IHdpbmRvdy5wcm9jZXNzLl9fbndqcykpIHtcblx0XHRyZXR1cm4gdHJ1ZTtcblx0fVxuXG5cdC8vIEludGVybmV0IEV4cGxvcmVyIGFuZCBFZGdlIGRvIG5vdCBzdXBwb3J0IGNvbG9ycy5cblx0aWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci51c2VyQWdlbnQgJiYgbmF2aWdhdG9yLnVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpLm1hdGNoKC8oZWRnZXx0cmlkZW50KVxcLyhcXGQrKS8pKSB7XG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cblx0bGV0IG07XG5cblx0Ly8gSXMgd2Via2l0PyBodHRwOi8vc3RhY2tvdmVyZmxvdy5jb20vYS8xNjQ1OTYwNi8zNzY3NzNcblx0Ly8gZG9jdW1lbnQgaXMgdW5kZWZpbmVkIGluIHJlYWN0LW5hdGl2ZTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0LW5hdGl2ZS9wdWxsLzE2MzJcblx0cmV0dXJuICh0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnICYmIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCAmJiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUgJiYgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLldlYmtpdEFwcGVhcmFuY2UpIHx8XG5cdFx0Ly8gSXMgZmlyZWJ1Zz8gaHR0cDovL3N0YWNrb3ZlcmZsb3cuY29tL2EvMzk4MTIwLzM3Njc3M1xuXHRcdCh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuY29uc29sZSAmJiAod2luZG93LmNvbnNvbGUuZmlyZWJ1ZyB8fCAod2luZG93LmNvbnNvbGUuZXhjZXB0aW9uICYmIHdpbmRvdy5jb25zb2xlLnRhYmxlKSkpIHx8XG5cdFx0Ly8gSXMgZmlyZWZveCA+PSB2MzE/XG5cdFx0Ly8gaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9Ub29scy9XZWJfQ29uc29sZSNTdHlsaW5nX21lc3NhZ2VzXG5cdFx0KHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci51c2VyQWdlbnQgJiYgKG0gPSBuYXZpZ2F0b3IudXNlckFnZW50LnRvTG93ZXJDYXNlKCkubWF0Y2goL2ZpcmVmb3hcXC8oXFxkKykvKSkgJiYgcGFyc2VJbnQobVsxXSwgMTApID49IDMxKSB8fFxuXHRcdC8vIERvdWJsZSBjaGVjayB3ZWJraXQgaW4gdXNlckFnZW50IGp1c3QgaW4gY2FzZSB3ZSBhcmUgaW4gYSB3b3JrZXJcblx0XHQodHlwZW9mIG5hdmlnYXRvciAhPT0gJ3VuZGVmaW5lZCcgJiYgbmF2aWdhdG9yLnVzZXJBZ2VudCAmJiBuYXZpZ2F0b3IudXNlckFnZW50LnRvTG93ZXJDYXNlKCkubWF0Y2goL2FwcGxld2Via2l0XFwvKFxcZCspLykpO1xufVxuXG4vKipcbiAqIENvbG9yaXplIGxvZyBhcmd1bWVudHMgaWYgZW5hYmxlZC5cbiAqXG4gKiBAYXBpIHB1YmxpY1xuICovXG5cbmZ1bmN0aW9uIGZvcm1hdEFyZ3MoYXJncykge1xuXHRhcmdzWzBdID0gKHRoaXMudXNlQ29sb3JzID8gJyVjJyA6ICcnKSArXG5cdFx0dGhpcy5uYW1lc3BhY2UgK1xuXHRcdCh0aGlzLnVzZUNvbG9ycyA/ICcgJWMnIDogJyAnKSArXG5cdFx0YXJnc1swXSArXG5cdFx0KHRoaXMudXNlQ29sb3JzID8gJyVjICcgOiAnICcpICtcblx0XHQnKycgKyBtb2R1bGUuZXhwb3J0cy5odW1hbml6ZSh0aGlzLmRpZmYpO1xuXG5cdGlmICghdGhpcy51c2VDb2xvcnMpIHtcblx0XHRyZXR1cm47XG5cdH1cblxuXHRjb25zdCBjID0gJ2NvbG9yOiAnICsgdGhpcy5jb2xvcjtcblx0YXJncy5zcGxpY2UoMSwgMCwgYywgJ2NvbG9yOiBpbmhlcml0Jyk7XG5cblx0Ly8gVGhlIGZpbmFsIFwiJWNcIiBpcyBzb21ld2hhdCB0cmlja3ksIGJlY2F1c2UgdGhlcmUgY291bGQgYmUgb3RoZXJcblx0Ly8gYXJndW1lbnRzIHBhc3NlZCBlaXRoZXIgYmVmb3JlIG9yIGFmdGVyIHRoZSAlYywgc28gd2UgbmVlZCB0b1xuXHQvLyBmaWd1cmUgb3V0IHRoZSBjb3JyZWN0IGluZGV4IHRvIGluc2VydCB0aGUgQ1NTIGludG9cblx0bGV0IGluZGV4ID0gMDtcblx0bGV0IGxhc3RDID0gMDtcblx0YXJnc1swXS5yZXBsYWNlKC8lW2EtekEtWiVdL2csIG1hdGNoID0+IHtcblx0XHRpZiAobWF0Y2ggPT09ICclJScpIHtcblx0XHRcdHJldHVybjtcblx0XHR9XG5cdFx0aW5kZXgrKztcblx0XHRpZiAobWF0Y2ggPT09ICclYycpIHtcblx0XHRcdC8vIFdlIG9ubHkgYXJlIGludGVyZXN0ZWQgaW4gdGhlICpsYXN0KiAlY1xuXHRcdFx0Ly8gKHRoZSB1c2VyIG1heSBoYXZlIHByb3ZpZGVkIHRoZWlyIG93bilcblx0XHRcdGxhc3RDID0gaW5kZXg7XG5cdFx0fVxuXHR9KTtcblxuXHRhcmdzLnNwbGljZShsYXN0QywgMCwgYyk7XG59XG5cbi8qKlxuICogSW52b2tlcyBgY29uc29sZS5kZWJ1ZygpYCB3aGVuIGF2YWlsYWJsZS5cbiAqIE5vLW9wIHdoZW4gYGNvbnNvbGUuZGVidWdgIGlzIG5vdCBhIFwiZnVuY3Rpb25cIi5cbiAqIElmIGBjb25zb2xlLmRlYnVnYCBpcyBub3QgYXZhaWxhYmxlLCBmYWxscyBiYWNrXG4gKiB0byBgY29uc29sZS5sb2dgLlxuICpcbiAqIEBhcGkgcHVibGljXG4gKi9cbmV4cG9ydHMubG9nID0gY29uc29sZS5kZWJ1ZyB8fCBjb25zb2xlLmxvZyB8fCAoKCkgPT4ge30pO1xuXG4vKipcbiAqIFNhdmUgYG5hbWVzcGFjZXNgLlxuICpcbiAqIEBwYXJhbSB7U3RyaW5nfSBuYW1lc3BhY2VzXG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuZnVuY3Rpb24gc2F2ZShuYW1lc3BhY2VzKSB7XG5cdHRyeSB7XG5cdFx0aWYgKG5hbWVzcGFjZXMpIHtcblx0XHRcdGV4cG9ydHMuc3RvcmFnZS5zZXRJdGVtKCdkZWJ1ZycsIG5hbWVzcGFjZXMpO1xuXHRcdH0gZWxzZSB7XG5cdFx0XHRleHBvcnRzLnN0b3JhZ2UucmVtb3ZlSXRlbSgnZGVidWcnKTtcblx0XHR9XG5cdH0gY2F0Y2ggKGVycm9yKSB7XG5cdFx0Ly8gU3dhbGxvd1xuXHRcdC8vIFhYWCAoQFFpeC0pIHNob3VsZCB3ZSBiZSBsb2dnaW5nIHRoZXNlP1xuXHR9XG59XG5cbi8qKlxuICogTG9hZCBgbmFtZXNwYWNlc2AuXG4gKlxuICogQHJldHVybiB7U3RyaW5nfSByZXR1cm5zIHRoZSBwcmV2aW91c2x5IHBlcnNpc3RlZCBkZWJ1ZyBtb2Rlc1xuICogQGFwaSBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIGxvYWQoKSB7XG5cdGxldCByO1xuXHR0cnkge1xuXHRcdHIgPSBleHBvcnRzLnN0b3JhZ2UuZ2V0SXRlbSgnZGVidWcnKTtcblx0fSBjYXRjaCAoZXJyb3IpIHtcblx0XHQvLyBTd2FsbG93XG5cdFx0Ly8gWFhYIChAUWl4LSkgc2hvdWxkIHdlIGJlIGxvZ2dpbmcgdGhlc2U/XG5cdH1cblxuXHQvLyBJZiBkZWJ1ZyBpc24ndCBzZXQgaW4gTFMsIGFuZCB3ZSdyZSBpbiBFbGVjdHJvbiwgdHJ5IHRvIGxvYWQgJERFQlVHXG5cdGlmICghciAmJiB0eXBlb2YgcHJvY2VzcyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ2VudicgaW4gcHJvY2Vzcykge1xuXHRcdHIgPSBwcm9jZXNzLmVudi5ERUJVRztcblx0fVxuXG5cdHJldHVybiByO1xufVxuXG4vKipcbiAqIExvY2Fsc3RvcmFnZSBhdHRlbXB0cyB0byByZXR1cm4gdGhlIGxvY2Fsc3RvcmFnZS5cbiAqXG4gKiBUaGlzIGlzIG5lY2Vzc2FyeSBiZWNhdXNlIHNhZmFyaSB0aHJvd3NcbiAqIHdoZW4gYSB1c2VyIGRpc2FibGVzIGNvb2tpZXMvbG9jYWxzdG9yYWdlXG4gKiBhbmQgeW91IGF0dGVtcHQgdG8gYWNjZXNzIGl0LlxuICpcbiAqIEByZXR1cm4ge0xvY2FsU3RvcmFnZX1cbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5cbmZ1bmN0aW9uIGxvY2Fsc3RvcmFnZSgpIHtcblx0dHJ5IHtcblx0XHQvLyBUVk1MS2l0IChBcHBsZSBUViBKUyBSdW50aW1lKSBkb2VzIG5vdCBoYXZlIGEgd2luZG93IG9iamVjdCwganVzdCBsb2NhbFN0b3JhZ2UgaW4gdGhlIGdsb2JhbCBjb250ZXh0XG5cdFx0Ly8gVGhlIEJyb3dzZXIgYWxzbyBoYXMgbG9jYWxTdG9yYWdlIGluIHRoZSBnbG9iYWwgY29udGV4dC5cblx0XHRyZXR1cm4gbG9jYWxTdG9yYWdlO1xuXHR9IGNhdGNoIChlcnJvcikge1xuXHRcdC8vIFN3YWxsb3dcblx0XHQvLyBYWFggKEBRaXgtKSBzaG91bGQgd2UgYmUgbG9nZ2luZyB0aGVzZT9cblx0fVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY29tbW9uJykoZXhwb3J0cyk7XG5cbmNvbnN0IHtmb3JtYXR0ZXJzfSA9IG1vZHVsZS5leHBvcnRzO1xuXG4vKipcbiAqIE1hcCAlaiB0byBgSlNPTi5zdHJpbmdpZnkoKWAsIHNpbmNlIG5vIFdlYiBJbnNwZWN0b3JzIGRvIHRoYXQgYnkgZGVmYXVsdC5cbiAqL1xuXG5mb3JtYXR0ZXJzLmogPSBmdW5jdGlvbiAodikge1xuXHR0cnkge1xuXHRcdHJldHVybiBKU09OLnN0cmluZ2lmeSh2KTtcblx0fSBjYXRjaCAoZXJyb3IpIHtcblx0XHRyZXR1cm4gJ1tVbmV4cGVjdGVkSlNPTlBhcnNlRXJyb3JdOiAnICsgZXJyb3IubWVzc2FnZTtcblx0fVxufTtcbiJdLCJuYW1lcyI6WyJleHBvcnRzIiwiZm9ybWF0QXJncyIsInNhdmUiLCJsb2FkIiwidXNlQ29sb3JzIiwic3RvcmFnZSIsImxvY2Fsc3RvcmFnZSIsImRlc3Ryb3kiLCJ3YXJuZWQiLCJjb25zb2xlIiwid2FybiIsImNvbG9ycyIsIndpbmRvdyIsInByb2Nlc3MiLCJ0eXBlIiwiX19ud2pzIiwibmF2aWdhdG9yIiwidXNlckFnZW50IiwidG9Mb3dlckNhc2UiLCJtYXRjaCIsIm0iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsInN0eWxlIiwiV2Via2l0QXBwZWFyYW5jZSIsImZpcmVidWciLCJleGNlcHRpb24iLCJ0YWJsZSIsInBhcnNlSW50IiwiYXJncyIsIm5hbWVzcGFjZSIsIm1vZHVsZSIsImh1bWFuaXplIiwiZGlmZiIsImMiLCJjb2xvciIsInNwbGljZSIsImluZGV4IiwibGFzdEMiLCJyZXBsYWNlIiwibG9nIiwiZGVidWciLCJuYW1lc3BhY2VzIiwic2V0SXRlbSIsInJlbW92ZUl0ZW0iLCJlcnJvciIsInIiLCJnZXRJdGVtIiwiZW52IiwiREVCVUciLCJsb2NhbFN0b3JhZ2UiLCJyZXF1aXJlIiwiZm9ybWF0dGVycyIsImoiLCJ2IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js":
/*!************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/common.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */ function setup(env) {\n    createDebug.debug = createDebug;\n    createDebug.default = createDebug;\n    createDebug.coerce = coerce;\n    createDebug.disable = disable;\n    createDebug.enable = enable;\n    createDebug.enabled = enabled;\n    createDebug.humanize = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n    createDebug.destroy = destroy;\n    Object.keys(env).forEach((key)=>{\n        createDebug[key] = env[key];\n    });\n    /**\n\t* The currently active debug mode names, and names to skip.\n\t*/ createDebug.names = [];\n    createDebug.skips = [];\n    /**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/ createDebug.formatters = {};\n    /**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/ function selectColor(namespace) {\n        let hash = 0;\n        for(let i = 0; i < namespace.length; i++){\n            hash = (hash << 5) - hash + namespace.charCodeAt(i);\n            hash |= 0; // Convert to 32bit integer\n        }\n        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n    }\n    createDebug.selectColor = selectColor;\n    /**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/ function createDebug(namespace) {\n        let prevTime;\n        let enableOverride = null;\n        let namespacesCache;\n        let enabledCache;\n        function debug(...args) {\n            // Disabled?\n            if (!debug.enabled) {\n                return;\n            }\n            const self = debug;\n            // Set `diff` timestamp\n            const curr = Number(new Date());\n            const ms = curr - (prevTime || curr);\n            self.diff = ms;\n            self.prev = prevTime;\n            self.curr = curr;\n            prevTime = curr;\n            args[0] = createDebug.coerce(args[0]);\n            if (typeof args[0] !== \"string\") {\n                // Anything else let's inspect with %O\n                args.unshift(\"%O\");\n            }\n            // Apply any `formatters` transformations\n            let index = 0;\n            args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format)=>{\n                // If we encounter an escaped % then don't increase the array index\n                if (match === \"%%\") {\n                    return \"%\";\n                }\n                index++;\n                const formatter = createDebug.formatters[format];\n                if (typeof formatter === \"function\") {\n                    const val = args[index];\n                    match = formatter.call(self, val);\n                    // Now we need to remove `args[index]` since it's inlined in the `format`\n                    args.splice(index, 1);\n                    index--;\n                }\n                return match;\n            });\n            // Apply env-specific formatting (colors, etc.)\n            createDebug.formatArgs.call(self, args);\n            const logFn = self.log || createDebug.log;\n            logFn.apply(self, args);\n        }\n        debug.namespace = namespace;\n        debug.useColors = createDebug.useColors();\n        debug.color = createDebug.selectColor(namespace);\n        debug.extend = extend;\n        debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n        Object.defineProperty(debug, \"enabled\", {\n            enumerable: true,\n            configurable: false,\n            get: ()=>{\n                if (enableOverride !== null) {\n                    return enableOverride;\n                }\n                if (namespacesCache !== createDebug.namespaces) {\n                    namespacesCache = createDebug.namespaces;\n                    enabledCache = createDebug.enabled(namespace);\n                }\n                return enabledCache;\n            },\n            set: (v)=>{\n                enableOverride = v;\n            }\n        });\n        // Env-specific initialization logic for debug instances\n        if (typeof createDebug.init === \"function\") {\n            createDebug.init(debug);\n        }\n        return debug;\n    }\n    function extend(namespace, delimiter) {\n        const newDebug = createDebug(this.namespace + (typeof delimiter === \"undefined\" ? \":\" : delimiter) + namespace);\n        newDebug.log = this.log;\n        return newDebug;\n    }\n    /**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/ function enable(namespaces) {\n        createDebug.save(namespaces);\n        createDebug.namespaces = namespaces;\n        createDebug.names = [];\n        createDebug.skips = [];\n        let i;\n        const split = (typeof namespaces === \"string\" ? namespaces : \"\").split(/[\\s,]+/);\n        const len = split.length;\n        for(i = 0; i < len; i++){\n            if (!split[i]) {\n                continue;\n            }\n            namespaces = split[i].replace(/\\*/g, \".*?\");\n            if (namespaces[0] === \"-\") {\n                createDebug.skips.push(new RegExp(\"^\" + namespaces.slice(1) + \"$\"));\n            } else {\n                createDebug.names.push(new RegExp(\"^\" + namespaces + \"$\"));\n            }\n        }\n    }\n    /**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/ function disable() {\n        const namespaces = [\n            ...createDebug.names.map(toNamespace),\n            ...createDebug.skips.map(toNamespace).map((namespace)=>\"-\" + namespace)\n        ].join(\",\");\n        createDebug.enable(\"\");\n        return namespaces;\n    }\n    /**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/ function enabled(name) {\n        if (name[name.length - 1] === \"*\") {\n            return true;\n        }\n        let i;\n        let len;\n        for(i = 0, len = createDebug.skips.length; i < len; i++){\n            if (createDebug.skips[i].test(name)) {\n                return false;\n            }\n        }\n        for(i = 0, len = createDebug.names.length; i < len; i++){\n            if (createDebug.names[i].test(name)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/ function toNamespace(regexp) {\n        return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, \"*\");\n    }\n    /**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/ function coerce(val) {\n        if (val instanceof Error) {\n            return val.stack || val.message;\n        }\n        return val;\n    }\n    /**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/ function destroy() {\n        console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n    }\n    createDebug.enable(createDebug.load());\n    return createDebug;\n}\nmodule.exports = setup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/index.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */ if (typeof process === \"undefined\" || process.type === \"renderer\" || false === true || process.__nwjs) {\n    module.exports = __webpack_require__(/*! ./browser.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\");\n} else {\n    module.exports = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUVELElBQUksT0FBT0EsWUFBWSxlQUFlQSxRQUFRQyxJQUFJLEtBQUssY0FBY0QsS0FBZSxLQUFLLFFBQVFBLFFBQVFHLE1BQU0sRUFBRTtJQUNoSEMsbUlBQXlCO0FBQzFCLE9BQU87SUFDTkEsNkhBQXlCO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2hhdHNhcHAtYm90LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1jbGllbnQvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9pbmRleC5qcz8wODJmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGV0ZWN0IEVsZWN0cm9uIHJlbmRlcmVyIC8gbndqcyBwcm9jZXNzLCB3aGljaCBpcyBub2RlLCBidXQgd2Ugc2hvdWxkXG4gKiB0cmVhdCBhcyBhIGJyb3dzZXIuXG4gKi9cblxuaWYgKHR5cGVvZiBwcm9jZXNzID09PSAndW5kZWZpbmVkJyB8fCBwcm9jZXNzLnR5cGUgPT09ICdyZW5kZXJlcicgfHwgcHJvY2Vzcy5icm93c2VyID09PSB0cnVlIHx8IHByb2Nlc3MuX19ud2pzKSB7XG5cdG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9icm93c2VyLmpzJyk7XG59IGVsc2Uge1xuXHRtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbm9kZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJ0eXBlIiwiYnJvd3NlciIsIl9fbndqcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js":
/*!**********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/node.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */ const tty = __webpack_require__(/*! tty */ \"tty\");\nconst util = __webpack_require__(/*! util */ \"util\");\n/**\n * This is the Node.js implementation of `debug()`.\n */ exports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(()=>{}, \"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n/**\n * Colors.\n */ exports.colors = [\n    6,\n    2,\n    3,\n    4,\n    5,\n    1\n];\ntry {\n    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const supportsColor = __webpack_require__(/*! supports-color */ \"(ssr)/./node_modules/supports-color/index.js\");\n    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n        exports.colors = [\n            20,\n            21,\n            26,\n            27,\n            32,\n            33,\n            38,\n            39,\n            40,\n            41,\n            42,\n            43,\n            44,\n            45,\n            56,\n            57,\n            62,\n            63,\n            68,\n            69,\n            74,\n            75,\n            76,\n            77,\n            78,\n            79,\n            80,\n            81,\n            92,\n            93,\n            98,\n            99,\n            112,\n            113,\n            128,\n            129,\n            134,\n            135,\n            148,\n            149,\n            160,\n            161,\n            162,\n            163,\n            164,\n            165,\n            166,\n            167,\n            168,\n            169,\n            170,\n            171,\n            172,\n            173,\n            178,\n            179,\n            184,\n            185,\n            196,\n            197,\n            198,\n            199,\n            200,\n            201,\n            202,\n            203,\n            204,\n            205,\n            206,\n            207,\n            208,\n            209,\n            214,\n            215,\n            220,\n            221\n        ];\n    }\n} catch (error) {\n// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */ exports.inspectOpts = Object.keys(process.env).filter((key)=>{\n    return /^debug_/i.test(key);\n}).reduce((obj, key)=>{\n    // Camel-case\n    const prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, (_, k)=>{\n        return k.toUpperCase();\n    });\n    // Coerce string value into JS value\n    let val = process.env[key];\n    if (/^(yes|on|true|enabled)$/i.test(val)) {\n        val = true;\n    } else if (/^(no|off|false|disabled)$/i.test(val)) {\n        val = false;\n    } else if (val === \"null\") {\n        val = null;\n    } else {\n        val = Number(val);\n    }\n    obj[prop] = val;\n    return obj;\n}, {});\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */ function useColors() {\n    return \"colors\" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);\n}\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    const { namespace: name, useColors } = this;\n    if (useColors) {\n        const c = this.color;\n        const colorCode = \"\\x1b[3\" + (c < 8 ? c : \"8;5;\" + c);\n        const prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n        args[0] = prefix + args[0].split(\"\\n\").join(\"\\n\" + prefix);\n        args.push(colorCode + \"m+\" + module.exports.humanize(this.diff) + \"\\x1b[0m\");\n    } else {\n        args[0] = getDate() + name + \" \" + args[0];\n    }\n}\nfunction getDate() {\n    if (exports.inspectOpts.hideDate) {\n        return \"\";\n    }\n    return new Date().toISOString() + \" \";\n}\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */ function log(...args) {\n    return process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + \"\\n\");\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    if (namespaces) {\n        process.env.DEBUG = namespaces;\n    } else {\n        // If you set a process.env field to null or undefined, it gets cast to the\n        // string 'null' or 'undefined'. Just delete instead.\n        delete process.env.DEBUG;\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    return process.env.DEBUG;\n}\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */ function init(debug) {\n    debug.inspectOpts = {};\n    const keys = Object.keys(exports.inspectOpts);\n    for(let i = 0; i < keys.length; i++){\n        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */ formatters.o = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts).split(\"\\n\").map((str)=>str.trim()).join(\" \");\n};\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */ formatters.O = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL25vZGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0NBRUMsR0FFRCxNQUFNQSxNQUFNQyxtQkFBT0EsQ0FBQztBQUNwQixNQUFNQyxPQUFPRCxtQkFBT0EsQ0FBQztBQUVyQjs7Q0FFQyxHQUVERSxZQUFZLEdBQUdDO0FBQ2ZELFdBQVcsR0FBR0U7QUFDZEYsa0JBQWtCLEdBQUdHO0FBQ3JCSCxZQUFZLEdBQUdJO0FBQ2ZKLFlBQVksR0FBR0s7QUFDZkwsaUJBQWlCLEdBQUdNO0FBQ3BCTixlQUFlLEdBQUdELEtBQUtTLFNBQVMsQ0FDL0IsS0FBTyxHQUNQO0FBR0Q7O0NBRUMsR0FFRFIsY0FBYyxHQUFHO0lBQUM7SUFBRztJQUFHO0lBQUc7SUFBRztJQUFHO0NBQUU7QUFFbkMsSUFBSTtJQUNILDJHQUEyRztJQUMzRyw2REFBNkQ7SUFDN0QsTUFBTVUsZ0JBQWdCWixtQkFBT0EsQ0FBQztJQUU5QixJQUFJWSxpQkFBaUIsQ0FBQ0EsY0FBY0MsTUFBTSxJQUFJRCxhQUFZLEVBQUdFLEtBQUssSUFBSSxHQUFHO1FBQ3hFWixjQUFjLEdBQUc7WUFDaEI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDQTtJQUNGO0FBQ0QsRUFBRSxPQUFPYSxPQUFPO0FBQ2Ysa0ZBQWtGO0FBQ25GO0FBRUE7Ozs7Q0FJQyxHQUVEYixtQkFBbUIsR0FBR2UsT0FBT0MsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLEVBQUVDLE1BQU0sQ0FBQ0MsQ0FBQUE7SUFDckQsT0FBTyxXQUFXQyxJQUFJLENBQUNEO0FBQ3hCLEdBQUdFLE1BQU0sQ0FBQyxDQUFDQyxLQUFLSDtJQUNmLGFBQWE7SUFDYixNQUFNSSxPQUFPSixJQUNYSyxTQUFTLENBQUMsR0FDVkMsV0FBVyxHQUNYQyxPQUFPLENBQUMsYUFBYSxDQUFDQyxHQUFHQztRQUN6QixPQUFPQSxFQUFFQyxXQUFXO0lBQ3JCO0lBRUQsb0NBQW9DO0lBQ3BDLElBQUlDLE1BQU1kLFFBQVFDLEdBQUcsQ0FBQ0UsSUFBSTtJQUMxQixJQUFJLDJCQUEyQkMsSUFBSSxDQUFDVSxNQUFNO1FBQ3pDQSxNQUFNO0lBQ1AsT0FBTyxJQUFJLDZCQUE2QlYsSUFBSSxDQUFDVSxNQUFNO1FBQ2xEQSxNQUFNO0lBQ1AsT0FBTyxJQUFJQSxRQUFRLFFBQVE7UUFDMUJBLE1BQU07SUFDUCxPQUFPO1FBQ05BLE1BQU1DLE9BQU9EO0lBQ2Q7SUFFQVIsR0FBRyxDQUFDQyxLQUFLLEdBQUdPO0lBQ1osT0FBT1I7QUFDUixHQUFHLENBQUM7QUFFSjs7Q0FFQyxHQUVELFNBQVNqQjtJQUNSLE9BQU8sWUFBWU4sUUFBUWMsV0FBVyxHQUNyQ21CLFFBQVFqQyxRQUFRYyxXQUFXLENBQUNMLE1BQU0sSUFDbENaLElBQUlxQyxNQUFNLENBQUNqQixRQUFRTixNQUFNLENBQUN3QixFQUFFO0FBQzlCO0FBRUE7Ozs7Q0FJQyxHQUVELFNBQVNoQyxXQUFXaUMsSUFBSTtJQUN2QixNQUFNLEVBQUNDLFdBQVdDLElBQUksRUFBRWhDLFNBQVMsRUFBQyxHQUFHLElBQUk7SUFFekMsSUFBSUEsV0FBVztRQUNkLE1BQU1pQyxJQUFJLElBQUksQ0FBQ0MsS0FBSztRQUNwQixNQUFNQyxZQUFZLFdBQWNGLENBQUFBLElBQUksSUFBSUEsSUFBSSxTQUFTQSxDQUFBQTtRQUNyRCxNQUFNRyxTQUFTLENBQUMsRUFBRSxFQUFFRCxVQUFVLEdBQUcsRUFBRUgsS0FBSyxVQUFVLENBQUM7UUFFbkRGLElBQUksQ0FBQyxFQUFFLEdBQUdNLFNBQVNOLElBQUksQ0FBQyxFQUFFLENBQUNPLEtBQUssQ0FBQyxNQUFNQyxJQUFJLENBQUMsT0FBT0Y7UUFDbkROLEtBQUtTLElBQUksQ0FBQ0osWUFBWSxPQUFPSyxPQUFPOUMsT0FBTyxDQUFDK0MsUUFBUSxDQUFDLElBQUksQ0FBQ0MsSUFBSSxJQUFJO0lBQ25FLE9BQU87UUFDTlosSUFBSSxDQUFDLEVBQUUsR0FBR2EsWUFBWVgsT0FBTyxNQUFNRixJQUFJLENBQUMsRUFBRTtJQUMzQztBQUNEO0FBRUEsU0FBU2E7SUFDUixJQUFJakQsUUFBUWMsV0FBVyxDQUFDb0MsUUFBUSxFQUFFO1FBQ2pDLE9BQU87SUFDUjtJQUNBLE9BQU8sSUFBSUMsT0FBT0MsV0FBVyxLQUFLO0FBQ25DO0FBRUE7O0NBRUMsR0FFRCxTQUFTbEQsSUFBSSxHQUFHa0MsSUFBSTtJQUNuQixPQUFPbkIsUUFBUU4sTUFBTSxDQUFDMEMsS0FBSyxDQUFDdEQsS0FBS3VELGlCQUFpQixDQUFDdEQsUUFBUWMsV0FBVyxLQUFLc0IsUUFBUTtBQUNwRjtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBU2hDLEtBQUttRCxVQUFVO0lBQ3ZCLElBQUlBLFlBQVk7UUFDZnRDLFFBQVFDLEdBQUcsQ0FBQ3NDLEtBQUssR0FBR0Q7SUFDckIsT0FBTztRQUNOLDJFQUEyRTtRQUMzRSxxREFBcUQ7UUFDckQsT0FBT3RDLFFBQVFDLEdBQUcsQ0FBQ3NDLEtBQUs7SUFDekI7QUFDRDtBQUVBOzs7OztDQUtDLEdBRUQsU0FBU25EO0lBQ1IsT0FBT1ksUUFBUUMsR0FBRyxDQUFDc0MsS0FBSztBQUN6QjtBQUVBOzs7OztDQUtDLEdBRUQsU0FBU3ZELEtBQUt3RCxLQUFLO0lBQ2xCQSxNQUFNM0MsV0FBVyxHQUFHLENBQUM7SUFFckIsTUFBTUUsT0FBT0QsT0FBT0MsSUFBSSxDQUFDaEIsUUFBUWMsV0FBVztJQUM1QyxJQUFLLElBQUk0QyxJQUFJLEdBQUdBLElBQUkxQyxLQUFLMkMsTUFBTSxFQUFFRCxJQUFLO1FBQ3JDRCxNQUFNM0MsV0FBVyxDQUFDRSxJQUFJLENBQUMwQyxFQUFFLENBQUMsR0FBRzFELFFBQVFjLFdBQVcsQ0FBQ0UsSUFBSSxDQUFDMEMsRUFBRSxDQUFDO0lBQzFEO0FBQ0Q7QUFFQVosT0FBTzlDLE9BQU8sR0FBR0YsbUJBQU9BLENBQUMsMEZBQVlFO0FBRXJDLE1BQU0sRUFBQzRELFVBQVUsRUFBQyxHQUFHZCxPQUFPOUMsT0FBTztBQUVuQzs7Q0FFQyxHQUVENEQsV0FBV0MsQ0FBQyxHQUFHLFNBQVVDLENBQUM7SUFDekIsSUFBSSxDQUFDaEQsV0FBVyxDQUFDTCxNQUFNLEdBQUcsSUFBSSxDQUFDSCxTQUFTO0lBQ3hDLE9BQU9QLEtBQUtnRSxPQUFPLENBQUNELEdBQUcsSUFBSSxDQUFDaEQsV0FBVyxFQUNyQzZCLEtBQUssQ0FBQyxNQUNOcUIsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJLElBQ25CdEIsSUFBSSxDQUFDO0FBQ1I7QUFFQTs7Q0FFQyxHQUVEZ0IsV0FBV08sQ0FBQyxHQUFHLFNBQVVMLENBQUM7SUFDekIsSUFBSSxDQUFDaEQsV0FBVyxDQUFDTCxNQUFNLEdBQUcsSUFBSSxDQUFDSCxTQUFTO0lBQ3hDLE9BQU9QLEtBQUtnRSxPQUFPLENBQUNELEdBQUcsSUFBSSxDQUFDaEQsV0FBVztBQUN4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zb2NrZXQuaW8tY2xpZW50L25vZGVfbW9kdWxlcy9kZWJ1Zy9zcmMvbm9kZS5qcz82ODg2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTW9kdWxlIGRlcGVuZGVuY2llcy5cbiAqL1xuXG5jb25zdCB0dHkgPSByZXF1aXJlKCd0dHknKTtcbmNvbnN0IHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5cbi8qKlxuICogVGhpcyBpcyB0aGUgTm9kZS5qcyBpbXBsZW1lbnRhdGlvbiBvZiBgZGVidWcoKWAuXG4gKi9cblxuZXhwb3J0cy5pbml0ID0gaW5pdDtcbmV4cG9ydHMubG9nID0gbG9nO1xuZXhwb3J0cy5mb3JtYXRBcmdzID0gZm9ybWF0QXJncztcbmV4cG9ydHMuc2F2ZSA9IHNhdmU7XG5leHBvcnRzLmxvYWQgPSBsb2FkO1xuZXhwb3J0cy51c2VDb2xvcnMgPSB1c2VDb2xvcnM7XG5leHBvcnRzLmRlc3Ryb3kgPSB1dGlsLmRlcHJlY2F0ZShcblx0KCkgPT4ge30sXG5cdCdJbnN0YW5jZSBtZXRob2QgYGRlYnVnLmRlc3Ryb3koKWAgaXMgZGVwcmVjYXRlZCBhbmQgbm8gbG9uZ2VyIGRvZXMgYW55dGhpbmcuIEl0IHdpbGwgYmUgcmVtb3ZlZCBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uIG9mIGBkZWJ1Z2AuJ1xuKTtcblxuLyoqXG4gKiBDb2xvcnMuXG4gKi9cblxuZXhwb3J0cy5jb2xvcnMgPSBbNiwgMiwgMywgNCwgNSwgMV07XG5cbnRyeSB7XG5cdC8vIE9wdGlvbmFsIGRlcGVuZGVuY3kgKGFzIGluLCBkb2Vzbid0IG5lZWQgdG8gYmUgaW5zdGFsbGVkLCBOT1QgbGlrZSBvcHRpb25hbERlcGVuZGVuY2llcyBpbiBwYWNrYWdlLmpzb24pXG5cdC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBpbXBvcnQvbm8tZXh0cmFuZW91cy1kZXBlbmRlbmNpZXNcblx0Y29uc3Qgc3VwcG9ydHNDb2xvciA9IHJlcXVpcmUoJ3N1cHBvcnRzLWNvbG9yJyk7XG5cblx0aWYgKHN1cHBvcnRzQ29sb3IgJiYgKHN1cHBvcnRzQ29sb3Iuc3RkZXJyIHx8IHN1cHBvcnRzQ29sb3IpLmxldmVsID49IDIpIHtcblx0XHRleHBvcnRzLmNvbG9ycyA9IFtcblx0XHRcdDIwLFxuXHRcdFx0MjEsXG5cdFx0XHQyNixcblx0XHRcdDI3LFxuXHRcdFx0MzIsXG5cdFx0XHQzMyxcblx0XHRcdDM4LFxuXHRcdFx0MzksXG5cdFx0XHQ0MCxcblx0XHRcdDQxLFxuXHRcdFx0NDIsXG5cdFx0XHQ0Myxcblx0XHRcdDQ0LFxuXHRcdFx0NDUsXG5cdFx0XHQ1Nixcblx0XHRcdDU3LFxuXHRcdFx0NjIsXG5cdFx0XHQ2Myxcblx0XHRcdDY4LFxuXHRcdFx0NjksXG5cdFx0XHQ3NCxcblx0XHRcdDc1LFxuXHRcdFx0NzYsXG5cdFx0XHQ3Nyxcblx0XHRcdDc4LFxuXHRcdFx0NzksXG5cdFx0XHQ4MCxcblx0XHRcdDgxLFxuXHRcdFx0OTIsXG5cdFx0XHQ5Myxcblx0XHRcdDk4LFxuXHRcdFx0OTksXG5cdFx0XHQxMTIsXG5cdFx0XHQxMTMsXG5cdFx0XHQxMjgsXG5cdFx0XHQxMjksXG5cdFx0XHQxMzQsXG5cdFx0XHQxMzUsXG5cdFx0XHQxNDgsXG5cdFx0XHQxNDksXG5cdFx0XHQxNjAsXG5cdFx0XHQxNjEsXG5cdFx0XHQxNjIsXG5cdFx0XHQxNjMsXG5cdFx0XHQxNjQsXG5cdFx0XHQxNjUsXG5cdFx0XHQxNjYsXG5cdFx0XHQxNjcsXG5cdFx0XHQxNjgsXG5cdFx0XHQxNjksXG5cdFx0XHQxNzAsXG5cdFx0XHQxNzEsXG5cdFx0XHQxNzIsXG5cdFx0XHQxNzMsXG5cdFx0XHQxNzgsXG5cdFx0XHQxNzksXG5cdFx0XHQxODQsXG5cdFx0XHQxODUsXG5cdFx0XHQxOTYsXG5cdFx0XHQxOTcsXG5cdFx0XHQxOTgsXG5cdFx0XHQxOTksXG5cdFx0XHQyMDAsXG5cdFx0XHQyMDEsXG5cdFx0XHQyMDIsXG5cdFx0XHQyMDMsXG5cdFx0XHQyMDQsXG5cdFx0XHQyMDUsXG5cdFx0XHQyMDYsXG5cdFx0XHQyMDcsXG5cdFx0XHQyMDgsXG5cdFx0XHQyMDksXG5cdFx0XHQyMTQsXG5cdFx0XHQyMTUsXG5cdFx0XHQyMjAsXG5cdFx0XHQyMjFcblx0XHRdO1xuXHR9XG59IGNhdGNoIChlcnJvcikge1xuXHQvLyBTd2FsbG93IC0gd2Ugb25seSBjYXJlIGlmIGBzdXBwb3J0cy1jb2xvcmAgaXMgYXZhaWxhYmxlOyBpdCBkb2Vzbid0IGhhdmUgdG8gYmUuXG59XG5cbi8qKlxuICogQnVpbGQgdXAgdGhlIGRlZmF1bHQgYGluc3BlY3RPcHRzYCBvYmplY3QgZnJvbSB0aGUgZW52aXJvbm1lbnQgdmFyaWFibGVzLlxuICpcbiAqICAgJCBERUJVR19DT0xPUlM9bm8gREVCVUdfREVQVEg9MTAgREVCVUdfU0hPV19ISURERU49ZW5hYmxlZCBub2RlIHNjcmlwdC5qc1xuICovXG5cbmV4cG9ydHMuaW5zcGVjdE9wdHMgPSBPYmplY3Qua2V5cyhwcm9jZXNzLmVudikuZmlsdGVyKGtleSA9PiB7XG5cdHJldHVybiAvXmRlYnVnXy9pLnRlc3Qoa2V5KTtcbn0pLnJlZHVjZSgob2JqLCBrZXkpID0+IHtcblx0Ly8gQ2FtZWwtY2FzZVxuXHRjb25zdCBwcm9wID0ga2V5XG5cdFx0LnN1YnN0cmluZyg2KVxuXHRcdC50b0xvd2VyQ2FzZSgpXG5cdFx0LnJlcGxhY2UoL18oW2Etel0pL2csIChfLCBrKSA9PiB7XG5cdFx0XHRyZXR1cm4gay50b1VwcGVyQ2FzZSgpO1xuXHRcdH0pO1xuXG5cdC8vIENvZXJjZSBzdHJpbmcgdmFsdWUgaW50byBKUyB2YWx1ZVxuXHRsZXQgdmFsID0gcHJvY2Vzcy5lbnZba2V5XTtcblx0aWYgKC9eKHllc3xvbnx0cnVlfGVuYWJsZWQpJC9pLnRlc3QodmFsKSkge1xuXHRcdHZhbCA9IHRydWU7XG5cdH0gZWxzZSBpZiAoL14obm98b2ZmfGZhbHNlfGRpc2FibGVkKSQvaS50ZXN0KHZhbCkpIHtcblx0XHR2YWwgPSBmYWxzZTtcblx0fSBlbHNlIGlmICh2YWwgPT09ICdudWxsJykge1xuXHRcdHZhbCA9IG51bGw7XG5cdH0gZWxzZSB7XG5cdFx0dmFsID0gTnVtYmVyKHZhbCk7XG5cdH1cblxuXHRvYmpbcHJvcF0gPSB2YWw7XG5cdHJldHVybiBvYmo7XG59LCB7fSk7XG5cbi8qKlxuICogSXMgc3Rkb3V0IGEgVFRZPyBDb2xvcmVkIG91dHB1dCBpcyBlbmFibGVkIHdoZW4gYHRydWVgLlxuICovXG5cbmZ1bmN0aW9uIHVzZUNvbG9ycygpIHtcblx0cmV0dXJuICdjb2xvcnMnIGluIGV4cG9ydHMuaW5zcGVjdE9wdHMgP1xuXHRcdEJvb2xlYW4oZXhwb3J0cy5pbnNwZWN0T3B0cy5jb2xvcnMpIDpcblx0XHR0dHkuaXNhdHR5KHByb2Nlc3Muc3RkZXJyLmZkKTtcbn1cblxuLyoqXG4gKiBBZGRzIEFOU0kgY29sb3IgZXNjYXBlIGNvZGVzIGlmIGVuYWJsZWQuXG4gKlxuICogQGFwaSBwdWJsaWNcbiAqL1xuXG5mdW5jdGlvbiBmb3JtYXRBcmdzKGFyZ3MpIHtcblx0Y29uc3Qge25hbWVzcGFjZTogbmFtZSwgdXNlQ29sb3JzfSA9IHRoaXM7XG5cblx0aWYgKHVzZUNvbG9ycykge1xuXHRcdGNvbnN0IGMgPSB0aGlzLmNvbG9yO1xuXHRcdGNvbnN0IGNvbG9yQ29kZSA9ICdcXHUwMDFCWzMnICsgKGMgPCA4ID8gYyA6ICc4OzU7JyArIGMpO1xuXHRcdGNvbnN0IHByZWZpeCA9IGAgICR7Y29sb3JDb2RlfTsxbSR7bmFtZX0gXFx1MDAxQlswbWA7XG5cblx0XHRhcmdzWzBdID0gcHJlZml4ICsgYXJnc1swXS5zcGxpdCgnXFxuJykuam9pbignXFxuJyArIHByZWZpeCk7XG5cdFx0YXJncy5wdXNoKGNvbG9yQ29kZSArICdtKycgKyBtb2R1bGUuZXhwb3J0cy5odW1hbml6ZSh0aGlzLmRpZmYpICsgJ1xcdTAwMUJbMG0nKTtcblx0fSBlbHNlIHtcblx0XHRhcmdzWzBdID0gZ2V0RGF0ZSgpICsgbmFtZSArICcgJyArIGFyZ3NbMF07XG5cdH1cbn1cblxuZnVuY3Rpb24gZ2V0RGF0ZSgpIHtcblx0aWYgKGV4cG9ydHMuaW5zcGVjdE9wdHMuaGlkZURhdGUpIHtcblx0XHRyZXR1cm4gJyc7XG5cdH1cblx0cmV0dXJuIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSArICcgJztcbn1cblxuLyoqXG4gKiBJbnZva2VzIGB1dGlsLmZvcm1hdFdpdGhPcHRpb25zKClgIHdpdGggdGhlIHNwZWNpZmllZCBhcmd1bWVudHMgYW5kIHdyaXRlcyB0byBzdGRlcnIuXG4gKi9cblxuZnVuY3Rpb24gbG9nKC4uLmFyZ3MpIHtcblx0cmV0dXJuIHByb2Nlc3Muc3RkZXJyLndyaXRlKHV0aWwuZm9ybWF0V2l0aE9wdGlvbnMoZXhwb3J0cy5pbnNwZWN0T3B0cywgLi4uYXJncykgKyAnXFxuJyk7XG59XG5cbi8qKlxuICogU2F2ZSBgbmFtZXNwYWNlc2AuXG4gKlxuICogQHBhcmFtIHtTdHJpbmd9IG5hbWVzcGFjZXNcbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBzYXZlKG5hbWVzcGFjZXMpIHtcblx0aWYgKG5hbWVzcGFjZXMpIHtcblx0XHRwcm9jZXNzLmVudi5ERUJVRyA9IG5hbWVzcGFjZXM7XG5cdH0gZWxzZSB7XG5cdFx0Ly8gSWYgeW91IHNldCBhIHByb2Nlc3MuZW52IGZpZWxkIHRvIG51bGwgb3IgdW5kZWZpbmVkLCBpdCBnZXRzIGNhc3QgdG8gdGhlXG5cdFx0Ly8gc3RyaW5nICdudWxsJyBvciAndW5kZWZpbmVkJy4gSnVzdCBkZWxldGUgaW5zdGVhZC5cblx0XHRkZWxldGUgcHJvY2Vzcy5lbnYuREVCVUc7XG5cdH1cbn1cblxuLyoqXG4gKiBMb2FkIGBuYW1lc3BhY2VzYC5cbiAqXG4gKiBAcmV0dXJuIHtTdHJpbmd9IHJldHVybnMgdGhlIHByZXZpb3VzbHkgcGVyc2lzdGVkIGRlYnVnIG1vZGVzXG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5mdW5jdGlvbiBsb2FkKCkge1xuXHRyZXR1cm4gcHJvY2Vzcy5lbnYuREVCVUc7XG59XG5cbi8qKlxuICogSW5pdCBsb2dpYyBmb3IgYGRlYnVnYCBpbnN0YW5jZXMuXG4gKlxuICogQ3JlYXRlIGEgbmV3IGBpbnNwZWN0T3B0c2Agb2JqZWN0IGluIGNhc2UgYHVzZUNvbG9yc2AgaXMgc2V0XG4gKiBkaWZmZXJlbnRseSBmb3IgYSBwYXJ0aWN1bGFyIGBkZWJ1Z2AgaW5zdGFuY2UuXG4gKi9cblxuZnVuY3Rpb24gaW5pdChkZWJ1Zykge1xuXHRkZWJ1Zy5pbnNwZWN0T3B0cyA9IHt9O1xuXG5cdGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhleHBvcnRzLmluc3BlY3RPcHRzKTtcblx0Zm9yIChsZXQgaSA9IDA7IGkgPCBrZXlzLmxlbmd0aDsgaSsrKSB7XG5cdFx0ZGVidWcuaW5zcGVjdE9wdHNba2V5c1tpXV0gPSBleHBvcnRzLmluc3BlY3RPcHRzW2tleXNbaV1dO1xuXHR9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9jb21tb24nKShleHBvcnRzKTtcblxuY29uc3Qge2Zvcm1hdHRlcnN9ID0gbW9kdWxlLmV4cG9ydHM7XG5cbi8qKlxuICogTWFwICVvIHRvIGB1dGlsLmluc3BlY3QoKWAsIGFsbCBvbiBhIHNpbmdsZSBsaW5lLlxuICovXG5cbmZvcm1hdHRlcnMubyA9IGZ1bmN0aW9uICh2KSB7XG5cdHRoaXMuaW5zcGVjdE9wdHMuY29sb3JzID0gdGhpcy51c2VDb2xvcnM7XG5cdHJldHVybiB1dGlsLmluc3BlY3QodiwgdGhpcy5pbnNwZWN0T3B0cylcblx0XHQuc3BsaXQoJ1xcbicpXG5cdFx0Lm1hcChzdHIgPT4gc3RyLnRyaW0oKSlcblx0XHQuam9pbignICcpO1xufTtcblxuLyoqXG4gKiBNYXAgJU8gdG8gYHV0aWwuaW5zcGVjdCgpYCwgYWxsb3dpbmcgbXVsdGlwbGUgbGluZXMgaWYgbmVlZGVkLlxuICovXG5cbmZvcm1hdHRlcnMuTyA9IGZ1bmN0aW9uICh2KSB7XG5cdHRoaXMuaW5zcGVjdE9wdHMuY29sb3JzID0gdGhpcy51c2VDb2xvcnM7XG5cdHJldHVybiB1dGlsLmluc3BlY3QodiwgdGhpcy5pbnNwZWN0T3B0cyk7XG59O1xuIl0sIm5hbWVzIjpbInR0eSIsInJlcXVpcmUiLCJ1dGlsIiwiZXhwb3J0cyIsImluaXQiLCJsb2ciLCJmb3JtYXRBcmdzIiwic2F2ZSIsImxvYWQiLCJ1c2VDb2xvcnMiLCJkZXN0cm95IiwiZGVwcmVjYXRlIiwiY29sb3JzIiwic3VwcG9ydHNDb2xvciIsInN0ZGVyciIsImxldmVsIiwiZXJyb3IiLCJpbnNwZWN0T3B0cyIsIk9iamVjdCIsImtleXMiLCJwcm9jZXNzIiwiZW52IiwiZmlsdGVyIiwia2V5IiwidGVzdCIsInJlZHVjZSIsIm9iaiIsInByb3AiLCJzdWJzdHJpbmciLCJ0b0xvd2VyQ2FzZSIsInJlcGxhY2UiLCJfIiwiayIsInRvVXBwZXJDYXNlIiwidmFsIiwiTnVtYmVyIiwiQm9vbGVhbiIsImlzYXR0eSIsImZkIiwiYXJncyIsIm5hbWVzcGFjZSIsIm5hbWUiLCJjIiwiY29sb3IiLCJjb2xvckNvZGUiLCJwcmVmaXgiLCJzcGxpdCIsImpvaW4iLCJwdXNoIiwibW9kdWxlIiwiaHVtYW5pemUiLCJkaWZmIiwiZ2V0RGF0ZSIsImhpZGVEYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwid3JpdGUiLCJmb3JtYXRXaXRoT3B0aW9ucyIsIm5hbWVzcGFjZXMiLCJERUJVRyIsImRlYnVnIiwiaSIsImxlbmd0aCIsImZvcm1hdHRlcnMiLCJvIiwidiIsImluc3BlY3QiLCJtYXAiLCJzdHIiLCJ0cmltIiwiTyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Backoff: () => (/* binding */ Backoff)\n/* harmony export */ });\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */ function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */ Backoff.prototype.duration = function() {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */ Backoff.prototype.reset = function() {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */ Backoff.prototype.setMin = function(min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */ Backoff.prototype.setMax = function(max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */ Backoff.prototype.setJitter = function(jitter) {\n    this.jitter = jitter;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.Fetch),\n/* harmony export */   Manager: () => (/* reexport safe */ _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager),\n/* harmony export */   NodeWebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeWebSocket),\n/* harmony export */   NodeXHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeXHR),\n/* harmony export */   Socket: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket),\n/* harmony export */   WebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebSocket),\n/* harmony export */   WebTransport: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebTransport),\n/* harmony export */   XHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.XHR),\n/* harmony export */   connect: () => (/* binding */ lookup),\n/* harmony export */   \"default\": () => (/* binding */ lookup),\n/* harmony export */   io: () => (/* binding */ lookup),\n/* harmony export */   protocol: () => (/* reexport safe */ socket_io_parser__WEBPACK_IMPORTED_MODULE_4__.protocol)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\");\n/* harmony import */ var _manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./manager.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client\"); // debug()\n/**\n * Managers cache.\n */ const cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n    } else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager: _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager,\n    Socket: _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket,\n    io: lookup,\n    connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */ \n/**\n * Expose constructors for standalone build.\n *\n * @public\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js":
/*!******************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/manager.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Manager: () => (/* binding */ Manager)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contrib/backo2.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__(\"socket.io-client:manager\"); // debug()\nclass Manager extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__.Emitter {\n    constructor(uri, opts){\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__.Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor()\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || socket_io_parser__WEBPACK_IMPORTED_MODULE_2__;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect) this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length) return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined) return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined) return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length) return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */ maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */ open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\")) return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = new engine_io_client__WEBPACK_IMPORTED_MODULE_0__.Socket(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"open\", function() {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err)=>{\n            debug(\"error\");\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            } else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            // set timer\n            const timer = this.setTimeoutFn(()=>{\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */ connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */ onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"ping\", this.onping.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"data\", this.ondata.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", this.onerror.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"close\", this.onclose.bind(this)), // @ts-ignore\n        (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */ onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */ ondata(data) {\n        try {\n            this.decoder.add(data);\n        } catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */ ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.nextTick)(()=>{\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */ onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */ socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new _socket_js__WEBPACK_IMPORTED_MODULE_1__.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        } else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */ _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps){\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */ _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for(let i = 0; i < encodedPackets.length; i++){\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */ cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy)=>subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */ _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */ disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */ onclose(reason, description) {\n        var _a;\n        debug(\"closed due to %s\", reason);\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */ reconnect() {\n        if (this._reconnecting || this.skipReconnect) return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        } else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(()=>{\n                if (self.skipReconnect) return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect) return;\n                self.open((err)=>{\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    } else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */ onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js":
/*!*************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/on.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLEdBQUdDLEdBQUcsRUFBRUMsRUFBRSxFQUFFQyxFQUFFO0lBQzFCRixJQUFJRCxFQUFFLENBQUNFLElBQUlDO0lBQ1gsT0FBTyxTQUFTQztRQUNaSCxJQUFJSSxHQUFHLENBQUNILElBQUlDO0lBQ2hCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aGF0c2FwcC1ib3QtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvb24uanM/ZjQ0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gb24ob2JqLCBldiwgZm4pIHtcbiAgICBvYmoub24oZXYsIGZuKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gc3ViRGVzdHJveSgpIHtcbiAgICAgICAgb2JqLm9mZihldiwgZm4pO1xuICAgIH07XG59XG4iXSwibmFtZXMiOlsib24iLCJvYmoiLCJldiIsImZuIiwic3ViRGVzdHJveSIsIm9mZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/socket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Socket: () => (/* binding */ Socket)\n/* harmony export */ });\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client:socket\"); // debug()\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */ const RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */ class Socket extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__.Emitter {\n    /**\n     * `Socket` constructor.\n     */ constructor(io, nsp, opts){\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */ this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */ this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */ this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */ this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */ this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */ this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */ this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect) this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */ get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */ subEvents() {\n        if (this.subs) return;\n        const io = this.io;\n        this.subs = [\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"open\", this.onopen.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"error\", this.onerror.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"close\", this.onclose.bind(this))\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */ get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */ connect() {\n        if (this.connected) return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState) this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */ open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */ send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */ emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT,\n            data: args\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            debug(\"emitting packet with ack id %d\", id);\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        } else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        } else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */ _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(()=>{\n            delete this.acks[id];\n            for(let i = 0; i < this.sendBuffer.length; i++){\n                if (this.sendBuffer[i].id === id) {\n                    debug(\"removing packet with ack id %d from the buffer\", id);\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            debug(\"event with ack id %d has timed out after %d ms\", id, timeout);\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args)=>{\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */ emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject)=>{\n            const fn = (arg1, arg2)=>{\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */ _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({\n                fromQueue: true\n            }, this.flags)\n        };\n        args.push((err, ...responseArgs)=>{\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    debug(\"packet [%d] is discarded after %d tries\", packet.id, packet.tryCount);\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            } else {\n                debug(\"packet [%d] was successfully sent\", packet.id);\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */ _drainQueue(force = false) {\n        debug(\"draining queue\");\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            debug(\"packet [%d] has already been sent and is waiting for an ack\", packet.id);\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        debug(\"sending packet [%d] (try n\\xb0%d)\", packet.id, packet.tryCount);\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */ packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */ onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data)=>{\n                this._sendConnectPacket(data);\n            });\n        } else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */ _sendConnectPacket(data) {\n        this.packet({\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT,\n            data: this._pid ? Object.assign({\n                pid: this._pid,\n                offset: this._lastOffset\n            }, data) : data\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */ onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */ onclose(reason, description) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */ _clearAcks() {\n        Object.keys(this.acks).forEach((id)=>{\n            const isBuffered = this.sendBuffer.some((packet)=>String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */ onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace) return;\n        switch(packet.type){\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                } else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */ onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        } else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */ ack(id) {\n        const self = this;\n        let sent = false;\n        return function(...args) {\n            // prevent double callbacks\n            if (sent) return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK,\n                id: id,\n                data: args\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */ onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            debug(\"bad ack %s\", packet.id);\n            return;\n        }\n        delete this.acks[packet.id];\n        debug(\"calling ack %s with %j\", packet.id, packet.data);\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */ onconnect(id, pid) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */ emitBuffered() {\n        this.receiveBuffer.forEach((args)=>this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet)=>{\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */ ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */ destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy)=>subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */ disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT\n            });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */ close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */ compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */ get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */ timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */ offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */ offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */ notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js":
/*!**************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   url: () => (/* binding */ url)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_1__(\"socket.io-client:url\"); // debug()\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */ function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || typeof location !== \"undefined\" && location;\n    if (null == uri) uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            } else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            } else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.parse)(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        } else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href = obj.protocol + \"://\" + host + (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUNULENBQUMsVUFBVTtBQUMzQyxNQUFNRSxRQUFRRCxrQ0FBV0EsQ0FBQyx5QkFBeUIsVUFBVTtBQUM3RDs7Ozs7Ozs7Q0FRQyxHQUNNLFNBQVNFLElBQUlDLEdBQUcsRUFBRUMsT0FBTyxFQUFFLEVBQUVDLEdBQUc7SUFDbkMsSUFBSUMsTUFBTUg7SUFDViw2QkFBNkI7SUFDN0JFLE1BQU1BLE9BQVEsT0FBT0UsYUFBYSxlQUFlQTtJQUNqRCxJQUFJLFFBQVFKLEtBQ1JBLE1BQU1FLElBQUlHLFFBQVEsR0FBRyxPQUFPSCxJQUFJSSxJQUFJO0lBQ3hDLHdCQUF3QjtJQUN4QixJQUFJLE9BQU9OLFFBQVEsVUFBVTtRQUN6QixJQUFJLFFBQVFBLElBQUlPLE1BQU0sQ0FBQyxJQUFJO1lBQ3ZCLElBQUksUUFBUVAsSUFBSU8sTUFBTSxDQUFDLElBQUk7Z0JBQ3ZCUCxNQUFNRSxJQUFJRyxRQUFRLEdBQUdMO1lBQ3pCLE9BQ0s7Z0JBQ0RBLE1BQU1FLElBQUlJLElBQUksR0FBR047WUFDckI7UUFDSjtRQUNBLElBQUksQ0FBQyxzQkFBc0JRLElBQUksQ0FBQ1IsTUFBTTtZQUNsQ0YsTUFBTSx3QkFBd0JFO1lBQzlCLElBQUksZ0JBQWdCLE9BQU9FLEtBQUs7Z0JBQzVCRixNQUFNRSxJQUFJRyxRQUFRLEdBQUcsT0FBT0w7WUFDaEMsT0FDSztnQkFDREEsTUFBTSxhQUFhQTtZQUN2QjtRQUNKO1FBQ0EsUUFBUTtRQUNSRixNQUFNLFlBQVlFO1FBQ2xCRyxNQUFNUCx1REFBS0EsQ0FBQ0k7SUFDaEI7SUFDQSw0REFBNEQ7SUFDNUQsSUFBSSxDQUFDRyxJQUFJTSxJQUFJLEVBQUU7UUFDWCxJQUFJLGNBQWNELElBQUksQ0FBQ0wsSUFBSUUsUUFBUSxHQUFHO1lBQ2xDRixJQUFJTSxJQUFJLEdBQUc7UUFDZixPQUNLLElBQUksZUFBZUQsSUFBSSxDQUFDTCxJQUFJRSxRQUFRLEdBQUc7WUFDeENGLElBQUlNLElBQUksR0FBRztRQUNmO0lBQ0o7SUFDQU4sSUFBSUYsSUFBSSxHQUFHRSxJQUFJRixJQUFJLElBQUk7SUFDdkIsTUFBTVMsT0FBT1AsSUFBSUcsSUFBSSxDQUFDSyxPQUFPLENBQUMsU0FBUyxDQUFDO0lBQ3hDLE1BQU1MLE9BQU9JLE9BQU8sTUFBTVAsSUFBSUcsSUFBSSxHQUFHLE1BQU1ILElBQUlHLElBQUk7SUFDbkQsbUJBQW1CO0lBQ25CSCxJQUFJUyxFQUFFLEdBQUdULElBQUlFLFFBQVEsR0FBRyxRQUFRQyxPQUFPLE1BQU1ILElBQUlNLElBQUksR0FBR1I7SUFDeEQsY0FBYztJQUNkRSxJQUFJVSxJQUFJLEdBQ0pWLElBQUlFLFFBQVEsR0FDUixRQUNBQyxPQUNDSixDQUFBQSxPQUFPQSxJQUFJTyxJQUFJLEtBQUtOLElBQUlNLElBQUksR0FBRyxLQUFLLE1BQU1OLElBQUlNLElBQUk7SUFDM0QsT0FBT047QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3doYXRzYXBwLWJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zb2NrZXQuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy91cmwuanM/NzA3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwYXJzZSB9IGZyb20gXCJlbmdpbmUuaW8tY2xpZW50XCI7XG5pbXBvcnQgZGVidWdNb2R1bGUgZnJvbSBcImRlYnVnXCI7IC8vIGRlYnVnKClcbmNvbnN0IGRlYnVnID0gZGVidWdNb2R1bGUoXCJzb2NrZXQuaW8tY2xpZW50OnVybFwiKTsgLy8gZGVidWcoKVxuLyoqXG4gKiBVUkwgcGFyc2VyLlxuICpcbiAqIEBwYXJhbSB1cmkgLSB1cmxcbiAqIEBwYXJhbSBwYXRoIC0gdGhlIHJlcXVlc3QgcGF0aCBvZiB0aGUgY29ubmVjdGlvblxuICogQHBhcmFtIGxvYyAtIEFuIG9iamVjdCBtZWFudCB0byBtaW1pYyB3aW5kb3cubG9jYXRpb24uXG4gKiAgICAgICAgRGVmYXVsdHMgdG8gd2luZG93LmxvY2F0aW9uLlxuICogQHB1YmxpY1xuICovXG5leHBvcnQgZnVuY3Rpb24gdXJsKHVyaSwgcGF0aCA9IFwiXCIsIGxvYykge1xuICAgIGxldCBvYmogPSB1cmk7XG4gICAgLy8gZGVmYXVsdCB0byB3aW5kb3cubG9jYXRpb25cbiAgICBsb2MgPSBsb2MgfHwgKHR5cGVvZiBsb2NhdGlvbiAhPT0gXCJ1bmRlZmluZWRcIiAmJiBsb2NhdGlvbik7XG4gICAgaWYgKG51bGwgPT0gdXJpKVxuICAgICAgICB1cmkgPSBsb2MucHJvdG9jb2wgKyBcIi8vXCIgKyBsb2MuaG9zdDtcbiAgICAvLyByZWxhdGl2ZSBwYXRoIHN1cHBvcnRcbiAgICBpZiAodHlwZW9mIHVyaSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBpZiAoXCIvXCIgPT09IHVyaS5jaGFyQXQoMCkpIHtcbiAgICAgICAgICAgIGlmIChcIi9cIiA9PT0gdXJpLmNoYXJBdCgxKSkge1xuICAgICAgICAgICAgICAgIHVyaSA9IGxvYy5wcm90b2NvbCArIHVyaTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHVyaSA9IGxvYy5ob3N0ICsgdXJpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICghL14oaHR0cHM/fHdzcz8pOlxcL1xcLy8udGVzdCh1cmkpKSB7XG4gICAgICAgICAgICBkZWJ1ZyhcInByb3RvY29sLWxlc3MgdXJsICVzXCIsIHVyaSk7XG4gICAgICAgICAgICBpZiAoXCJ1bmRlZmluZWRcIiAhPT0gdHlwZW9mIGxvYykge1xuICAgICAgICAgICAgICAgIHVyaSA9IGxvYy5wcm90b2NvbCArIFwiLy9cIiArIHVyaTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHVyaSA9IFwiaHR0cHM6Ly9cIiArIHVyaTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyBwYXJzZVxuICAgICAgICBkZWJ1ZyhcInBhcnNlICVzXCIsIHVyaSk7XG4gICAgICAgIG9iaiA9IHBhcnNlKHVyaSk7XG4gICAgfVxuICAgIC8vIG1ha2Ugc3VyZSB3ZSB0cmVhdCBgbG9jYWxob3N0OjgwYCBhbmQgYGxvY2FsaG9zdGAgZXF1YWxseVxuICAgIGlmICghb2JqLnBvcnQpIHtcbiAgICAgICAgaWYgKC9eKGh0dHB8d3MpJC8udGVzdChvYmoucHJvdG9jb2wpKSB7XG4gICAgICAgICAgICBvYmoucG9ydCA9IFwiODBcIjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICgvXihodHRwfHdzKXMkLy50ZXN0KG9iai5wcm90b2NvbCkpIHtcbiAgICAgICAgICAgIG9iai5wb3J0ID0gXCI0NDNcIjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBvYmoucGF0aCA9IG9iai5wYXRoIHx8IFwiL1wiO1xuICAgIGNvbnN0IGlwdjYgPSBvYmouaG9zdC5pbmRleE9mKFwiOlwiKSAhPT0gLTE7XG4gICAgY29uc3QgaG9zdCA9IGlwdjYgPyBcIltcIiArIG9iai5ob3N0ICsgXCJdXCIgOiBvYmouaG9zdDtcbiAgICAvLyBkZWZpbmUgdW5pcXVlIGlkXG4gICAgb2JqLmlkID0gb2JqLnByb3RvY29sICsgXCI6Ly9cIiArIGhvc3QgKyBcIjpcIiArIG9iai5wb3J0ICsgcGF0aDtcbiAgICAvLyBkZWZpbmUgaHJlZlxuICAgIG9iai5ocmVmID1cbiAgICAgICAgb2JqLnByb3RvY29sICtcbiAgICAgICAgICAgIFwiOi8vXCIgK1xuICAgICAgICAgICAgaG9zdCArXG4gICAgICAgICAgICAobG9jICYmIGxvYy5wb3J0ID09PSBvYmoucG9ydCA/IFwiXCIgOiBcIjpcIiArIG9iai5wb3J0KTtcbiAgICByZXR1cm4gb2JqO1xufVxuIl0sIm5hbWVzIjpbInBhcnNlIiwiZGVidWdNb2R1bGUiLCJkZWJ1ZyIsInVybCIsInVyaSIsInBhdGgiLCJsb2MiLCJvYmoiLCJsb2NhdGlvbiIsInByb3RvY29sIiwiaG9zdCIsImNoYXJBdCIsInRlc3QiLCJwb3J0IiwiaXB2NiIsImluZGV4T2YiLCJpZCIsImhyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\n");

/***/ })

};
;