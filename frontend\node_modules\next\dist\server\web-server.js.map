{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["NextWebServer", "BaseServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "dynamicRouteMatcher", "getRouteMatcher", "defaultRouteMatches", "paramsResult", "normalizeDynamicRouteParams", "normalizedParams", "hasValidParams", "params", "interpolateDynamicPath", "normalizeVercelUrl", "Object", "keys", "routeKeys", "removeTrailingSlash", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "isAPIRoute", "render", "err", "NoFallbackError", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "IncrementalCache", "requestProtocol", "pagesDir", "enabledDirectories", "pages", "appDir", "app", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "ppr", "getResponseCache", "WebResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "addRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "byteLength", "generateEtags", "generateETag", "body", "send", "findPageComponents", "url", "_url", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": ";;;;+BAgDA;;;eAAqBA;;;qBAjCM;oEACiB;sBACf;6BACE;6DACF;4BACF;qCACS;uBACL;6BAKxB;4BAC4B;8BACH;kCACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB,MAAMA,sBAAsBC,mBAAU;IACnDC,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aAgGEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIG,IAAAA,qBAAc,EAACN,WAAW;oBAC5B,MAAMO,aAAaC,IAAAA,8BAAkB,EAACR,UAAU;oBAChD,MAAMS,sBAAsBC,IAAAA,6BAAe,EAACH;oBAC5C,MAAMI,sBAAsBF,oBAC1BT;oBAEF,MAAMY,eAAeC,IAAAA,wCAA2B,EAC9CZ,OACA,OACAM,YACAI;oBAEF,MAAMG,mBAAmBF,aAAaG,cAAc,GAChDH,aAAaI,MAAM,GACnBf;oBAEJD,WAAWiB,IAAAA,mCAAsB,EAC/BjB,UACAc,kBACAP;oBAEFW,IAAAA,+BAAkB,EAChBrB,KACA,MACAsB,OAAOC,IAAI,CAACb,WAAWc,SAAS,GAChC,MACAd;gBAEJ;YACF;YAEA,wDAAwD;YACxDP,WAAWsB,IAAAA,wCAAmB,EAACtB;YAE/B,IAAI,IAAI,CAACuB,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACzB;gBAC3D,IAAIwB,gBAAgB;oBAClBzB,UAAUE,KAAK,CAACyB,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAAC1B,MAAM2B,qBAAqB;YAEtD,IAAIC,IAAAA,sBAAU,EAAC7B,WAAW;gBACxB,OAAOC,MAAM2B,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACE,MAAM,CAACjC,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOgC,KAAK;gBACZ,IAAIA,eAAeC,2BAAe,IAAIL,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMI;YACR;QACF;QAxKE,uBAAuB;QACvBZ,OAAOc,MAAM,CAAC,IAAI,CAACC,UAAU,EAAEvC,QAAQU,eAAe,CAAC8B,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIC,kCAAgB,CAAC;YAC1BD;YACAD;YACAG,iBAAiB;YACjBC,UAAU,IAAI,CAACC,kBAAkB,CAACC,KAAK;YACvCC,QAAQ,IAAI,CAACF,kBAAkB,CAACG,GAAG;YACnCC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAACnD,aAAa,CAACC,eAAe,CAACmD,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrD,4CAA4C;YAC5CT,cAAc;gBAAEU,KAAK;YAAM;QAC7B;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIC,aAAgB,CAAC,IAAI,CAACX,WAAW;IAC9C;IAEA,MAAgBY,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAAC1D,aAAa,CAACC,eAAe,CAACyD,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAAC3D,aAAa,CAACC,eAAe,CAAC8B,gBAAgB,CAAC6B,OAAO;IACpE;IAEUC,wBAAwB;QAChC,OAAO;YACLpB,KAAK,IAAI,CAACzC,aAAa,CAACC,eAAe,CAAC6D,SAAS,KAAK;YACtDvB,OAAO,IAAI,CAACvC,aAAa,CAACC,eAAe,CAAC6D,SAAS,KAAK;QAC1D;IACF;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAAC/D,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACyD,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAAC1D,aAAa,CAACC,eAAe,CAACyD,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAAC1D,aAAa,CAACC,eAAe,CAACyD,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACRxE,GAAmB,EACnBE,SAAiC,EACjC;QACAuE,IAAAA,2BAAc,EAACzE,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUwD,uBAAuB;YAE3B;QADJ,MAAM,EAAEc,iBAAiB,EAAE,GAAG,IAAI,CAACnE,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAAC6B,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAACiC,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAAC1E,aAAa,CAACC,eAAe,CAAC8B,gBAAgB,CAAC4C,gBAAgB;IAC7E;IA8EUC,WACRnF,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBiC,UAA4B,EACL;QACvB,MAAM,EAAE+C,YAAY,EAAE,GAAG,IAAI,CAAC7E,aAAa,CAACC,eAAe;QAC3D,IAAI,CAAC4E,cAAc;YACjB,MAAM,IAAI/E,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAckC,CAAAA,WAAWI,GAAG,GAAG,eAAe,aAAY,GAAI;YAChEtC,WAAW;QACb;QACA,OAAOiF,aACLpF,KACAC,KACAE,UACAC,OACAkB,OAAOc,MAAM,CAACC,YAAY;YACxBgD,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpBvF,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIwF,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAI3F,QAAQ4F,eAAe,IAAI5F,QAAQ6F,IAAI,KAAK,QAAQ;YACtD1F,IAAIwF,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAACxF,IAAI2F,SAAS,CAAC,iBAAiB;YAClC3F,IAAIwF,SAAS,CACX,gBACA3F,QAAQ+F,MAAM,CAACC,WAAW,GACtBhG,QAAQ+F,MAAM,CAACC,WAAW,GAC1BhG,QAAQ6F,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAIjG,QAAQ+F,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAUjG,QAAQ+F,MAAM,CAACI,MAAM,CAAChG,IAAIiG,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAUtG,QAAQ+F,MAAM,CAACQ,iBAAiB;YAChDpG,IAAIwF,SAAS,CAAC,kBAAkBa,OAAOC,IAAAA,eAAU,EAACH;YAClD,IAAItG,QAAQ0G,aAAa,EAAE;gBACzBvG,IAAIwF,SAAS,CAAC,QAAQgB,IAAAA,kBAAY,EAACL;YACrC;YACAnG,IAAIyG,IAAI,CAACN;QACX;QAEAnG,IAAI0G,IAAI;QAER,gDAAgD;QAChD,IAAIZ,SAAS,MAAMA;IACrB;IAEA,MAAgBa,mBAAmB,EACjC3C,IAAI,EACJ7D,KAAK,EACLe,MAAM,EACN0F,KAAKC,IAAI,EAOV,EAAE;QACD,MAAMjB,SAAS,MAAM,IAAI,CAACtF,aAAa,CAACC,eAAe,CAACuG,aAAa,CAAC9C;QACtE,IAAI,CAAC4B,QAAQ,OAAO;QAEpB,OAAO;YACLzF,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIe,UAAU,CAAC,CAAC;YAClB;YACA6F,YAAYnB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBoB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,2BACdb,IAAa,EAC6B;QAC1C,wEAAwE;QACxE,OAAO;IACT;IAEUc,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}